<?php

namespace App\Console\Commands;

use App\Models\TitleServiceVideo;
use App\Models\Video;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;

class CalculateVideosSize extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'calculate:videos {disk=uploads} {--count} {--size}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        $videos = TitleServiceVideo::where('video_type', 1)->get();
        dump($videos->count());
        $totalSize = 0;
        $totalFileCount = 0;
        if ($this->option('size')) {
            foreach ($videos as $video) {
                $totalSize += $this->calculateSize($video->video_file);
                $totalSize += $this->calculateSize($video->thumbnail);
            }
        }


        if ($this->option('size')) {
            dump("size: " . ($totalSize / 1048576) . "MB");
        }
        if ($this->option('count')) {
            $totalFileCount = count(Storage::disk($this->argument('disk'))->allFiles());
            dump("file count: " . $totalFileCount);
        }

        return Command::SUCCESS;
    }
    protected function calculateSize($file_path): int
    {
        if ($file_path == null || !Storage::disk($this->argument('disk'))->exists($file_path)) {
            return 0;
        }
        $size = Storage::disk($this->argument('disk'))
            ->size($file_path);
        return $size;
    }
    protected function countFile($file_path): int
    {
        if (($file_path == null) || (!Storage::disk($this->argument('disk'))->exists($file_path))) {
            return 0;
        }
        return 1;
    }
}

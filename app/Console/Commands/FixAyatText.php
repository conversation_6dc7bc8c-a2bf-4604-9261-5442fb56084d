<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixAyatText extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:ayat ';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->fixAyat();

        // $this->info($this->option("table"));
    }

    public function fixAyat()
    {

        $tables = [
            "aljadwal", "baghawy", "ghare<PERSON>_ibn_qutaybah", "ibn_alqayyem", "ibn_katheer",
            "iraab_aldarweesh", "iraab_alnahas", "nathm_aldurar", "qiraat_almawsoah", "qortoby",
            "saady", "siraaj_ghareeb", "tabary", "tahbeer_altayseer", "tanweer", "zad_almaseer"
        ];


        foreach ($tables as $table) {

            $data = DB::table($table)->whereRaw("text REGEXP '^[0-9]+$'")->get();



            foreach ($data as $key => $value) {
                DB::table($table)->where('id', $value->id)->update(['text' => DB::table($table)->where('verse_id', $value->text)->first()->text]);
            }

            $this->info("$table done");
        }




        // foreach ($data as $key => $value) {
        //     // dd(quran()->replaceUnicodeWithText($value->text));
        //     if ($value->text != null) {
        //         DB::table($table)->where('id', $value->id)->update(['text' =>  quran()->replaceUnicodeWithText($value->text)]);
        //     }

        //     $this->info($value->id);
        // }

        // $this->
        $this->info("$table done");
    }
}

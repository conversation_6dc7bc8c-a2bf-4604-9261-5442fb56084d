<?php

namespace App\Console\Commands;

use App\Models\Content;
use App\Models\TitleService;
use App\Models\TitleServiceActivity;
use App\Models\TitleServiceCalender;
use App\Models\TitleServiceQuotation;
use App\Models\TitleServiceReference;
use App\Models\TitleServiceText;
use App\Models\TitleServiceVideo;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class GenerateSearchableText extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'searchable-text:generate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';



    public   $tafseers = [
        "ibn_katheer",
        "baghawy",
        "saady",
        "qortoby",
        "tabary",
        "tanweer",
        "zad_almaseer",
        "ibn_alqayyem",
        'ghareeb_ibn_qutaybah',
        'aljadwal',
        'iraab_aldarweesh',
        'ira<PERSON>_alnahas',
        'nathm_aldurar',
        'qiraat_almawsoah',
        'siraaj_ghareeb',
        'tahbeer_altayseer',
        // 'tadabor'
    ];

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->content();
        $this->titleServices();
        $this->tafseer();
        return Command::SUCCESS;
    }


    public function content()
    {
        $this->info('content.........');
        foreach (config('panels.panels') as $id => $panel) {

            $page = 1;
            while (true) {
                $contents = Content::on($panel['database_connection'])
                    ->paginate(perPage: 50, page: $page);
                $data = [];
                foreach ($contents as $content) {
                    $_data = $content->toArray();
                    unset($_data['model_type']);
                    unset($_data['created_at']);
                    unset($_data['updated_at']);
                    $data[] = [
                        ...$_data, "id" => $content->id, "searchable_text" => get_searchable_text($content->text)
                    ];
                }

                // dd($data);
                Content::on($panel['database_connection'])->upsert($data, uniqueBy: ['id'], update: ['searchable_text']);


                $this->info("Done  content page {$page} .................");
                if (!$contents->hasMorePages()) {
                    break;
                }
                $page++;
            }
        }

        $this->info("Done  content.................");
    }




    public function titleServices()
    {
        $this->titleServiceVideo();
        $this->titleServiceText();
        $this->titleServiceQuotation();
        $this->titleServiceReference();
        $this->titleServiceActivity();
        $this->titleServiceCalender();
    }



    public function titleServiceVideo()
    {
        $this->info('TitleServiceVideo .........');

        foreach (config('panels.panels') as $id => $panel) {

            $page = 1;
            while (true) {
                $contents = TitleServiceVideo::on($panel['database_connection'])
                    ->paginate(perPage: 50, page: $page);

                $data = [];
                foreach ($contents as $content) {
                    $_data = $content->toArray();
                    unset($_data['model_type']);
                    unset($_data['created_at']);
                    unset($_data['updated_at']);

                    $data[] = [
                        ...$_data,
                        "id" => $content->id,
                        "searchable_text" =>  get_searchable_text($content->text)
                    ];
                }

                // dd($data);
                TitleServiceVideo::on($panel['database_connection'])->upsert($data, uniqueBy: ['id'], update: ['searchable_text']);

                if (!$contents->hasMorePages()) {
                    break;
                }
                $this->info("Done  TitleServiceVideo page {$page}.................");

                $page++;
            }
        }

        $this->info("Done  TitleServiceVideo.................");
    }


    public function titleServiceText()
    {
        $this->info('TitleServiceText .........');

        foreach (config('panels.panels') as $id => $panel) {

            $page = 1;

            while (true) {
                $contents = TitleServiceText::on($panel['database_connection'])
                    ->paginate(perPage: 50, page: $page);

                $data = [];
                foreach ($contents as $content) {
                    $_data = $content->toArray();
                    unset($_data['model_type']);
                    unset($_data['created_at']);
                    unset($_data['updated_at']);

                    $data[] = [
                        ...$_data, "id" => $content->id, "searchable_text" =>  get_searchable_text($content->text)
                    ];
                }

                // dd($data);
                TitleServiceText::on($panel['database_connection'])->upsert($data, uniqueBy: ['id'], update: ['searchable_text']);

                if (!$contents->hasMorePages()) {
                    break;
                }
                $this->info("Done  TitleServiceText page {$page}.................");

                $page++;
            }
        }

        $this->info("Done  TitleServiceText.................");
    }


    public function titleServiceQuotation()
    {
        $this->info('TitleServiceQuotation .........');

        foreach (config('panels.panels') as $id => $panel) {

            $page = 1;
            while (true) {
                $contents = TitleServiceQuotation::on($panel['database_connection'])

                    ->paginate(perPage: 50, page: $page);

                $data = [];
                foreach ($contents as $content) {
                    $_data = $content->toArray();
                    unset($_data['model_type']);
                    unset($_data['created_at']);
                    unset($_data['updated_at']);

                    $data[] = [
                        ...$_data, "id" => $content->id,
                        "searchable_text" => get_searchable_text($content->name).' '. get_searchable_text($content->text),
                    ];
                }

                // dd($data);
                TitleServiceQuotation::on($panel['database_connection'])->upsert($data, uniqueBy: ['id'], update: ['searchable_text']);

                if (!$contents->hasMorePages()) {
                    break;
                }
                $this->info("Done  TitleServiceQuotation page {$page}.................");

                $page++;
            }
        }

        $this->info("Done  TitleServiceQuotation.................");
    }


    public function titleServiceReference()
    {
        $this->info('TitleServiceReference .........');

        foreach (config('panels.panels') as $id => $panel) {

            $page = 1;
            while (true) {
                $contents = TitleServiceReference::on($panel['database_connection'])

                    ->paginate(perPage: 50, page: $page);

                $data = [];
                foreach ($contents as $content) {
                    $_data = $content->toArray();
                    unset($_data['model_type']);
                    unset($_data['created_at']);
                    unset($_data['updated_at']);

                    $data[] = [
                        ...$_data, "id" => $content->id,
                        "searchable_text" =>  get_searchable_text($content->name) . ' ' . get_searchable_text($content->description)
                    ];
                }

                // dd($data);
                TitleServiceReference::on($panel['database_connection'])->upsert($data, uniqueBy: ['id'], update: ['searchable_text']);

                if (!$contents->hasMorePages()) {
                    break;
                }
                $this->info("Done  TitleServiceReference page {$page}.................");

                $page++;
            }
        }

        $this->info("Done  TitleServiceReference.................");
    }


    public function titleServiceActivity()
    {
        $this->info('TitleServiceActivity .........');

        foreach (config('panels.panels') as $id => $panel) {

            $page = 1;
            while (true) {
                $contents = TitleServiceActivity::on($panel['database_connection'])

                    ->paginate(perPage: 50, page: $page);

                $data = [];
                foreach ($contents as $content) {
                    $_data = $content->toArray();
                    unset($_data['model_type']);
                    unset($_data['created_at']);
                    unset($_data['updated_at']);

                    $data[] = [
                        ...$_data, "id" => $content->id,
                        "searchable_text" =>
                        get_searchable_text($content->goal)
                            . ' ' . get_searchable_text($content->answer)
                            . ' ' . get_searchable_text($content->needed)

                    ];
                }

                // dd($data);
                TitleServiceActivity::on($panel['database_connection'])->upsert($data, uniqueBy: ['id'], update: ['searchable_text']);

                if (!$contents->hasMorePages()) {
                    break;
                }
                $this->info("Done  TitleServiceActivity page {$page}.................");

                $page++;
            }
        }

        $this->info("Done  TitleServiceActivity.................");
    }


    public function titleServiceCalender()
    {
        $this->info('TitleServiceCalender .........');

        foreach (config('panels.panels') as $id => $panel) {

            $page = 1;
            while (true) {
                $contents = TitleServiceCalender::on($panel['database_connection'])

                    ->paginate(perPage: 50, page: $page);

                $data = [];
                foreach ($contents as $content) {
                    $_data = $content->toArray();
                    unset($_data['model_type']);
                    unset($_data['created_at']);
                    unset($_data['updated_at']);

                    $data[] = [
                        ...$_data, "id" => $content->id, "searchable_text" => get_searchable_text($content->goal)
                            .' '. get_searchable_text($content->feedback_answers) ." ". get_searchable_text($content->tools_questions),
                    ];
                }

                // dd($data);
                TitleServiceCalender::on($panel['database_connection'])->upsert($data, uniqueBy: ['id'], update: ['searchable_text']);

                if (!$contents->hasMorePages()) {
                    break;
                }
                $this->info("Done  TitleServiceCalender page {$page}.................");

                $page++;
            }
        }

        $this->info("Done  TitleServiceCalender.................");
    }



    public function tafseer()
    {
        foreach ($this->tafseers as $tafseer) {

            $this->info("tafseer {$tafseer} .........");

            $page = 1;

            while (true) {
                $contents = DB::table($tafseer)
                    ->paginate(perPage: 50, page: $page);

                $data = [];

                foreach ($contents as $content) {
                    $_data = (array) $content;
                    unset($_data['model_type']);
                    unset($_data['created_at']);
                    unset($_data['updated_at']);

                    $data[] = [
                        ...$_data, "id" => $content->id, "searchable_text" =>  get_searchable_text($content->text)
                    ];
                }

                // dd($data);
                DB::table($tafseer)->upsert($data, uniqueBy: ['id'], update: ['searchable_text']);

                if (!$contents->hasMorePages()) {
                    break;
                }
                $this->info("Done  tafseer {$tafseer} page {$page}.................");

                $page++;
            }


            $this->info("Done  tafseer {$tafseer}.................");
        }
    }
}

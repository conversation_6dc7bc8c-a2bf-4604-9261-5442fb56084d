<?php

namespace App\Console\Commands;

use App\Models\Content;
use App\Models\Tadabor;
use App\Models\TitleService;
use App\Models\TitleServiceActivity;
use App\Models\TitleServiceCalender;
use App\Models\TitleServiceQuotation;
use App\Models\TitleServiceReference;
use App\Models\TitleServiceText;
use App\Models\TitleServiceVideo;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class GenerateSearchableTextForTadabor extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'searchable-tadabor:generate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';


    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->content();
        return Command::SUCCESS;
    }


    public function content()
    {
        $this->info('Tadabor ..................');
        Tadabor::all()->each(function ($tadabor) {
            $tadabor->searchable_text = get_searchable_text($tadabor->text);
            $tadabor->save();
        });

    }
}

<?php

namespace App\Console\Commands;

use App\Models\TitleService;
use App\Models\TitleServiceActivity;
use App\Models\TitleServiceQuotation;
use App\Models\TitleServiceReference;
use App\Models\TitleServiceText;
use App\Models\TitleServiceVideo;
use Illuminate\Console\Command;

class MigrateActivityService extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:activity';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $services = TitleService::where('view_type', 5)->get();
        dump($services->count());
        foreach ($services as $service) {
            $sort = 0;
            foreach ($service->data??[] as $data) {
                $sort++;
                dump($sort);
                /*
                'service_id',
        'goal',
        'style',
        'imp_type',
        'sort',
        'answer',
        'needed',
        'type',
         */
                TitleServiceActivity::updateOrCreate([
                    'service_id' => $service->id,
                    'style' => $data['style'],
                    'goal' => $data['goal'],
                    'imp_type' => $data['imp_type'],
                    'answer' => $data['answer'],
                    'needed' => $data['needed'],
                    'type' => $data['type'],
                    'sort' => $sort,
                ],[
                ]);
            }
        }
        return Command::SUCCESS;
    }
}

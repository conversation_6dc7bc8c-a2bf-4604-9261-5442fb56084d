<?php

namespace App\Console\Commands;

use App\Models\TitleServiceVideo;
use App\Models\Video;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;

class MigrateContentVideosToS3 extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:contentVideosToS3 {connection=mysql}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        $videos = TitleServiceVideo::on($this->argument('connection'))->where('video_type', 1)->get();
        dump($videos->count());
        $i = 1;
        foreach ($videos as $video) {
            dump("$i / " . $videos->count(),);
            $i++;
            if (!$video->video_file) continue;
            $this->migrate($video->video_file);
            if (!$video->thumbnail) continue;
            $this->migrate($video->thumbnail);
        }
        return Command::SUCCESS;
    }
    protected function migrate($file_path)
    {
        if (Cache::get($file_path)) {
            dump($file_path, ' migrating with another process');
            return;
        }
        if (!Storage::disk('uploads')->exists($file_path)) {
            dump($file_path, ' not exists');
            return;
        }
        if (Storage::disk('do')->exists($file_path)) {
            dump($file_path, ' already exists');
            return;
        }
        dump("migrating ", $file_path);
        Cache::put($file_path, true, 5 * 60);
        $file = Storage::disk('uploads')
            ->get($file_path);
        Storage::disk('do')->put($file_path, $file, 'public');
        Cache::forget($file_path);
    }
}

<?php

namespace App\Console\Commands;

use App\Models\TitleService;
use App\Models\TitleServiceQuotation;
use App\Models\TitleServiceText;
use App\Models\TitleServiceVideo;
use Illuminate\Console\Command;

class MigrateQoutationsService extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:qoutations';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $services = TitleService::where('view_type', 3)->get();
        dump($services->count());
        foreach ($services as $service) {
            $sort = 0;
            foreach ($service->data as $data) {
                $sort++;
                dump($sort);
                TitleServiceQuotation::updateOrCreate([
                    'service_id' => $service->id,
                    'name' => $data['name'],
                    'text' => $data['text'],
                    'sort' => $sort,
                ],[]);
            }
        }
        return Command::SUCCESS;
    }
}

<?php

namespace App\Console\Commands;

use App\Models\TitleService;
use App\Models\TitleServiceQuotation;
use App\Models\TitleServiceReference;
use App\Models\TitleServiceText;
use App\Models\TitleServiceVideo;
use Illuminate\Console\Command;

class MigrateReferenceService extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:references';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $services = TitleService::where('view_type', 4)->get();
        dump($services->count());
        foreach ($services as $service) {
            $sort = 0;
            foreach ($service->data as $data) {
                $sort++;
                dump($sort);
                TitleServiceReference::updateOrCreate([
                    'service_id' => $service->id,
                    'name' => $data['name'],
                    'description' => $data['description'],
                    'file' => $data['file'],
                    'sort' => $sort,
                ],[]);
            }
        }
        return Command::SUCCESS;
    }
}

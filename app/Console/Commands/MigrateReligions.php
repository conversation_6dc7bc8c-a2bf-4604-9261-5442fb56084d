<?php

namespace App\Console\Commands;

use App\Models\Book;
use App\Models\Content;
use App\Models\Title;
use App\Models\TitleService;
use App\Models\TitleServiceActivity;
use App\Models\TitleServiceCalender;
use App\Models\TitleServiceQuotation;
use App\Models\TitleServiceReference;
use App\Models\TitleServiceText;
use App\Models\TitleServiceVideo;
use Illuminate\Console\Command;

class MigrateReligions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:religions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $books_ids = $this->insertBooks();
        $titles_ids = $this->insertTitles($books_ids);
        $contents_ids = $this->insertContents($titles_ids);
        $title_services_ids = $this->insertTitleServices($titles_ids);
        $title_service_texts_ids = $this->insertTitleServiceTexts($title_services_ids);
        $title_service_calenders_ids = $this->insertTitleServiceCalenders($title_services_ids);
        $title_service_references_ids = $this->insertTitleServiceReferences($title_services_ids);
        $title_service_quotations_ids = $this->insertTitleServiceQuotations($title_services_ids);
        $title_service_videos_ids = $this->insertTitleServiceVideos($title_services_ids);
        $title_service_activities_ids = $this->insertTitleServiceActivities($title_services_ids);

        // dd($books->toArray());
        // foreach ($books as $book) {
        //     $book_data = $book->toArray();
        //     unset($book_data['files']);
        //     $new_book = Book::on('religions')->updateOrCreate(["id" => $book_data['id']], $book_data);
        //     // $titles = $book->titles;
        //     // foreach ($titles as $title) {
        //     //     $title_data  = $title->toArray();
        //     //     unset($title_data['parent']);
        //     //     $new_title = Title::on('religions')->updateOrCreate(["id" => $title_data['id']], $title_data);
        //     //     $content_data = $title->content?->toArray();
        //     //     unset($content_data['model_type']);
        //     //     if ($content_data != null) {
        //     //         Content::on('religions')->updateOrCreate(["id" => $content_data['id']], $content_data);
        //     //     }
        //     //     $title_services = $title->services;
        //     //     if ($title_services == null) {

        //     //         foreach ($title_services as $title_service) {
        //     //             $title_service_data = $title_service->toArray();
        //     //             // dd($title_service_data);
        //     //             $new_title_service = TitleService::on('religions')->updateOrCreate(["id" => $title_service_data['id']], $title_service_data);
        //     //            $text =  $title_service->text;
        //     //             if ($text != null) {
        //     //                 $text_data = $text->toArray();
        //     //                 unset($text_data['model_type']);
        //     //                 Content::on('religions')->updateOrCreate(["id" => $text_data['id']], $text_data);
        //     //             }
        //     //         }
        //     //     }
        //     // }
        // }
    }
    function insertBooks(): array
    {
        $this->info('insert books');
        // empty books
        Book::on('religions')->delete();
        $books = Book::on('mysql')->whereNotIn('type', [6,0])->get();
        // unset files from all books
        $books_data = $books->toArray();
        foreach ($books_data as $key => $book) {
            unset($books_data[$key]['files']);
            $books_data[$key]['created_at'] = date('Y-m-d H:i:s', strtotime($book['created_at']));
            $books_data[$key]['updated_at'] = date('Y-m-d H:i:s', strtotime($book['updated_at']));
        }
        Book::on('religions')->insert($books_data);
        $this->info('books inserted');
        return $books->pluck('id')->toArray();
    }
    function insertTitles($books_ids): array
    {
        $this->info('insert titles');
        // empty titles
        Title::on('religions')->delete();
        $title_ids = Title::on('mysql')->whereIn('book_id', $books_ids)->pluck('id')->toArray();
        $titles = Title::on('mysql')->whereIn('id', $title_ids)->get();
        $titles_data = $titles->toArray();

        foreach ($titles_data as $key => $title) {
            unset($titles_data[$key]['parent']);
            $titles_data[$key]['created_at'] = date('Y-m-d H:i:s', strtotime($title['created_at']));
            $titles_data[$key]['updated_at'] = date('Y-m-d H:i:s', strtotime($title['updated_at']));
        }
        Title::on('religions')->insert($titles_data);
        $this->info('titles inserted');
        return $titles->pluck('id')->toArray();
    }

    function insertContents($titles_ids): array
    {
        $this->info('insert contents');
        // force delete contents
        Content::on('religions')->forceDelete();
        $content_ids = Content::on('mysql')->whereIn('title_id', $titles_ids)->pluck('id')->toArray();
        $contents = Content::on('mysql')->whereIn('id', $content_ids)->get();
        $contents_data = $contents->toArray();
        foreach ($contents_data as $key => $content) {
            unset($contents_data[$key]['model_type']);
            $contents_data[$key]['created_at'] = null;
            $contents_data[$key]['updated_at'] = null;

        }
        Content::on('religions')->insert($contents_data);
        $this->info('contents inserted');
        return $contents->pluck('id')->toArray();
    }

    // insert title services

    function insertTitleServices($titles_ids): array
    {
        $this->info('insert title services');
        // empty title services
        TitleService::on('religions')->delete();
        $title_service_ids = TitleService::on('mysql')->whereIn('title_id', $titles_ids)->pluck('id')->toArray();
        $title_services = TitleService::on('mysql')->whereIn('id', $title_service_ids)->get();
        $title_services_data = $title_services->toArray();
        // dd($title_services_data);

        foreach ($title_services_data as $key => $title_service) {
            unset($title_services_data[$key]['model_type']);
            unset($title_services_data[$key]['data']);
            $title_services_data[$key]['created_at'] = date('Y-m-d H:i:s', strtotime($title_service['created_at']));
            $title_services_data[$key]['updated_at'] = date('Y-m-d H:i:s', strtotime($title_service['updated_at']));
        }
        TitleService::on('religions')->insert($title_services_data);
        $this->info('title services inserted');
        return $title_services->pluck('id')->toArray();
    }

    // insert title service text
    function insertTitleServiceTexts($title_service_ids): array
    {
        $this->info('insert title service text');
        // empty title service text
        TitleServiceText::on('religions')->delete();
        $title_service_text_ids = TitleServiceText::on('mysql')->whereIn('service_id', $title_service_ids)->pluck('id')->toArray();
        $title_service_texts = TitleServiceText::on('mysql')->whereIn('id', $title_service_text_ids)->get();
        $title_service_texts_data = $title_service_texts->toArray();
        foreach ($title_service_texts_data as $key => $title_service_text) {
            unset($title_service_texts_data[$key]['model_type']);
            $title_service_texts_data[$key]['created_at'] = date('Y-m-d H:i:s', strtotime($title_service_text['created_at']));
            $title_service_texts_data[$key]['updated_at'] = date('Y-m-d H:i:s', strtotime($title_service_text['updated_at']));
        }
        TitleServiceText::on('religions')->insert($title_service_texts_data);
        $this->info('title service text inserted');
        return $title_service_texts->pluck('id')->toArray();
    }
    // insert title service activities
    function insertTitleServiceActivities($title_service_ids): array
    {
        $this->info('insert title service activities');
        // empty title service activities
        TitleServiceActivity::on('religions')->delete();
        $title_service_activity_ids = TitleServiceActivity::on('mysql')->whereIn('service_id', $title_service_ids)->pluck('id')->toArray();
        $title_service_activities = TitleServiceActivity::on('mysql')->whereIn('id', $title_service_activity_ids)->get();
        $title_service_activities_data = $title_service_activities->toArray();
        foreach ($title_service_activities_data as $key => $title_service_activity) {
            unset($title_service_activities_data[$key]['model_type']);
            $title_service_activities_data[$key]['created_at'] = date('Y-m-d H:i:s', strtotime($title_service_activity['created_at']));
            $title_service_activities_data[$key]['updated_at'] = date('Y-m-d H:i:s', strtotime($title_service_activity['updated_at']));
        }
        TitleServiceActivity::on('religions')->insert($title_service_activities_data);
        $this->info('title service activities inserted');
        return $title_service_activities->pluck('id')->toArray();
    }

    // insert title service Calenders
    function insertTitleServiceCalenders($title_service_ids): array
    {
        $this->info('insert title service calenders');
        // empty title service calenders
        TitleServiceCalender::on('religions')->delete();
        $title_service_calender_ids = TitleServiceCalender::on('mysql')->whereIn('service_id', $title_service_ids)->pluck('id')->toArray();
        $title_service_calenders = TitleServiceCalender::on('mysql')->whereIn('id', $title_service_calender_ids)->get();
        $title_service_calenders_data = $title_service_calenders->toArray();
        foreach ($title_service_calenders_data as $key => $title_service_calender) {
            unset($title_service_calenders_data[$key]['model_type']);
            $title_service_calenders_data[$key]['created_at'] = date('Y-m-d H:i:s', strtotime($title_service_calender['created_at']));
            $title_service_calenders_data[$key]['updated_at'] = date('Y-m-d H:i:s', strtotime($title_service_calender['updated_at']));
        }
        TitleServiceCalender::on('religions')->insert($title_service_calenders_data);
        $this->info('title service calenders inserted');
        return $title_service_calenders->pluck('id')->toArray();
    }

    // insert title service videos
    function insertTitleServiceVideos($title_service_ids): array
    {
        $this->info('insert title service videos');
        // empty title service videos
        TitleServiceVideo::on('religions')->delete();
        $title_service_video_ids = TitleServiceVideo::on('mysql')->whereIn('service_id', $title_service_ids)->pluck('id')->toArray();
        $title_service_videos = TitleServiceVideo::on('mysql')->whereIn('id', $title_service_video_ids)->get();
        $title_service_videos_data = $title_service_videos->toArray();
        foreach ($title_service_videos_data as $key => $title_service_video) {
            unset($title_service_videos_data[$key]['model_type']);
            $title_service_videos_data[$key]['created_at'] = date('Y-m-d H:i:s', strtotime($title_service_video['created_at']));
            $title_service_videos_data[$key]['updated_at'] = date('Y-m-d H:i:s', strtotime($title_service_video['updated_at']));
        }
        TitleServiceVideo::on('religions')->insert($title_service_videos_data);
        $this->info('title service videos inserted');
        return $title_service_videos->pluck('id')->toArray();
    }

    // insert title service quotations
    function insertTitleServiceQuotations($title_service_ids): array
    {
        $this->info('insert title service quotations');
        // empty title service quotations
        TitleServiceQuotation::on('religions')->delete();
        $title_service_quotation_ids = TitleServiceQuotation::on('mysql')->whereIn('service_id', $title_service_ids)->pluck('id')->toArray();
        $title_service_quotations = TitleServiceQuotation::on('mysql')->whereIn('id', $title_service_quotation_ids)->get();
        $title_service_quotations_data = $title_service_quotations->toArray();
        foreach ($title_service_quotations_data as $key => $title_service_quotation) {
            unset($title_service_quotations_data[$key]['model_type']);
            $title_service_quotations_data[$key]['created_at'] = date('Y-m-d H:i:s', strtotime($title_service_quotation['created_at']));
            $title_service_quotations_data[$key]['updated_at'] = date('Y-m-d H:i:s', strtotime($title_service_quotation['updated_at']));
        }
        TitleServiceQuotation::on('religions')->insert($title_service_quotations_data);
        $this->info('title service quotations inserted');
        return $title_service_quotations->pluck('id')->toArray();
    }

    // insert title service references
    function insertTitleServiceReferences($title_service_ids): array
    {
        $this->info('insert title service references');
        // empty title service references
        TitleServiceReference::on('religions')->delete();
        $title_service_reference_ids = TitleServiceReference::on('mysql')->whereIn('service_id', $title_service_ids)->pluck('id')->toArray();
        $title_service_references = TitleServiceReference::on('mysql')->whereIn('id', $title_service_reference_ids)->get();
        $title_service_references_data = $title_service_references->toArray();
        foreach ($title_service_references_data as $key => $title_service_reference) {
            unset($title_service_references_data[$key]['model_type']);
            $title_service_references_data[$key]['created_at'] = date('Y-m-d H:i:s', strtotime($title_service_reference['created_at']));
            $title_service_references_data[$key]['updated_at'] = date('Y-m-d H:i:s', strtotime($title_service_reference['updated_at']));
        }
        TitleServiceReference::on('religions')->insert($title_service_references_data);
        $this->info('title service references inserted');
        return $title_service_references->pluck('id')->toArray();
    }

}

<?php

namespace App\Console\Commands;

use App\Models\TitleService;
use App\Models\TitleServiceText;
use App\Models\TitleServiceVideo;
use Illuminate\Console\Command;

class MigrateTextService extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:text';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $services = TitleService::where('view_type', 1)->get();
        dump($services->count());
        foreach ($services as $service) {
            $text = $service->data[0]["text"];
            TitleServiceText::updateOrCreate([
                'service_id' => $service->id,
                'text' => $text,
                'sort' => 1,
            ],[]);
        }
        return Command::SUCCESS;
    }
}

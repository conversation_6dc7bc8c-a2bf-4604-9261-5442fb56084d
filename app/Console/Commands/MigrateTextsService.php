<?php

namespace App\Console\Commands;

use App\Models\TitleService;
use App\Models\TitleServiceText;
use App\Models\TitleServiceVideo;
use Illuminate\Console\Command;

class MigrateTextsService extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:texts';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $services = TitleService::where('view_type', 7)->get();
        dump($services->count());
        foreach ($services as $service) {
            $sort = 0;
            foreach ($service->data as $text) {
                $sort++;
                dump($sort);
                TitleServiceText::updateOrCreate([
                    'service_id' => $service->id,
                    'text' => $text['text'],
                    'sort' => $sort,
                ],[]);
            }
        }
        return Command::SUCCESS;
    }
}

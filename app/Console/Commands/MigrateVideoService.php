<?php

namespace App\Console\Commands;

use App\Models\TitleService;
use App\Models\TitleServiceVideo;
use Illuminate\Console\Command;

class MigrateVideoService extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:videos';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $services = TitleService::where('view_type', 2)->get();
        foreach ($services as $service) {
            foreach ($service->data as $video) {
                 dump($video['video_id']??null);
                //  dump($service);
                TitleServiceVideo::updateOrCreate([
                    'service_id' => $service->id,
                    'video_id' => $video['video_id']??null,
                    'title' => $video['title'],
                    'detail' => $video['detail'],
                    'video_type' => intval($video['video_type']),
                    'video_file' => is_array($video['video_file']??[]) ? null : $video['video_file']??null,
                    'thumbnail' => $video['thumbnail']??null,
                ],[]);
            }
        }
        return Command::SUCCESS;
    }
}

<?php

namespace App\Console\Commands;

use App\Models\Video;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class MigrateVideosToS3 extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:videosToS3';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        $videos = Video::where('video_type', 1)->get();
        dump($videos->count());
        foreach ($videos as $video) {
            if (!$video->file) continue;
            $this->migrate($video->file);
            if (!$video->thumb) continue;
            $this->migrate($video->thumb);
        }
        return Command::SUCCESS;
    }
    protected function migrate($file_path)
    {
        if (!Storage::disk('uploads')->exists($file_path)) return;
        if (Storage::disk('do')->exists($file_path)) return;
        $file = Storage::disk('uploads')
            ->get($file_path);
        Storage::disk('do')->put($file_path, $file, 'public');
    }
}

<?php

namespace App\Console\Commands;

use App\Models\Calender;
use App\Models\Goal;
use App\Models\Other;
use App\Models\Quotation;
use App\Models\Reference;
use App\Models\TitleService;
use App\Models\VideoContent;
use Illuminate\Console\Command;

class MoveServices extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'move:services';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // move activity to title service
        $activities = \App\Models\Activity::all();
        /*
        [
            {
               "style":"\u0627\u0633\u0644\u0648\u0628 \u0627\u0644\u0646\u0634\u0627\u0637",
               "imp_type":"\u0625\u0633\u0644\u0648\u0628 \u0627\u0644\u062a\u0646\u0641\u064a\u0630 ",
               "type":"\u0646\u0648\u0639 \u0627\u0644\u0623\u0646\u0634\u0637\u0629",
               "goal":"<p dir=\"rtl\">\u0627\u0644\u0623\u0647\u062f\u0627\u0641<\/p>",
               "needed":"<p dir=\"rtl\">\u0627\u0644\u0645\u0637\u0644\u0648\u0628<\/p>",
               "answer":"<p dir=\"rtl\">\u0627\u0644\u0625\u062c\u0627\u0628\u0629<\/p>",
            }]

            [
                [
                {"style":"\u0627\u0644\u0627\u0633\u062a\u0646\u0628\u0627\u0637","imp_type":"\u062c\u0645\u0627\u0639\u064a","type":"\u0634\u0641\u0647\u064a","goal":"<p><span dir=\"RTL\" lang=\"AR-SA\" style=\"font-size:16.0pt\"><span style=\"line-height:115%\"><span style=\"font-family:\">\u064a\u062f\u0631\u0643 \u0645\u0646\u0627\u0633\u0628\u0629 \u062a\u0642\u062f\u064a\u0645 \u0643\u062a\u0627\u0628 \u0627\u0644\u0637\u0647\u0627\u0631\u0629 \u0639\u0644\u0649 \u062c\u0645\u064a\u0639 \u0643\u062a\u0628 \u0648\u0623\u0628\u0648\u0627\u0628 \u0627\u0644\u0641\u0642\u0647.<\/span><\/span><\/span><\/p>","needed":"<p><strong><span style=\"color:#de6036;\"><span dir=\"RTL\" lang=\"AR-SA\" style=\"font-size:16.0pt\"><span style=\"line-height:115%\"><span style=\"font-family:\">\u0645\u0627 \u0645\u0646\u0627\u0633\u0628\u0629 \u062a\u0642\u062f\u064a\u0645 \u0643\u062a\u0627\u0628 \u0627\u0644\u0637\u0647\u0627\u0631\u0629 \u0639\u0644\u0649 \u062c\u0645\u064a\u0639 \u0643\u062a\u0628 \u0648\u0623\u0628\u0648\u0627\u0628 \u0627\u0644\u0641\u0642\u0647\u061f<\/span><\/span><\/span><\/span><\/strong><\/p>","answer":"<p><var><strong><span dir=\"RTL\" lang=\"AR-SA\" style=\"font-size:16.0pt\"><span style=\"line-height:115%\"><span style=\"font-family:\">\u0644\u0623\u0646 \u0623\u0647\u0645 \u0645\u0633\u0627\u0626\u0644 \u0627\u0644\u0641\u0642\u0647: \u0627\u0644\u0635\u0644\u0627\u0629\u061b \u0641\u0647\u064a \u0623\u0647\u0645 \u0623\u0631\u0643\u0627\u0646 \u0627\u0644\u0625\u0633\u0644\u0627\u0645 \u0628\u0639\u062f \u0627\u0644\u0634\u0647\u0627\u062f\u062a\u064a\u0646\u060c \u0648\u0623\u0648\u0644 \u0645\u0627 \u064a\u064f\u0628\u064a\u0646 \u0645\u0646 \u0645\u0633\u0627\u0626\u0644 \u0627\u0644\u0635\u0644\u0627\u0629 \u0647\u0648 \u0634\u0631\u0648\u0637\u0647\u0627\u061b \u0644\u0623\u0646 \u0627\u0644\u0634\u0631\u0637 \u064a\u062a\u0642\u062f\u0645 \u0639\u0644\u0649 \u0627\u0644\u0645\u0634\u0631\u0648\u0637\u060c \u0641\u0644\u0627 \u0628\u062f \u0645\u0646 \u062a\u062d\u0642\u0642 \u0627\u0644\u0634\u0631\u0648\u0637 \u0642\u0628\u0644 \u0627\u0644\u0634\u0631\u0648\u0639 \u0641\u064a \u0627\u0644\u0645\u0634\u0631\u0648\u0637\u060c \u0648\u0645\u0646 \u0623\u0647\u0645 \u0634\u0631\u0648\u0637 \u0627\u0644\u0635\u0644\u0627\u0629: \u0627\u0644\u0637\u0647\u0627\u0631\u0629\u060c \u0648\u0627\u0644\u0637\u0647\u0627\u0631\u0629 \u0645\u0641\u062a\u0627\u062d \u0627\u0644\u0635\u0644\u0627\u0629\u060c \u0648\u0644\u0627 \u062a\u0635\u062d \u0627\u0644\u0635\u0644\u0627\u0629 \u0625\u0644\u0627 \u0628\u0647\u0627.<\/span><\/span><\/span><\/strong><\/var><\/p>"}],[{"style":"\u0627\u0644\u0645\u0642\u0627\u0631\u0646\u0629","imp_type":"\u0641\u0631\u062f\u064a","type":"\u062a\u062d\u0631\u064a\u0631\u064a","goal":"<p><span dir=\"RTL\" lang=\"AR-SA\" style=\"font-size:16.0pt\"><span style=\"line-height:115%\"><span style=\"font-family:\">\u064a\u0645\u0627\u064a\u0632 \u0641\u064a \u062a\u0639\u0631\u064a\u0641 \u0627\u0644\u0637\u0647\u0627\u0631\u0629 \u0628\u064a\u0646 \u0627\u0644\u0627\u0631\u062a\u0641\u0627\u0639 \u0648\u0627\u0644\u0632\u0648\u0627\u0644.<\/span><\/span><\/span><\/p>","needed":"<p dir=\"RTL\" style=\"margin:0cm 0cm 10pt; text-align:right\"><span style=\"color:#de6036;\"><strong><span style=\"font-size:11pt\"><span style=\"line-height:105%\"><span style=\"direction:rtl\"><span style=\"unicode-bidi:embed\"><span calibri=\"\"><span lang=\"AR-SA\" style=\"font-size:16.0pt\"><span style=\"line-height:105%\">\u0627\u0644\u0637\u0647\u0627\u0631\u0629 \u0627\u0635\u0637\u0644\u0627\u062d\u0627: \u0627\u0631\u062a\u0641\u0627\u0639 \u0627\u0644\u062d\u062f\u062b \u0648\u0632\u0648\u0627\u0644 \u0627\u0644\u0646\u062c\u0633.<\/span><\/span><\/span><\/span><\/span><\/span><\/span><\/strong><\/span><\/p>\r\n\r\n<p><span style=\"color:#de6036;\"><strong><span dir=\"RTL\" lang=\"AR-SA\" style=\"font-size:16.0pt\"><span style=\"line-height:115%\">\u0645\u0627 \u0648\u062c\u0647 \u0627\u0644\u062a\u0639\u0628\u064a\u0631 \u0641\u064a \u062c\u0627\u0646\u0628 \u0627\u0644\u062d\u062f\u062b \u0628\u0627\u0644\u0627\u0631\u062a\u0641\u0627\u0639\u060c \u0648\u0641\u064a \u062c\u0627\u0646\u0628 \u0627\u0644\u0646\u062c\u0633 \u0628\u0627\u0644\u0632\u0648\u0627\u0644\u061f<\/span><\/span><\/strong><\/span><\/p>","answer":"<p dir=\"RTL\" style=\"margin:0cm 0cm 10pt; text-align:right\"><var><strong><span style=\"font-size:11pt\"><span style=\"line-height:105%\"><span style=\"direction:rtl\"><span style=\"unicode-bidi:embed\"><span calibri=\"\" style=\"font-family:\"><span lang=\"AR-SA\" style=\"font-size:16.0pt\"><span style=\"line-height:105%\"><span style=\"font-family:\">\u0648\u062c\u0647 \u0627\u0644\u062a\u0639\u0628\u064a\u0631 \u0641\u064a \u062c\u0627\u0646\u0628 \u0627\u0644\u062d\u062f\u062b \u0628\u0627\u0644\u0627\u0631\u062a\u0641\u0627\u0639\u061b \u0644\u0623\u0646\u0647 \u0623\u0645\u0631\u064c \u0645\u0639\u0646\u0648\u064a.<\/span><\/span><\/span><\/span><\/span><\/span><\/span><\/span><\/strong><\/var><\/p>\r\n\r\n<p><var><strong><span dir=\"RTL\" lang=\"AR-SA\" style=\"font-size:16.0pt\"><span style=\"line-height:115%\"><span style=\"font-family:\">\u0648\u0648\u062c\u0647 \u0627\u0644\u062a\u0639\u0628\u064a\u0631 \u0641\u064a \u062c\u0627\u0646\u0628 \u0627\u0644\u0646\u062c\u0633 \u0628\u0627\u0644\u0632\u0648\u0627\u0644\u061b \u0644\u0623\u0646\u0647 \u062c\u064f\u0631\u0652\u0645\u064c \u062d\u0633\u064a\u060c \u0648\u0627\u0644\u0625\u0632\u0627\u0644\u0629 \u0644\u0627 \u062a\u0643\u0648\u0646 \u0625\u0644\u0627 \u0641\u064a \u0627\u0644\u0623\u062c\u0631\u0627\u0645.<\/span><\/span><\/span><\/strong><\/var><\/p>"}]]

        */
        $activities = $activities->groupBy('title_id');
        // dump($activities);
        foreach ($activities as $title_id =>  $activity) {
            $data = [];
            foreach ($activity as  $value) {
                $data[] =
                    [
                        "style" => $value->style,
                        "imp_type" => $value->imp_type,
                        "type" => $value->type,
                        "goal" => $value->goal,
                        "needed" => $value->needed,
                        "answer" => $value->answer,
                    ];
            }
            TitleService::create([
                "title_id" => $title_id,
                "view_type" => 5,
                "service_type" => 2,
                "name" => "الأنشطة",
                "sort" => 2,
                "data" => $data,
            ]);
        }
        // move quotations to title_services
        $quotations = Quotation::all();
        /*
        [{"text":"tex","name":"ref"}]
        */
        $quotations = $quotations->groupBy('title_id');
        foreach ($quotations as $title_id =>  $quotation) {
            $data = [];
            foreach ($quotation as  $value) {
                $data[] =
                    [
                        "text" => $value->text,
                        "name" => $value->refrence_name,
                    ];
            }
            TitleService::create([
                "title_id" => $title_id,
                "view_type" => 3,
                "service_type" => 1,
                "name" => "الاقتباسات",
                "sort" => 3,
                "data" => $data,
            ]);
        }
        // move references to title_services
        $references = Reference::all();
        /*
        [{"name":"test","description":"<p>fsdf<\/p>","file":"2a79nS1DH7wdUBY5qgb5TWDTxyqH2p-metaZExLSmZPLTIwMjItMDktMjIucGRm-.pdf"}]
        */
        $references = $references->groupBy('title_id');
        foreach ($references as $title_id =>  $reference) {
            $data = [];
            foreach ($reference as  $value) {
                $data[] =
                    [
                        "name" => $value->name,
                        "description" => $value->description,
                        "file" => $value->file,
                    ];
            }
            TitleService::create([
                "title_id" => $title_id,
                "view_type" => 4,
                "service_type" => 1,
                "name" => "المراجع",
                "sort" => 4,
                "data" => $data,
            ]);
        }

        // move calender to title service
        $calenders = Calender::all();
        $calenders = $calenders->groupBy('title_id');
        foreach ($calenders as $title_id => $calender) {
            $data = [];
            foreach ($calender as  $value) {
                /*
                [{
                   "style":"style",
                   "imp_type":"imp_type",
                   "goal":"<p>goals<\/p>",
                   "duration":"duration",
                   "tools_questions":"<p>tools<\/p>",
                   "feedback_answers":"<p>feed<\/p>"
                }]
                */
                $data[] =
                    [
                        "style" => $value->style,
                        "imp_type" => $value->imp_style,
                        "goal" => $value->goal,
                        "duration" => $value->duration,
                        "tools_questions" => $value->tools_questions,
                        "feedback_answers" => $value->feedback_answers,
                    ];
            }
            TitleService::create([
                "title_id" => $title_id,
                "view_type" => 6,
                "service_type" => 2,
                "name" => "التقويم",
                "sort" => 3,
                "data" => $data,
            ]);
        }
        // move goals to title service
        $goals = Goal::all();
        /*
        [
            {
                "text":"<p>sdf<\/p>"
            }
        ]
        */
        foreach ($goals as $goal) {
            TitleService::create([
                "title_id" => $goal->title_id,
                "view_type" => 1,
                "service_type" => 2,
                "name" => "الأهداف",
                "sort" => 1,
                "data" => [
                    [
                        "text" => $goal->text,
                    ]
                ],
            ]);
        }
        // move others to title service
        $others = Other::all();
        /*
        [
            {
                "content":"<p>sdf<\/p>"
            }
        ]
        */
        foreach ($others as $other) {
            TitleService::create([
                "title_id" => $other->title_id,
                "view_type" => 1,
                "service_type" => 2,
                "name" => "اخرى",
                "sort" => 4,
                "data" => [
                    [
                        "text" => $other->content,
                    ]
                ],
            ]);
        }
        // videos to title service
        $videos = VideoContent::all();
        $videos = $videos->groupBy('title_id');
        foreach ($videos as $title_id => $video) {
            $data = [];
            foreach ($video as  $value) {
                /*
                [
                    {
                        "video_type":"2",
                        "thumbnail":"https:\/\/i.ytimg.com\/vi\/g3MGDKPse34\/hqdefault.jpg",
                        "video_id":"g3MGDKPse34",
                        "title":"\u0646\u0635 \u0627\u0644\u0641\u064a\u062f\u064a\u0648",
                        "detail":"\u062a\u0641\u0627\u0635\u064a\u0644",
                    }
                    ]
                */
                $data[] =
                    [
                        "video_type" => 2,
                        "thumbnail" => "https://i.ytimg.com/vi/$value->url/default.jpg",
                        "video_id" =>$value->url,
                        "title" => $value->name,
                        "detail" => $value->description,
                    ];
            }
            TitleService::create([
                "title_id" => $title_id,
                "view_type" => 2,
                "service_type" => 1,
                "name" => "مواد مرئية",
                "sort" => 2,
                "data" => $data,
            ]);
        }
        return Command::SUCCESS;
    }
}

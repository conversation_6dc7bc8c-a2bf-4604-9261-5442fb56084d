<?php

namespace App\Console\Commands;

use App\Models\TitleServiceVideo;
use App\Models\Video;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;

class RemoveContentVideos extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'remove:videos';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        $videos = TitleServiceVideo::where('video_type', 1)->get();
        dump($videos->count());
        $count = 0;
        $i = 1;
        foreach ($videos as $video) {
            // dump("$i / " . $videos->count(),);
            $i++;
            if (!$video->video_file) continue;
            $count += $this->remove($video->video_file);
            if (!$video->thumbnail) continue;
            $count += $this->remove($video->thumbnail);
        }
        dump($count . ' videos removed');
        return Command::SUCCESS;
    }
    protected function remove($file_path): int
    {

        if (!Storage::disk('uploads')->exists($file_path)) {
            // dump($file_path . ' not exists');
            return 0;
        }
        if (Storage::disk('do')->exists($file_path)) {
            $file = Storage::disk('uploads')
                ->delete($file_path);
            dump($file_path . ' removed');
            return 1;
        }

        return 0;
    }
}

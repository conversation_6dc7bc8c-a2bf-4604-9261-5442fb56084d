<?php

namespace App\Console\Commands;

use App\Models\TitleService;
use Illuminate\Console\Command;

class RenameQuotes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'rename:quotes';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $services = TitleService::where(function ($query){
            $query->where('name','like','%اقتباسات%')
            ->orWhere(function($q){
                $q->where('name','like','%مذاهب%')
                ->where('name','not like','%المذاهب%');
            });
        })->whereHas('title.book',function($query){
            $query->where('book_id',116);
        })->get();
        dump($services->count());
        foreach ($services as $service){
            $service->name = str_replace('الاقتباسات','المذاهب',$service->name);
            $service->name = str_replace('اقتباسات','المذاهب',$service->name);
            $service->name = str_replace('مذاهب','المذاهب',$service->name);
            $service->save();
            // dump($service->name);
        }
        $services = TitleService::where(function ($query){
            $query->where('name','like','%فيديوهات%')
            ->orWhere(function($q){
                $q->where('name','like','%مرئيات%')
                ->where('name','not like','%المرئيات%');
            })
            ->orWhere(function($q){
                $q->where('name','like','%مواد مرئية%')
                ;
            })
            ->where('name','not like','%المواد المرئية%');
        })->whereHas('title.book',function($query){
            $query->where('book_id',116);
        })->get();
        dump($services->count());
        foreach ($services as $service){
            $service->name = str_replace('مرئيات','المرئيات',$service->name);
            $service->name = str_replace('المواد المرئية','المرئيات',$service->name);
            $service->name = str_replace('مواد مرئية','المرئيات',$service->name);
            // $service->name = str_replace('الال','ال',$service->name);
            // $service->name = str_replace('الالال','ال',$service->name);
            $service->save();
            // dump($service->name);
        }
        $services = TitleService::where(function ($query){
            $query->where('name','like','%فتاوى%')
            ->where('name','not like','%الفتاوى%');
        })->whereHas('title.book',function($query){
            $query->where('book_id',116);
        })->get();
        dump($services->count());
        foreach ($services as $service){
            $service->name = str_replace('فتاوى','الفتاوى',$service->name);
            $service->save();
            // dump($service->name);
        }
        $this->info('Services renamed successfully.');
    }
}

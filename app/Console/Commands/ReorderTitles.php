<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class ReorderTitles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reorder:titles';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $titles = \App\Models\Title::all();
        foreach ($titles as $title) {
            $title->order = $title->id;
            $title->save();
        }
        $this->info('Titles re-ordered successfully.');
        return Command::SUCCESS;
    }
}

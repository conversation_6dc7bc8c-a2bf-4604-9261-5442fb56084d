<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\URL;

class generateSiteMap extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mapsite:generate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        $routeCollection = Route::getRoutes();

        $urls = [];

        foreach ($routeCollection as $value) {
            if (str($value->uri)->contains(['admin', 'livewire', '_ignition', 'filament', 'sanctum', 'captcha', 'api/user'])) {
                continue;
            }

            $urls[$value->uri] = route($value->uri);
        }

        dd($urls);

        // $sitemap = resolve("sitemap");

        // $sitemap->add("test", '2012-08-25T20:10:00+02:00', '1.0', 'daily');
        // $sitemap->add(URL::to('page'), '2012-08-26T12:30:00+02:00', '0.9', 'monthly');


        // // generate (format, filename)
        // // sitemap.xml is stored within the public folder
        // $sitemap->store('xml', 'sitemap');


        return Command::SUCCESS;
    }
}

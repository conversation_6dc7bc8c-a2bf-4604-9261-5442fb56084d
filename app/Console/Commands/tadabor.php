<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class tadabor extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tadabor';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        // $this->info(str_replace("%",":","إِيَّاكَ نَعْبُدُ % لاَ نَعْبُدُ إِلاَّ أَنْتَ."));
        $this->readCSV();
        return Command::SUCCESS;
    }

    public function readCSV()
    {
        $file_handle = fopen(database_path("migrations/csv/" . "tadabor.csv"), 'r');
        while (!feof($file_handle)) {
            $line_of_text[] = fgetcsv($file_handle, 0, ",");
        }
        fclose($file_handle);


        DB::table("tadabor_verses")->delete();
        DB::table("tadabor")->delete();

        $this->info("deleted old data successed");

        $index = 1;

        // id,sura_number,page_number,start_aya,start_aya,end_aya,type,type_id,number,نص الآية,text

        while ($index  < count($line_of_text)) {
            DB::table("tadabor")->insert([
                "id" => $line_of_text[$index][0],
                "type" => $line_of_text[$index][6],
                "type_id" => $line_of_text[$index][7],
                "text" => $line_of_text[$index][7] == 4 ? str_replace("%", ":", $line_of_text[$index][10]) : $line_of_text[$index][10],
                "verse_text" => $line_of_text[$index][9],
            ]);


            $current_ayah = abs($line_of_text[$index][4]);


            $this->info("tadabor_id= " . $line_of_text[$index][0]);
            $this->info("sorah_num= " . $line_of_text[$index][1]);

            while ($current_ayah <= abs($line_of_text[$index][5])) {
                $this->info("ayah_num =" . $current_ayah);
                $verse_id = DB::table("quran")->select("id")->where("sora_num", "=", $line_of_text[$index][1])
                    ->where("aya_num", "=", $current_ayah)->first()->id;

                $this->info("verse_id= " . $verse_id);
                DB::table("tadabor_verses")->insert([
                    "tadabor_id" => $line_of_text[$index][0],
                    "verse_id" =>  $verse_id,

                ]);
                $current_ayah++;
            }

            $index++;
        }
    }
}

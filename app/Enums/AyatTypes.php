<?php


namespace App\Enums;


class AyatTypes extends Enum
{
    const SIMPLE          = 'quranSimple';
    const SIMPLE_CLEAN    = 'quranSimpleClean';
    const SIMPLE_ENHANCED = 'quranSimpleEnhanced';
    const SIMPLE_MIN      = 'quranSimpleMin';
    const UTHMANI         = 'quranUthmani';
    const UTHMANI_MIN     = 'quranUthmaniMin';
    const ALL_TYPES       = [
        self::SIMPLE,
        self::SIMPLE_CLEAN,
        self::SIMPLE_ENHANCED,
        self::SIMPLE_MIN,
        self::UTHMANI,
        self::UTHMANI_MIN,
    ];

    public static function TypesAndLabels(): array
    {
        return [
            self::SIMPLE          => ['label' => 'إملائي عادي', 'ayah' => 'مَالِكِ يَوْمِ الدِّينِ'],
            self::SIMPLE_CLEAN    => ['label' => 'إملائي عادي بدون تشكيل', 'ayah' => 'مالك يوم الدين'],
            self::SIMPLE_ENHANCED => ['label' => 'إملائي عادي محسن', 'ayah' => 'مَالِكِ يَوْمِ الدِّينِ'],
            self::SIMPLE_MIN      => ['label' => 'إملائي عادي مخفف', 'ayah' => 'مالِكِ يَومِ الدّينِ'],
            self::UTHMANI         => ['label' => 'إملائي عثماني', 'ayah' => 'مَٰلِكِ يَوْمِ ٱلدِّينِ'],
            self::UTHMANI_MIN     => ['label' => 'إملائي عثماني مخفف', 'ayah' => 'مٰلِكِ يَومِ الدّينِ'],
        ];
    }
}

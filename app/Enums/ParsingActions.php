<?php


namespace App\Enums;


use App\Services\ParsingActions\FinishAction;
use App\Services\ParsingActions\ProcessIndexesAction;
use App\Services\ParsingActions\StartAction;

class ParsingActions extends Enum
{
    const START           = 'start';
    const PROCESS_INDEXES = 'processIndexes';
    const FINISH          = 'finish';
    const ALL_TYPES       = [
        self::START,
        self::PROCESS_INDEXES,
        self::FINISH,
    ];

    public static function TypesAndLabels(): array
    {
        return [
            self::START           => ['label' => 'البدء بالمعالجة، وإستخراج الفهارس'],
            self::PROCESS_INDEXES => ['label' => 'معالجة الفهارس'],
            self::FINISH          => ['label' => 'إنهاء المعالجة'],
        ];
    }

    /**
     * @param string $action [start, processIndexes, finish]
     *
     * @return StartAction|ProcessIndexesAction|FinishAction
     */
    public static function ActionClassFor(string $action)
    {
        $action = [
                      static::START           => StartAction::class,
                      static::PROCESS_INDEXES => ProcessIndexesAction::class,
                      static::FINISH          => FinishAction::class,
                  ][$action];
        return new $action;
    }
}

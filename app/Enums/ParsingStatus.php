<?php


namespace App\Enums;


class ParsingStatus
{
    const PENDING   = 0;
    const PARSING   = 1;
    const PARSED    = 2;
    const FAILED    = 3;
    const ALL_TYPES = [
        self::PENDING,
        self::PARSING,
        self::PARSED,
        self::FAILED,
    ];

    public static function TypesAndLabels(): array
    {
        return [
            self::PENDING => ['label' => 'بإنتظار المعالجة'],
            self::PARSING => ['label' => 'جاري المعالجة'],
            self::PARSED  => ['label' => 'تمت المعالجة'],
            self::FAILED  => ['label' => 'فشلت المعالجة'],
        ];
    }
}

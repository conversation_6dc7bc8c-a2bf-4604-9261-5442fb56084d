<?php


namespace App\Enums;


class ProcessingStatus
{
    const PENDING   = 0;
    const PROCESSING   = 1;
    const SUCCESS    = 2;
    const FAILED    = 3;
    const ALL_TYPES = [
        self::PENDING,
        self::PROCESSING,
        self::SUCCESS,
        self::FAILED,
    ];

    public static function TypesAndLabels(): array
    {
        return [
            self::PENDING => ['label' => 'بإنتظار المعالجة'],
            self::PROCESSING => ['label' => 'جاري المعالجة'],
            self::SUCCESS  => ['label' => 'تمت المعالجة'],
            self::FAILED  => ['label' => 'فشلت المعالجة'],
        ];
    }
}

<?php


namespace App\Enums;


class QuranVersions extends Enum
{
    const NORMAL    = 'quran_othmany_v1';
    const V2        = 'quran_othmany_qcf2';
    const SVG       = 'quran_othmany_svg';
    const ALL_TYPES = [
        self::NORMAL,
        self::V2,
        self::SVG,
    ];

    public static function TypesAndLabels(): array
    {
        return [
            self::NORMAL => ['label' => 'مصحف المدينة النبوية (الإصدار القديم)', 'type' => 'v1'],
            self::V2     => ['label' => 'مصحف المدينة النبوية (الإصدار الجديد)', 'type' => 'qcf2'],
            self::SVG    => ['label' => 'مصحف المدينة النبوية (الإصدار الجديد - معدل)', 'type' => 'svg'],
        ];
    }
}

<?php

namespace App\Exports;

use App\Filament\Resources\TitleResource;
use App\Models\Title;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;

class ServicesExport implements FromArray, WithHeadings, ShouldAutoSize
{

    public  $records;
    public string  $view_type;

    function   __construct($view_type, $records)
    {
        $this->records = $records;
        $this->view_type = $view_type;
    }

    public function headings(): array
    {
        if ($this->view_type == "text") {
            return [
                "#",
                __('common.fields.book_name.label'),
                __('common.fields.title_name.label'),
                __('common.fields.service_name.label'),
                __('title_service_text.fields.text.label')
            ];
        } elseif ($this->view_type == "video") {
            return [
                "#",
                __('common.fields.book_name.label'),
                __('common.fields.title_name.label'),
                __('common.fields.service_name.label'),
                __('title_service_video.fields.video_type.label'),
                __('title_service_video.fields.thumbnail.label'),
                __('title_service_video.fields.title.label'),
                __('title_service_video.fields.detail.label'),
                __('Video'),
            ];
        } elseif ($this->view_type == "quotation") {
            return [
                "#",
                __('common.fields.book_name.label'),
                __('common.fields.title_name.label'),
                __('common.fields.service_name.label'),
                __('title_service_quotation.fields.name.label'),
                __('title_service_quotation.fields.text.label'),
                __('common.fields.sort.label'),
            ];
        } elseif ($this->view_type == "reference") {
            return [
                "#",
                __('common.fields.book_name.label'),
                __('common.fields.title_name.label'),
                __('common.fields.service_name.label'),
                __('title_service_reference.fields.name.label'),
                __('title_service_reference.fields.description.label'),
                __('title_service_reference.fields.file.label'),
                __('common.fields.sort.label'),
            ];
        } elseif ($this->view_type == "activity") {
            return [
                "#",
                __('common.fields.book_name.label'),
                __('common.fields.title_name.label'),
                __('common.fields.service_name.label'),
                __('title_service_activity.fields.view_type.label'),
                __('title_service_activity.fields.type.label'),
                __('title_service_activity.fields.style.label'),
                __('title_service_activity.fields.imp_type.label'),
                __('title_service_activity.fields.goal.label'),
                __('title_service_activity.fields.needed.label'),
                __('title_service_activity.fields.answer.label'),
                __('common.fields.sort.label'),
            ];
        } elseif ($this->view_type == "calender") {
            return [
                "#",
                __('common.fields.book_name.label'),
                __('common.fields.title_name.label'),
                __('common.fields.service_name.label'),
                __('title_service_calender.fields.style.label'),
                __('title_service_calender.fields.imp_type.label'),
                __('title_service_calender.fields.duration.label'),
                __('title_service_calender.fields.goal.label'),
                __('title_service_calender.fields.tools_questions.label'),
                __('title_service_calender.fields.feedback_answers.label'),
                __('common.fields.sort.label'),
            ];
        }
        return [];
    }
    /**
     * @return \Illuminate\Support\Collection
     */
    public function array(): array
    {
        $sevices = [];


        foreach ($this->records as $record) {
            $sevice = [];
            //text
            if ($this->view_type == 'text') {
                $sevice[] = ($record->id);


                $sevice[] = $record->service?->title?->book?->name ?? "";
                $sevice[] = $record->service?->title?->name ?? "";
                $sevice[] = $record->service?->name ?? "";

                $sevice[] = ($record->text);
            } elseif ($this->view_type == "video") {
                $sevice[] = ($record->id);

                $sevice[] = $record->service?->title?->book?->name ?? "";
                $sevice[] = $record->service?->title?->name ?? "";
                $sevice[] = $record->service?->name ?? "";
                $sevice[] = __('title_service_video.options.video_type.' . $record->video_type);
                $sevice[] = $record->title;
                $sevice[] = $record->detail ?? "";
                $sevice[] = $record->video_type == 1 ? url('uploads/' . $record->video_file) : 'https://www.youtube.com/watch?v=' . $record->video_id;
            } elseif ($this->view_type == "quotation") {
                $sevice[] = ($record->id);

                $sevice[] = $record->service?->title?->book?->name ?? "";
                $sevice[] = $record->service?->title?->name ?? "";
                $sevice[] = $record->service?->name ?? "";

                $sevice[] = $record->name;
                $sevice[] = $record->text;
                $sevice[] = $record->sort;
            } elseif ($this->view_type == "reference") {
                $sevice[] = ($record->id);

                $sevice[] = $record->service?->title?->book?->name ?? "";
                $sevice[] = $record->service?->title?->name ?? "";
                $sevice[] = $record->service?->name ?? "";

                $sevice[] = $record->name;
                $sevice[] = $record->description;
                $sevice[] = url('uploads/' . $record->file);
                $sevice[] = $record->sort;
            } elseif ($this->view_type == "activity") {
                $sevice[] = ($record->id);

                $sevice[] = $record->service?->title?->book?->name ?? "";
                $sevice[] = $record->service?->title?->name ?? "";
                $sevice[] = $record->service?->name ?? "";

                $sevice[] = __('common.options.view_type.' . $record->view_type);
                $sevice[] = $record->type;
                $sevice[] = $record->style;
                $sevice[] = $record->imp_type;
                $sevice[] = $record->goal;
                $sevice[] = $record->needed;
                $sevice[] = $record->answer;
                $sevice[] = $record->sort;
            } elseif ($this->view_type == "calender") {
                $sevice[] = ($record->id);

                $sevice[] = $record->service?->title?->book?->name ?? "";
                $sevice[] = $record->service?->title?->name ?? "";
                $sevice[] = $record->service?->name ?? "";

                $sevice[] = $record->style;
                $sevice[] = $record->imp_type;
                $sevice[] = $record->duration;

                $sevice[] = $record->goal;
                $sevice[] = $record->tools_questions;
                $sevice[] = $record->feedback_answers;
                $sevice[] = $record->sort;
            }

            foreach ($sevice as &$value) {
                $value = strip_tags($value);

                $value = str_replace('&nbsp;', ' ', $value);
            }
            $sevices[] = $sevice;
        }
        return  $sevices;
    }
}

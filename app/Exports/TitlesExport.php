<?php

namespace App\Exports;

use App\Filament\Resources\TitleResource;
use App\Models\Title;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;

class TitlesExport implements FromArray, WithHeadings, ShouldAutoSize
{

    public $bookId;

    function   __construct($bookId)
    {
        $this->bookId = $bookId;
    }

    public function headings(): array
    {
        return [
            'Title ID',
            'Title Name',
            'Is Parent',
            'statistic',
            'Dashboard Url',
            'Web Url',
        ];
    }
    /**
     * @return \Illuminate\Support\Collection
     */
    public function array(): array
    {
        $titles = Title::where('book_id', $this->bookId)->with('services')->get();
        $titles = $titles->map(fn($title) => [
            $title->id,
            $title->name,
            $title->parent_id == null ? "نعم" : "لا",

            $title->getServicesStatistics(),
            TitleResource::getUrl(
                'view',
                $title->id
            ),
            route(\App\Facades\Panel::currentPanel() . ".BookPage", $title->book_id) . '?t=' . $title->id

        ])->toArray();
        return  $titles;
    }
}

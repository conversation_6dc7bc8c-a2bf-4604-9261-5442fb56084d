<?php

namespace App\Exports;

use App\Models\TitleService;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;

class VideosExport implements FromArray, WithHeadings
{
    public function headings(): array
    {
        return [
            'Book ID',
            'Book Name',
            'Title ID',
            'Title Name',
            'Video Title',
            'Video Description',
        ];
    }
    /**
     * @return \Illuminate\Support\Collection
     */
    public function array(): array
    {
        $services = TitleService::with('title:id,name,book_id', 'title.book:id,name')
            ->whereHas('title')
            ->whereHas('title.book')
            ->where('view_type', 2)->get();
        $services = $services->sortBy(function ($service) {
            return $service->title->book->id;
        });
        $array = [];
        foreach ($services as $service) {
            $book_id = $service->title->book_id;
            $book_name = $service->title->book->name;
            $title_id = $service->title_id;
            $title_name = $service->title->name;

            foreach ($service->data as $video) {
                $video_title = $video['title'];
                $video_detail = $video['detail'];
                $array[] = [
                    $book_id,
                    $book_name,
                    $title_id,
                    $title_name,
                    $video_title,
                    $video_detail
                ];
            }
        }
        return $array;
    }
}

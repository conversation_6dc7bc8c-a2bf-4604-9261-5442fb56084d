<?php

namespace App\Filament\Actions;

use Filament\Tables\Actions\CreateAction as BaseCreateAction;
use Illuminate\Database\Eloquent\Model;

class RMCreateAction extends BaseCreateAction
{
    protected function setUp(): void
    {
        parent::setUp();
    }

    protected function getDefaultEvaluationParameters(): array
    {
        return array_merge(parent::getDefaultEvaluationParameters(), [
            'ownerRecord' => $this->resolveEvaluationParameter(
                'ownerRecord',
                fn (): ?Model => $this->getRelationship()->getParent(),
            ),
        ]);
    }
}

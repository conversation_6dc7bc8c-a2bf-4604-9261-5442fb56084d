<?php

namespace App\Filament\Actions;

use App\Filament\Resources\TitleResource;
use Filament\Tables\Actions\Action;
use Illuminate\Database\Eloquent\Model;

class VisitTitleAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'visit_title';
    }
    protected function setUp(): void
    {
        parent::setUp();
        $this->label(__('title_service_video.actions.visit_title'));
        $this->url(fn ($record) => TitleResource::getUrl('view', ['record' => $record->service?->title]), shouldOpenInNewTab: true);
        $this->icon('heroicon-o-link');
        $this->visible( function($record){
            return $record->service?->title !=null;
        });
        $this->color('success');
    }


}

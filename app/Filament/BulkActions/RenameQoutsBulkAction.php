<?php

namespace App\Filament\BulkActions;

use App\Models\TitleService;
use App\Models\TitleServiceQuotation;
use App\Models\TitleServiceReference;
use Filament\Forms\Components\Actions\Action as ActionsAction;
use Filament\Forms\Components\Actions\Modal\Actions\Action;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Tables\Actions\BulkAction;
use Filament\Tables\Actions\Modal\Actions\Action as ModalActionsAction;
use Illuminate\Support\Facades\DB;

class RenameQoutsBulkAction extends BulkAction
{
    public static function getDefaultName(): ?string
    {
        return 'rename_qouts';
    }

    protected function setUp(): void
    {
        parent::setUp();
        $this->label(__('common.actions.rename_qouts.label'));
        $this->icon('heroicon-o-pencil');
        $this->color('success');
        $this->form(function ($records) {
            $temp = $records->pluck('name')->unique();
            $refrencesNames = [];
            foreach ($temp as $key => $value) {
                $refrencesNames[$value] = $value;
            }
            return [
                TextInput::make('search')
                    ->label(__('common.actions.rename_qouts.fields.search.label'))
                    ->reactive()
                    ->afterStateUpdated(function ($state, $set) use ($records) {
                        $search = $state;
                        if (empty($search)) {
                            $set('search_result', 0);
                            return;
                        }
                        $services_ids = $records->pluck('id')->unique()->toArray();
                        $result_count =  TitleServiceQuotation::whereIn('id', $services_ids)->where('name', 'like', '%' . $search . '%')->count();
                        $set('search_result', $result_count);
                    })
                    ->required(),
                TextInput::make('replace')
                    ->label(__('common.actions.rename_qouts.fields.replace.label'))
                    ->required(),
                Placeholder::make('search_result')
                    ->label(__('common.actions.rename_qouts.fields.search_result.label'))
                    ->visible(fn ($get) => $get('search_result') != "" && $get('search') !== null)
                    ->content(function ($get) {
                        return $get('search_result');
                    })
            ];
        });
        $this->action(function ($data, $records, $action) {
            $services_ids = $records->pluck('id')->unique()->toArray();
            $search = $data['search'];
            $replace = $data['replace'];
            TitleServiceQuotation::whereIn('id', $services_ids)->where('name', 'like', '%' . $search . '%')->update([
                'name' => DB::raw("REPLACE(name,'$search','$replace')")
            ]);
            $action->success();
        });
        $this->modalButton(__('common.actions.rename_qouts.label'));
        $this->deselectRecordsAfterCompletion();
        $this->successNotificationTitle(__('common.actions.rename_qouts.success'));
    }
}

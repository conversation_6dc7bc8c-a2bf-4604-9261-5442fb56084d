<?php

namespace App\Filament\BulkActions;

use App\Models\TitleService;
use App\Models\TitleServiceReference;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Tables\Actions\BulkAction;

class RenameRefrencesBulkAction extends BulkAction
{
    public static function getDefaultName(): ?string
    {
        return 'rename_references';
    }

    protected function setUp(): void
    {
        parent::setUp();
        $this->label(__('common.actions.rename_refrences.label'));
        $this->icon('heroicon-o-pencil');
        $this->color('success');
        $this->form(function ($records) {
            $temp = $records->pluck('name')->unique();
            $refrencesNames = [];
            foreach ($temp as $key => $value) {
                $refrencesNames[$value] = $value;
            }
            return [
                Select::make('from')
                    ->label(__('common.actions.rename_refrences.fields.from.label'))
                    ->options($refrencesNames)
                    ->required(),
                TextInput::make('to')
                    ->label(__('common.actions.rename_refrences.fields.to.label'))
                    ->required(),
            ];
        });
        $this->action(function ($data, $records, $action) {
            $services_ids = $records->pluck('id')->unique()->toArray();
            TitleServiceReference::whereIn('id', $services_ids)->where('name', $data['from'])->update(['name' => $data['to'],]);
            $action->success();
        });
        $this->modalButton(__('common.actions.rename_refrences.label'));
        $this->deselectRecordsAfterCompletion();
        $this->successNotificationTitle(__('common.actions.rename_refrences.success'));
    }
}

<?php

namespace App\Filament\BulkActions;

use App\Models\TitleService;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Tables\Actions\BulkAction;

class RenameServicesBulkAction extends BulkAction
{
    public static function getDefaultName(): ?string
    {
        return 'rename_services';
    }

    protected function setUp(): void
    {
        parent::setUp();
        $this->label(__('common.actions.rename_services.label'));
        $this->icon('heroicon-o-pencil');
        $this->color('warning');
        $this->form(function ($records) {
            $serviceNames = TitleService::select('name')->distinct()->whereIn('id', $records->pluck('service_id')->unique()->toArray())->pluck('name', 'name');
            return [
                Select::make('from')
                    ->label(__('common.actions.rename_services.fields.from.label'))
                    ->options($serviceNames)
                    ->required(),
                TextInput::make('to')
                    ->label(__('common.actions.rename_services.fields.to.label'))
                    ->required(),
            ];
        });
        $this->action(function ($data, $records, $action) {
            $services_ids = $records->pluck('service_id')->unique()->toArray();
            TitleService::whereIn('id', $services_ids)->where('name', $data['from'])->update(['name' => $data['to'],]);
            $action->success();
        });
        $this->modalButton(__('common.actions.rename_services.label'));
        $this->deselectRecordsAfterCompletion();
        $this->successNotificationTitle(__('common.actions.rename_services.success'));
    }
}

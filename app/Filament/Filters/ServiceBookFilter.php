<?php

namespace App\Filament\Filters;

use App\Models\Book;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;

class ServiceBookFilter extends SelectFilter
{

    public static function getDefaultName(): ?string
    {
        return 'book_id';
    }
    protected function setUp(): void
    {
        parent::setUp();
        $this->label(__('title_service_video.fields.book_name.label'));
        $this->options(Book::all()->pluck('name', 'id'));
        $this->query(function ($query,  $state) {
            if (!$state['value']) return $query;
            $query->whereHas('service.title.book', function ($query) use ($state) {
                return $query->where('id', $state['value']);
            });
        });
    }
}

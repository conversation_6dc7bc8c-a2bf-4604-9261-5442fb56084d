<?php

namespace App\Filament\Pages;

use App\Facades\Panel;
use App\Models\AboutCurriculum;
use App\Traits\MultiPanelPage;
use Filament\Facades\Filament;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Pages\Page;

class AboutCirriculumPage extends Page
{
    use MultiPanelPage;
    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static string $view = 'filament.pages.about';
    public $data = [];

    // get navigation group function
    protected static function getNavigationGroup(): string
    {
        return __("common.navigation_groups.about_pages");
    }
    // get navigation label function
    protected static function getNavigationLabel(): string
    {
        return __("about_curriculum.plural_label");
    }
    protected  function getTitle(): string
    {
        return __("about_curriculum.plural_label");
    }

    protected static function shouldRegisterNavigation(): bool
    {
        return Panel::currentPanel() == "kholasah";
    }
    public function mount()
    {
        $data =  AboutCurriculum::firstOrCreate([
            "id" => 1,

        ], [
            "id" => 1,
            "title" => "مشروع المناهج التعليمية",
            "description" => "بناء مناهج تعليمية مبنية على مشروع الخلاصة العلمية وتقديمها للمؤسسات التعليمية من جامعات وكليات ومعاهد ومدارس في مختلف أنحاء العالم .والتي تمكنهم من العقيدة الصحيحة والأحكام الشرعية والقيم الإسلامية التي تجعلهم معتزين بالإسلام عاملين بأحكامه داعين إليه.",
            "image" => "",
            "file" => "",
            // "text" => "",
            "methods" => [
                ["text" => 'تحميل المناهج التعليمية"كتاب إلكتروني"'],
                ["text" => 'شراء نسخة من المناهج التعليمية']
            ],
            "main_goal" => 'اكساب المتعلم أهم العلوم الشرعية والعصرية',
            "goals" => '<ol>
            <li>المساهمة في تطوير التعليم من خلال تقديم مناهج تعليمية بمعايير متقدمة
            </li>
            <li>مساعدة مؤسسات التعليم في تحقيق رسالتها.
            </li>
            <li>تعميق المستوى العلمي للمتعلمين في تفاصيل الأحكام الشرعية والعصرية.
            </li>
            <li>تكوين الوعي الإيجابي الذي يواجه به المتعلم الأفكار الهدامة والاتجاهات المضللة.
            </li>
            <li>تزويد المتعلمين بأهم العلوم العصرية الضرورية.
            </li>
            <li>تضمين المناهج للتوجيهات الإيجابية الحديثة في بناء المناهج، ومنه، مهارات التفكير، ومهارات
                حل المشكلات، التعليم الذاتي.
            </li>
            <li>تنمية المهارات الأدائية من خلال التركيز على التعليم عن طريق العمل والممارسة الفعلية
                للأنشطة.
            </li>
            <li>تنمية مهارة التعاون والعمل بروح الفريق الواحد.</li>
        </ol>',
            "foundations" => [
                [
                    "title" => "أولاً الأسس الشرعية",
                    "text" => "<ol>
                    <li>الاعتناء بالدليل الشرعي الصحيح
                    </li>
                    <li>الاعتناء بالمعتقد وتفاصيل الأحكام
                    </li>
                    <li>ربط المتعلمين بالعلماء الربانيين الراسخين
                    </li>
                    <li>العلم الشرعي لا ينفصل عن الصلة بالله والخشوع له وتأليهه</li>
                </ol>"
                ],
                [
                    "title" => "ثانياً الأسس التربوية",
                    "text" => "<ol>
                    <li>الاعتناء بالمنهجية العلمية المتبعة في العلوم الشرعية </li>
                    <li>التنوع والمرونة
                    </li>
                    <li>التعلم النشط
                    </li>
                    <li>التغذية الراجعة </li>
                    <li>مراعاة الفروق الفردية
                    </li>
                    <li>تنوع أنماط التعلم
                    </li>
                    <li>مراعاة نضج المتعلم
                    </li>
                    <li>تنمية الدافعية</li>
                </ol>"
                ]
            ],
            "curriculum" => [
                [
                    "title" => "التفسير",
                    "name" => "دليل المعلم لمقرر مختصر تفسير ابن كثير",
                    "hours" => 90
                ]
            ],

        ]);
        $this->form->fill(["data" => $data->toArray(), 'seo' => $data->seo]);
    }
    public function getCancelButtonUrlProperty()
    {
        return static::getUrl();
    }
    protected function getBreadcrumbs(): array
    {
        return [
            url()->current() => __('about_curriculum.plural_label'),
        ];
    }
    protected function getFormSchema(): array
    {
        return [
            Section::make(__('about_curriculum.groups.general'))
                ->columns(1)
                ->schema([
                    FileUpload::make('data.image')
                        ->label(__('about_curriculum.fields.image.label'))
                        ->directory('image')
                        ->required()
                        ->acceptedFileTypes(['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/svg+xml', 'image/webp'])
                        ->maxFiles(1),
                    TextInput::make('data.title')
                        ->label(__('about_curriculum.fields.title.label'))
                        ->required()
                        ->placeholder(__('about_curriculum.fields.title.placeholder')),
                    RichEditor::make('data.description')
                        ->label(__('about_curriculum.fields.description.label'))
                        ->required()
                        ->placeholder(__('about_curriculum.fields.description.placeholder')),
                    FileUpload::make('data.file')
                        ->label(__('about_curriculum.fields.file.label'))
                        ->directory('file')
                        ->enableDownload()
                        ->enableOpen()
                        ->acceptedFileTypes(['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'])
                        ->maxFiles(1),
                    TextInput::make('data.main_goal')
                        ->label(__('about_curriculum.fields.main_goal.label'))
                        ->placeholder(__('about_curriculum.fields.main_goal.placeholder')),
                    RichEditor::make('data.goals')
                        ->label(__('about_curriculum.fields.goals.label'))
                        ->placeholder(__('about_curriculum.fields.goals.placeholder')),
                ]),
            // meta data
            Section::make(__('about_curriculum.groups.methods'))
                ->columns(1)
                ->schema([
                    Repeater::make('data.methods')
                        ->minItems(0)
                        ->label(__('about_curriculum.fields.method.label'))
                        ->schema([
                            TextInput::make('text')
                                ->label(__('about_curriculum.fields.method.text.label'))
                                ->placeholder(__('about_curriculum.fields.method.text.placeholder'))
                                ->required(),
                        ])
                ]),
            // social media
            // branches
            Section::make(__('about_curriculum.groups.foundations'))
                ->columns(1)
                ->schema([
                    Repeater::make('data.foundations')
                        ->minItems(0)
                        ->label(__('about_curriculum.fields.foundations.label'))
                        ->columns(
                            [
                                "default" => 1,
                            ]
                        )
                        ->schema([
                            TextInput::make('title')
                                ->label(__('about_curriculum.fields.title.label'))
                                ->placeholder(__('about_curriculum.fields.title.placeholder'))
                                ->required(),
                            RichEditor::make('text')
                                ->label(__('about_curriculum.fields.text.label'))
                                ->placeholder(__('about_curriculum.fields.text.placeholder')),

                        ]),
                ]),
            Section::make(__('about_curriculum.groups.curriculum'))
                ->columns(1)
                ->schema([
                    Repeater::make('data.curriculum')
                        ->minItems(0)
                        ->label(__('about_curriculum.fields.curriculum.label'))
                        ->columns(
                            [
                                "default" => 1,
                            ]
                        )
                        ->schema([
                            TextInput::make('title')
                                ->label(__('about_curriculum.fields.title.label'))
                                ->placeholder(__('about_curriculum.fields.title.placeholder'))
                                ->required(),
                            TextInput::make('name')
                                ->label(__('about_curriculum.fields.name.label'))
                                ->placeholder(__('about_curriculum.fields.name.placeholder'))
                                ->required(),
                            TextInput::make('hours')
                                ->label(__('about_curriculum.fields.hours.label'))
                                ->placeholder(__('about_curriculum.fields.hours.placeholder'))
                                ->numeric()
                                ->required(),

                        ]),


                ]),
            Section::make("SEO")
                ->columns(1)
                ->schema([
                    get_seo_filament_fields()
                ])
        ];
    }

    public function submit()
    {
        $data = $this->form->getState();

        // dd($data);
        $aboutInfo = AboutCurriculum::firstOrCreate(['id' => 1]);
        $aboutInfo->fill($data['data']);
        $aboutInfo->save();

        $this->form->model($aboutInfo)->saveRelationships();

        Filament::notify('success', __('about_curriculum.messages.updated'));
        return redirect(static::getUrl());
    }

    public static function getCleanOptionString($data): string
    {
        return
            view('filament.components.font-awesome-icon')
            ->with('icon', $data)
            ->render();
    }
}

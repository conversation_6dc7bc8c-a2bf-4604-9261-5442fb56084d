<?php

namespace App\Filament\Pages;

use App\Facades\Panel;
use App\Models\AboutKholasa;
use App\Traits\MultiPanelPage;
use Filament\Facades\Filament;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TagsInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Pages\Page;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Illuminate\Support\Facades\Route;

class AboutKholasaPage extends Page implements HasForms
{
    use MultiPanelPage;
    use InteractsWithForms;
    protected static ?string $navigationIcon = 'heroicon-o-book-open';
    protected static ?string $slug = 'about-kholasa';
    public $data = [];

    // get navigation group function
    protected static function getNavigationGroup(): string
    {
        return __("common.navigation_groups.about_pages");
    }
    // get navigation label function
    protected static function getNavigationLabel(): string
    {
        return __("about_kholasa.plural_label");
    }
    protected  function getTitle(): string
    {
        return __("about_kholasa.plural_label");
    }
    protected static string $view = 'filament.pages.about';

    protected static function shouldRegisterNavigation(): bool
    {
        return Panel::currentPanel() == "kholasah";
    }
    public function mount()
    {
        $data =  AboutKholasa::firstOrCreate([
            "id" => 1,

        ], [
            "id" => 1,
            "title" => "مشروع الخلاصة العلمية",
            "description" => "نظراً لاحتياج شرائح الأمة لمكتبة شاملة ومختصرة لجميع علوم أهل السنة والجماعة تتميز بالموثوقية الشرعية والعلمية وقلة التكلفة، وسعياً للمساهمة في سد هذا الاحتياج المهم في الأمة أعد مركز المنهاج للإشراف والتدريب التربوي مكتبة علمية مركزة تحتوي على: خلاصة سبعة عشر علماً شرعياً، بالإضافة إلى خمسة علوم عصرية تهم المسلم، وفق منهجية محكمة، تساهم في تقريب العلم النافع بالمعايير الآتية: شمولية الكتاب للعلم وسهولة عبارته ، واعتماده على الدليل الصحيح والفهم السليم ، بالإضافة إلى اختصاره.",
            "image" => "",
            "file" => "",
            // "text" => "",
            "methods" => [
                ["text" => "تصفح الخلاصة العلمية من خلال المنصة"],
                ["text" => 'تحميل الخلاصة العلمية "كتاب إلكتروني"'],
                ["text" => 'شراء نسخة من الخلاصة العلمية']
            ],
            "goals_achievement" => '<ul dir="rtl"><li>التعاقد مع الجهات والمؤسسات المتخصصة لتنفيذ المشروع</li><li>عقد شراكات مع الهيئات والجمعيات الإسلامية لتنفيذ المشروع في المدارس والمعاهد التابعة لها .</li></ul>',
            "targets" => [
                [
                    "icon" => "fas fa-university",
                    "text" => "الجامعات والكليات"
                ],
                [
                    "icon" => "fas fa-graduation-cap",
                    "text" => "إنشاء كلية إلكترونية متخصصة بالمشروع"
                ],
                [
                    "icon" => "fas fa-school",
                    "text" => "المدارس والمعاهد"
                ],
                [
                    "icon" => "fas fa-chalkboard-teacher",
                    "text" => "الدورات العلمية المكثفة"
                ]
            ],
            "outcomes" => '<ul dir="rtl"><li>مكتبة ورقية تشمل خلاصة العلوم الشرعية والعصرية</li><li>منصة علمية تقرب العلم للمستفيدين منصة الخلاصة العلمية</li><li>مكتبة إلكترونية</li><li>مناهج دراسية ورقية للجامعات والكليات والمعاهد الإسلامية</li><li>مناهج دراسية إلكترونية للجامعات والكليات الإلكترونية</li><li>حقــــــــائب تدريبية للدورات الشرعية المكثفة</li></ul>',
            "comments" => [],

        ]);
        $this->form->fill(["data" => $data->toArray(), 'seo' => $data->seo]);
    }
    public function getCancelButtonUrlProperty()
    {
        return static::getUrl();
    }
    protected function getBreadcrumbs(): array
    {
        return [
            url()->current() => __('about_kholasa.plural_label'),
        ];
    }
    protected function getFormSchema(): array
    {
        return [
            Section::make(__('about_kholasa.groups.general'))
                ->columns(1)
                ->schema([
                    FileUpload::make('data.image')
                        ->label(__('about_kholasa.fields.image.label'))
                        ->directory('image')
                        ->required()
                        ->acceptedFileTypes(['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/svg+xml', 'image/webp'])
                        ->maxFiles(1),
                    TextInput::make('data.title')
                        ->label(__('about_kholasa.fields.title.label'))
                        ->required()
                        ->placeholder(__('about_kholasa.fields.title.placeholder')),
                    RichEditor::make('data.description')
                        ->label(__('about_kholasa.fields.description.label'))
                        ->required()
                        ->placeholder(__('about_kholasa.fields.description.placeholder')),
                    FileUpload::make('data.file')
                        ->label(__('about_kholasa.fields.file.label'))
                        ->directory('file')
                        ->enableDownload()
                        ->enableOpen()
                        ->acceptedFileTypes(['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'])
                        ->maxFiles(1),
                    RichEditor::make('data.goals_achievement')
                        ->label(__('about_kholasa.fields.goals_achievement.label'))
                        ->placeholder(__('about_kholasa.fields.goals_achievement.placeholder')),
                    RichEditor::make('data.outcomes')
                        ->label(__('about_kholasa.fields.outcomes.label'))
                        ->placeholder(__('about_kholasa.fields.outcomes.placeholder')),
                ]),
            // meta data
            Section::make(__('about_kholasa.groups.methods'))
                ->columns(1)
                ->schema([
                    Repeater::make('data.methods')
                        ->label(__('about_kholasa.fields.method.label'))
                        ->minItems(0)
                        ->schema([
                            TextInput::make('text')
                                ->label(__('about_kholasa.fields.method.text.label'))
                                ->placeholder(__('about_kholasa.fields.method.text.placeholder'))
                                ->required(),
                        ])
                ]),
            // social media
            // branches
            Section::make(__('about_kholasa.groups.targets'))
                ->columns(1)
                ->schema([
                    Repeater::make('data.targets')
                        ->minItems(0)
                        ->label(__('about_kholasa.fields.targets.label'))
                        ->columns(
                            [
                                "default" => 1,
                                "xl" => 3,
                            ]
                        )
                        ->schema([
                            Select::make('icon')
                                ->allowHtml()
                                ->searchable()
                                ->getSearchResultsUsing(function (string $search) {
                                    $array = include(resource_path('icons2.php'));
                                    $icons = collect($array);
                                    if ($search == '') {
                                        return $icons->mapWithKeys(function ($icon) {
                                            return [$icon["title"] => static::getCleanOptionString($icon["title"])];
                                        });
                                    }
                                    $result = $icons->filter(function ($icon) use ($search) {
                                        return str_contains($icon["searchTerms"], $search) || str_contains($icon["title"], $search);
                                    });
                                    return $result->mapWithKeys(function ($icon) {
                                        return [$icon["title"] => static::getCleanOptionString($icon["title"])];
                                    });
                                })
                                ->options(
                                    function () {

                                        $array = include(resource_path('icons2.php'));
                                        $icons = collect($array);

                                        $result = $icons->mapWithKeys(function ($icon) {
                                            return [$icon["title"] => static::getCleanOptionString($icon["title"])];
                                        });
                                        return $result;
                                    }
                                )
                                ->getOptionLabelUsing(function ($value): string {
                                    return static::getCleanOptionString($value);
                                })
                                ->label(__('about_kholasa.fields.target_icon.label'))
                                ->required(),
                            TextInput::make('text')
                                ->label(__('about_kholasa.fields.target_text.label'))
                                ->columnSpan(
                                    [
                                        "default" => 1,
                                        "xl" => 2,
                                    ]
                                )
                                ->placeholder(__('about_kholasa.fields.target_text.placeholder')),

                        ]),
                ]),

            Section::make("SEO")
                ->columns(1)
                ->schema([
                    get_seo_filament_fields()
                ])
        ];
    }

    public function submit()
    {
        $data = $this->form->getState();
        // dd($data);
        $aboutInfo = AboutKholasa::firstOrCreate(['id' => 1]);
        $aboutInfo->fill($data['data']);
        $aboutInfo->save();
        $this->form->model($aboutInfo)->saveRelationships();

        Filament::notify('success', __('about_kholasa.messages.updated'));
        return redirect(static::getUrl());
    }

    public static function getCleanOptionString($data): string
    {
        return
            view('filament.components.font-awesome-icon')
            ->with('icon', $data)
            ->render();
    }
}

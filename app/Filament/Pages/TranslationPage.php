<?php

namespace App\Filament\Pages;

use App\Facades\Panel;
use App\Models\AboutKholasa;
use App\Traits\MultiPanelPage;
use Filament\Facades\Filament;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\TagsInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Pages\Page;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Str;

class TranslationPage extends Page implements HasForms
{
    use MultiPanelPage;
    use InteractsWithForms;
    protected static ?string $navigationIcon = 'heroicon-o-globe-alt';
    protected static ?string $slug = 'translation';
    public $data = [];
    public array $files = [];
    public $currentLang = "ar";

    public $translationData = [];

    // get navigation group function
    protected static function getNavigationGroup(): string
    {
        return __("common.navigation_groups.site_management");
    }
    // get navigation label function
    protected static function getNavigationLabel(): string
    {
        return __("translation.plural_label");
    }
    protected  function getTitle(): string
    {
        return __("translation.plural_label");
    }
    protected static string $view = 'filament.pages.translation';

    protected static function shouldRegisterNavigation(): bool
    {
        return Panel::currentPanel() == "kholasah";
    }
    public function mount()
    {

        $this->files = [
            "home" => __('translation.files.home'),
            "news" => __('translation.files.news'),
        ];

        $this->translationData = include(lang_path('translation_data.php'));

        foreach ($this->translationData as $file_name => $tdata) {
            $data = [];
            $file_path = lang_path("{$this->currentLang}/frontend/{$file_name}.php");
            $translation = [];
            if (file_exists($file_path)) {
                $translation = include($file_path);
            }
            foreach ($tdata['translations'] as $key => $value) {
                $data[$key] = $translation[$key] ?? $value['default'];
            }
            $this->data[$file_name] = $data;
        }



        $this->form->fill($this->data);
    }
    public function getCancelButtonUrlProperty()
    {
        return static::getUrl();
    }
    protected function getFormStatePath(): ?string
    {
        return "data";
    }
    protected function getBreadcrumbs(): array
    {
        return [
            url()->current() => __('about_kholasa.plural_label'),
        ];
    }
    protected function getFormSchema(): array
    {
        $schema = [];
        foreach ($this->translationData as $file_name => $data) {
            $translations = $data['translations'];
            $groupSchema = [];
            foreach ($translations as $key => $value) {
                $groupSchema[] = [
                    "text" =>  TextInput::make($file_name . '.' . $key)
                        ->label($value['label'])
                        ->required(),
                    "video" => FileUpload::make($file_name . '.' . $key)
                        ->label($value['label'])
                        ->acceptedFileTypes(['video/*'])
                        ->directory('translations/videos'),
                    // ->required(),
                    "image" => FileUpload::make($file_name . '.' . $key)
                        ->label($value['label'])
                        ->image()
                        ->directory('translations/images')
                        ->required(),
                    "textarea" => Textarea::make($file_name . '.' . $key)
                        ->label($value['label'])
                        ->rows(2)
                        ->required(),
                    "richeditor" => RichEditor::make($file_name . '.' . $key)
                        ->label($value['label'])
                        ->required(),
                    "url" => TextInput::make($file_name . '.' . $key)
                        ->label($value['label'])
                        ->url(),
                    "seo" =>
                    get_seo_filament_fields($file_name . '.' . $key),

                ][$value['type'] ?? "textarea"];
            }
            $schema[] = Tab::make($data['label'])
                ->columns(1)
                // ->aside()
                // ->group($file_name)
                ->schema($groupSchema);
        }
        return [Tabs::make('Heading')
            ->tabs($schema)];
    }

    public function submit()
    {
        $data = $this->form->getState();
        // dd($data);
        // write to lang file
        foreach ($this->translationData as $file_name => $datos) {
            $fileContent = "<?php\nreturn ";
            $translations = $datos['translations'];
            foreach ($translations as $key => $value) {
                $translations[$key] = $data[$file_name][$key] ?? "";
            }
            $fileContent .= json_encode($translations, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            $fileContent .= ";\n";
            // replace {} with []
            $fileContent = str_replace(["{", "}"], ["[", "]"], $fileContent);
            // replace : with =>
            $fileContent = str_replace("\":", "\" =>", $fileContent);
            // dd($fileContent);
            $file = lang_path("{$this->currentLang}/frontend/{$file_name}.php");
            file_put_contents($file, $fileContent);
        }
        $this->form->fill($data);
        Filament::notify('success', __('translation.messages.updated'));
    }

    public static function getCleanOptionString($data): string
    {
        return
            view('filament.components.font-awesome-icon')
            ->with('icon', $data)
            ->render();
    }
}

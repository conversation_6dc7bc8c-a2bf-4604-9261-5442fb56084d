<?php

namespace App\Filament\Resources;

use App\Facades\Panel;

use App\Filament\Resources\AlbumResource\Pages;
use App\Filament\Resources\AlbumResource\RelationManagers;
use App\Models\Album;
use App\Traits\MultiPanelPage;
use App\Traits\MultiPanelResourse;
use Filament\Forms;
use Filament\Forms\Components\Card;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Filament\Tables\Columns\BooleanColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use phpDocumentor\Reflection\Types\Boolean;

class AlbumResource extends Resource
{
    use MultiPanelResourse;
    protected static ?string $model = Album::class;
    protected static ?int $navigationSort = 3;

    protected static ?string $navigationIcon = 'heroicon-o-photograph';

    protected static ?string $recordTitleAttribute = 'title';

    protected static function getNavigationGroup(): ?string
    {
        return __('common.navigation_groups.media_center');
    }

    public static function getModelLabel(): string
    {
        return __('album.singular_label');
    }
    public static function getPluralModelLabel(): string
    {
        return __('album.plural_label');
    }
    protected static function shouldRegisterNavigation(): bool
    {
        return Panel::currentPanel() == "kholasah";
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Card::make([
                    TextInput::make('title')
                        ->label(__('album.fields.title.label'))
                        ->placeholder(__('album.fields.title.placeholder'))
                        ->required()
                        ->maxLength(255),
                    Textarea::make('description')
                        ->label(__('album.fields.description.label'))
                        ->placeholder(__('album.fields.description.placeholder'))
                        ->required(),
                    FileUpload::make('images')
                        ->label(__('album.fields.images.label'))
                        ->placeholder(__('album.fields.images.placeholder'))
                        ->image()
                        ->minFiles(1)
                        ->directory('albums')
                        ->multiple()
                        ->required(),
                    get_seo_filament_fields(),
                    Toggle::make('is_active')
                        ->dehydrateStateUsing(function ($state) {
                            return $state == true ? '1' : '0';
                        })
                        ->label(__('album.fields.is_active.label'))
                        ->required(),
                ])->columnSpan(
                    [
                        "sm" => 2
                    ]
                ),
                Card::make([
                    Forms\Components\Placeholder::make('created_at')
                        ->label(__('common.fields.created_at'))
                        ->content(fn (?Model $record): string => $record ? $record->created_at->diffForHumans() : '-'),
                    Forms\Components\Placeholder::make('updated_at')
                        ->label(__('common.fields.updated_at'))
                        ->content(fn (?Model $record): string => $record ? $record->updated_at->diffForHumans() : '-'),
                ])->columnSpan(1),
            ])->columns(
                [
                    "sm" => 3,
                    "lg" => null,
                ]
            );
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                //id
                TextColumn::make('id')
                    ->label(__('common.fields.id'))
                    ->sortable(),
                //title
                TextColumn::make('title')
                    ->label(__('album.fields.title.label'))
                    ->searchable()
                    ->sortable(),
                //is_active
                IconColumn::make('is_active')
                    ->boolean()
                    ->label(__('album.fields.is_active.label'))
                    ->searchable()
                    ->sortable(),
                // images count
                TextColumn::make('images')
                    ->label(__('album.fields.images_count.label'))
                    ->getStateUsing(fn (?Model $record): string => $record ? count($record->images) : '-')
                    ->searchable()
                    ->sortable(),
                //created_at
                TextColumn::make('created_at')
                    ->label(__('common.fields.created_at'))
                    ->searchable()
                    ->sortable(),
                //updated_at
                TextColumn::make('updated_at')
                    ->label(__('common.fields.updated_at'))
                    ->searchable()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAlbums::route('/'),
            'create' => Pages\CreateAlbum::route('/create'),
            'view' => Pages\ViewAlbum::route('/{record}'),
            'edit' => Pages\EditAlbum::route('/{record}/edit'),
        ];
    }
}

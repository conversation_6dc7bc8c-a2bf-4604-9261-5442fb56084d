<?php

namespace App\Filament\Resources;

use App\Facades\Panel;

use App\Filament\Resources\BookResource\Pages;
use App\Filament\Resources\BookResource\RelationManagers;
use App\Filament\Resources\BookResource\RelationManagers\TitlesRelationManager;
use App\Models\Book;
use App\Traits\MultiPanelResourse;
use Filament\Forms;
use Filament\Forms\Components\Card;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Mohamedsabil83\FilamentFormsTinyeditor\Components\TinyEditor;

class BookResource extends Resource
{
    use MultiPanelResourse;

    protected static ?string $model = Book::class;

    protected static ?string $navigationIcon = 'heroicon-o-book-open';

    protected static function getNavigationGroup(): ?string
    {
        return __('common.navigation_groups.books');
    }

    public static function getModelLabel(): string
    {
        return __('book.singular_label');
    }
    public static function getPluralModelLabel(): string
    {
        return __('book.plural_label');
    }



    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Card::make([
                    Forms\Components\Select::make('type')
                        ->label(__('book.fields.type.label'))
                        ->default(0)
                        ->placeholder(__('book.fields.type.placeholder'))
                        ->options(__('book.options.type'))
                        ->visible(fn () => Panel::currentPanel() == "religions")
                        ->required(),
                    Forms\Components\TextInput::make('name')
                        ->label(__('book.fields.name.label'))
                        ->placeholder(__('book.fields.name.placeholder'))
                        ->required()
                        ->maxLength(255),
                    Forms\Components\TextInput::make('kholasa_name')
                        ->label(__('book.fields.kholasa_name.label'))
                        ->placeholder(__('book.fields.kholasa_name.placeholder'))
                        ->required()
                        ->maxLength(255),
                    Forms\Components\Textarea::make('overview')
                        ->label(__('book.fields.overview.label'))
                        ->placeholder(__('book.fields.overview.placeholder'))
                        ->required(),
                    TinyEditor::make('methology')
                        ->setRelativeUrls(false)
                        ->minHeight(300)
                        ->label(__('book.fields.methology.label')),

                    Forms\Components\TextInput::make('sort')
                        ->label(__('book.fields.sort.label'))
                        ->placeholder(__('book.fields.sort.placeholder'))
                        ->numeric()
                        ->minValue(0)
                        ->required(),
                    FileUpload::make('cover')
                        ->label(__('book.fields.cover.label'))
                        ->placeholder(__('book.fields.cover.placeholder'))
                        ->directory('books/covers')
                        ->disk('uploads')
                        ->image()
                        ->required(),
                    Repeater::make('files')
                        ->label(__('book.fields.files.label'))
                        ->schema([
                            TextInput::make('name')
                                ->label(__('book.fields.download_name.label'))
                                ->placeholder(__('book.fields.download_name.placeholder'))
                                ->required()
                                ->maxLength(255),
                            Forms\Components\FileUpload::make('file')
                                ->label(__('book.fields.download_file.label'))
                                ->placeholder(__('book.fields.download_file.placeholder'))
                                ->required()
                                ->acceptedFileTypes(['application/pdf', 'application/x-pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'])
                                ->enableDownload(),
                        ]),
                    get_seo_filament_fields(),
                    Forms\Components\Toggle::make('published')
                        ->label(__('book.fields.published.label'))
                        ->default(true)
                        ->required(),
                ])->columnSpan(
                    [
                        "sm" => 2
                    ]
                ),
                Card::make([
                    Forms\Components\Placeholder::make('created_at')
                        ->label(__('common.fields.created_at'))
                        ->content(fn (?Model $record): string => $record ? $record->created_at->diffForHumans() : '-'),
                    Forms\Components\Placeholder::make('updated_at')
                        ->label(__('common.fields.updated_at'))
                        ->content(fn (?Model $record): string => $record ? $record->updated_at->diffForHumans() : '-'),
                ])->columnSpan(1),
            ])->columns(
                [
                    "sm" => 3,
                    "lg" => null,
                ]
            );
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->sortable()
                    ->label(__('common.fields.id')),
                Tables\Columns\TextColumn::make('name')
                    ->label(__('book.fields.name.label'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('kholasa_name')
                    ->label(__('book.fields.kholasa_name.label'))
                    ->sortable()
                    ->searchable(),
                Tables\Columns\BadgeColumn::make('type')
                    ->label(__('book.fields.type.label'))
                    ->enum(__('book.options.type'))
                    ->visible(fn (?Model $record) => Panel::currentPanel() == "religions")
                    ->sortable(),
                Tables\Columns\IconColumn::make('published')
                    ->label(__('book.fields.published.label'))
                    ->boolean()
                    ->sortable(),
                Tables\Columns\TextColumn::make('sort')
                    ->label(__('book.fields.sort.label'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('common.fields.created_at'))
                    ->sortable()
                    ->dateTime("Y-m-d h:i A"),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('common.fields.updated_at'))
                    ->sortable()
                    ->dateTime('Y-m-d h:i A'),
            ])
            ->filters([
                SelectFilter::make('type')
                    ->label(__('book.fields.type.label'))
                    ->options(__('book.options.type')),
            ])
            ->actions([
                Tables\Actions\Action::make('view-title-tree')
                    ->label(__('book.actions.view_titles_tree.label'))
                    ->icon('heroicon-o-menu-alt-1')
                    ->url(fn ($record) => TitleResource::getUrl('tree', $record)),
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                // Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            TitlesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBooks::route('/'),
            'create' => Pages\CreateBook::route('/create'),
            'view' => Pages\ViewBook::route('/{record}'),
            'edit' => Pages\EditBook::route('/{record}/edit'),
            'tree' => Pages\TitleTree::route('/tree'),
        ];
    }
}

<?php

namespace App\Filament\Resources\BookResource\Pages;

use App\Facades\Panel;
use App\Filament\Resources\BookResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateBook extends CreateRecord
{
    protected static string $resource = BookResource::class;
    protected function mutateFormDataBeforeCreate(array $data): array
    {
        if(Panel::currentPanel() != 'religions') {
            $data['type'] = 0;
        }

        return $data;
    }
}

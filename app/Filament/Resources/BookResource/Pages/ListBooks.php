<?php

namespace App\Filament\Resources\BookResource\Pages;

use App\Filament\Resources\BookResource;
use App\Filament\Resources\BookResource\Widgets\BookTileTreeWidget;
use App\Filament\Resources\TitleResource;
use App\Models\Title;
use Filament\Pages\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Tables\Actions\Modal\Actions\Action;

class ListBooks extends ListRecords
{
    protected static string $resource = BookResource::class;

    protected function getActions(): array
    {
        return [
            Actions\Action::make('go_to_last_title')
                ->label(__('book.actions.go_to_last_title.label'))
                ->hidden(function () {
                    $title = auth()->user()->last_title_id;
                    return $title == null;
                })
                ->action(function () {
                })
                ->requiresConfirmation()
                ->modalSubHeading(function () {
                    $title_id = auth()->user()->last_title_id;
                    $title = Title::find($title_id);
                    return __('book.actions.go_to_last_title.sub_heading',['title'=> $title?->name??"", 'book' => $title->book?->name??""]);
                })
                ->modalSubmitAction(
                    Action::make('confirm')
                        ->label(__('book.actions.go_to_last_title.confirm_label'))
                        ->url(function () {
                            $title = auth()->user()->last_title_id;
                            return TitleResource::getUrl('view', $title);
                        })
                    ),
            Actions\CreateAction::make(),
        ];
    }

    protected function getTableReorderColumn(): ?string
    {
        return 'sort';
    }

    protected function getTableRecordsPerPageSelectOptions(): array
    {
        return [5, 10, 25, 50];
    }
}

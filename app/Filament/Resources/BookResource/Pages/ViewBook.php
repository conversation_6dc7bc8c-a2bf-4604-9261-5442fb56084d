<?php

namespace App\Filament\Resources\BookResource\Pages;

use App\Filament\Resources\BookResource;
use App\Filament\Resources\BookResource\Widgets\BookTileTreeWidget;
use App\Filament\Resources\TitleResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewBook extends ViewRecord
{
    protected static string $resource = BookResource::class;

    protected function getActions(): array
    {
        return [
            Actions\Action::make('view-title-tree')
                ->label(__('book.actions.view_titles_tree.label'))
                ->icon('heroicon-o-menu-alt-1')
                ->url(fn ($livewire) => TitleResource::getUrl('tree', $livewire->record)),
            Actions\EditAction::make(),
        ];
    }
    protected function getFooterWidgets(): array
    {
        return [
            // BookTileTreeWidget::class
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            BookResource\Widgets\BookStatsOverview::class,
        ];
    }
}

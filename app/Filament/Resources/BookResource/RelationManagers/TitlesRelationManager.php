<?php

namespace App\Filament\Resources\BookResource\RelationManagers;

use App\Filament\Actions\RMCreateAction;
use App\Filament\Resources\TitleResource;
use App\Models\Book;
use App\Models\Title;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Resources\Table;
use Filament\Tables;
use Filament\Tables\Actions\CreateAction;
use Filament\Tables\Actions\ViewAction;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use AlperenErsoy\FilamentExport\Actions\FilamentExportBulkAction;
use App\Exports\TitlesExport;
use Filament\Forms\Components\Placeholder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Maatwebsite\Excel\Facades\Excel;

class TitlesRelationManager extends RelationManager
{
    protected static string $relationship = 'titles';

    // protected static ?string $recordTitleAttribute = 'name';

    public static function getModelLabel(): string
    {
        return __('title.singular_label');
    }
    public static function getPluralModelLabel(): string
    {
        return __('title.plural_label');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label(__('title.fields.name.label'))
                    ->placeholder(__('title.fields.name.placeholder'))
                    ->required()
                    ->maxLength(255),
                Forms\Components\Select::make('parent_id')
                    ->label(__('title.fields.parent_id.label'))
                    ->placeholder(__('title.fields.parent_id.placeholder'))
                    ->searchable()
                    ->options(fn($livewire) => Title::where("book_id", $livewire->ownerRecord->id)->pluck("name", "id")),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label(__('title.fields.name.label'))
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('parent.name')
                    ->label(__('title.fields.parent_id.label'))
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('order')
                    ->label(__('title.fields.order.label'))
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_parent')
                    ->boolean()
                    ->label(__('title.fields.is_parent.label'))
                    ->counts('subtitles')
                    ->getStateUsing(function (Title $record) {
                        return $record->subtitles_count > 0;
                    }),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                CreateAction::make(),
                \Filament\Tables\Actions\Action::make("export")
                    ->label(__('title.actions.export'))
                    ->action(fn($livewire) => Excel::download(new TitlesExport($livewire->ownerRecord->id), 'titles.xlsx'))
            ])
            ->actions([
                ViewAction::make()
                    ->action(null)
                    ->url(fn($record) => TitleResource::getUrl('view', $record->id)),
                Tables\Actions\EditAction::make()
                    ->action(null)
                    ->url(fn($record) => TitleResource::getUrl('edit', $record->id)),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\Action::make('view_services_statistics')
                    ->label(__('title.actions.view_services_statistics.label'))
                    ->icon('heroicon-o-chart-bar')
                    ->color('success')
                    ->visible(fn($record) => $record->subtitles_count == 0)
                    ->form([
                        Placeholder::make('statistics')
                            ->label('')
                            ->content(
                                fn(Title $record) => str(nl2br($record->getServicesStatistics()))->toHtmlString(),
                            ),
                    ])
                    ->modalActions([])
                    ->action(function (Title $record) {}),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
                FilamentExportBulkAction::make('export')
                    ->label(__('title.actions.export'))
                    ->disableAdditionalColumns(),
            ]);
    }
    protected function getTableRecordsPerPageSelectOptions(): array
    {
        return [5, 10, 25, 50, 100, -1];
    }
}

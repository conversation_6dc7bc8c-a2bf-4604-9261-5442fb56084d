<?php

namespace App\Filament\Resources\BookResource\Widgets;

use App\Models\Model;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Card;

class BookStatsOverview extends BaseWidget
{
    public ?Model $record = null;
    protected function getCards(): array
    {
        $book_id = $this->record->id;
        // text
        $text_services_count = \App\Models\TitleServiceText::whereHas('service.title.book', function ($query) use ($book_id) {
            return $query->where('id', $book_id);
        })->count();
        // video
        $video_services_count =  \App\Models\TitleServiceVideo::whereHas('service.title.book', function ($query) use ($book_id) {
            return $query->where('id', $book_id);
        })->count();
        // qoutation
        $qoutation_service_count = \App\Models\TitleServiceQuotation::whereHas('service.title.book', function ($query) use ($book_id) {
            return $query->where('id', $book_id);
        })->count();
        // reference
        $reference_service_count = \App\Models\TitleServiceReference::whereHas('service.title.book', function ($query) use ($book_id) {
            return $query->where('id', $book_id);
        })->count();
        // activity
        $activity_service_count = \App\Models\TitleServiceActivity::whereHas('service.title.book', function ($query) use ($book_id) {
            return $query->where('id', $book_id);
        })->count();
        // calender
        $calender_service_count = \App\Models\TitleServiceCalender::whereHas('service.title.book', function ($query) use ($book_id) {
            return $query->where('id', $book_id);
        })->count();

        return [
            Card::make('عدد الخدمات النصية', $text_services_count),
            Card::make('عدد الخدمات المرئية', $video_services_count),
            Card::make('عدد خدمات الإقتباسات', $qoutation_service_count),
            Card::make('عدد خدمات المراجع', $reference_service_count),
            Card::make('عدد خدمات الانشطة', $activity_service_count),
            Card::make('عدد خدمات التقويم', $calender_service_count),
        ];
    }
}

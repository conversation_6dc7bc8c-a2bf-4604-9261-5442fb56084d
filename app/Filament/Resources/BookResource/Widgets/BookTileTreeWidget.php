<?php

namespace App\Filament\Resources\BookResource\Widgets;

use App\Filament\Resources\TitleResource;
use App\Models\Title;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use SolutionForest\FilamentTree\Actions\Action;
use SolutionForest\FilamentTree\Actions\ActionGroup;
use SolutionForest\FilamentTree\Actions\DeleteAction;
use SolutionForest\FilamentTree\Actions\EditAction;
use SolutionForest\FilamentTree\Actions\ViewAction;
use SolutionForest\FilamentTree\Widgets\Tree as BaseWidget;

class BookTileTreeWidget extends BaseWidget
{
    public ?Model $record = null;
    protected static string $model = Title::class;

    // protected static int $maxDepth = 999;

    protected ?string $treeTitle = 'BookTileTreeWidget';

    public function getTreeTitle(): ?string
    {
        return __('book.widgets.title_tree.heading');
    }

    protected bool $enableTreeTitle = true;
    protected function getTreeQuery(): Builder
    {
        return $this->getModel()::where('book_id', $this->record->id);
    }

    protected function getFormSchema(): array
    {
        return [
            //
        ];
    }
    public function getNodeCollapsedState(?\Illuminate\Database\Eloquent\Model $record = null): bool
    {
        // All tree nodes will be collapsed by default.
        return true;
    }
    // CUSTOMIZE ICON OF EACH RECORD, CAN DELETE
    // public function getTreeRecordIcon(?\Illuminate\Database\Eloquent\Model $record = null): ?string
    // {
    //     return null;
    // }

    // CUSTOMIZE ACTION OF EACH RECORD, CAN DELETE
    protected function getTreeActions(): array
    {
        return [
            // Action::make('helloWorld')
            //     ->action(function () {
            //         Notification::make()->success()->title('Hello World')->send();
            //     }),
            // // ViewAction::make(),
            EditAction::make()

            ->url(fn ( $record) => TitleResource::getUrl('edit',$record))
            ,
            // ActionGroup::make([

            //     ViewAction::make(),
            //     EditAction::make(),
            // ]),
            // DeleteAction::make(),
        ];
    }
    // OR OVERRIDE FOLLOWING METHODS
    //protected function hasDeleteAction(): bool
    //{
    //    return true;
    //}
    // protected function hasEditAction(): bool
    // {
    //     // return true;
    // }
    //protected function hasViewAction(): bool
    //{
    //    return true;
    //}
}

<?php

namespace App\Filament\Resources;

use App\Facades\Panel;

use App\Filament\Resources\ContactResource\Pages;
use App\Filament\Resources\ContactResource\RelationManagers;
use App\Models\Contact;
use App\Traits\MultiPanelPage;
use App\Traits\MultiPanelResourse;
use Filament\Forms;
use Filament\Forms\Components\Card;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ContactResource extends Resource
{
    use MultiPanelResourse;
    protected static ?string $model = Contact::class;

    protected static ?string $navigationIcon = 'heroicon-o-mail';

    protected static function getNavigationGroup(): ?string
    {
        return __('common.navigation_groups.site_management');
    }

    public static function getModelLabel(): string
    {
        return __('contact.singular_label');
    }
    public static function getPluralModelLabel(): string
    {
        return __('contact.plural_label');
    }
    protected static function shouldRegisterNavigation(): bool
    {
        return Panel::currentPanel() == "kholasah" || Panel::currentPanel() == "religions";
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Card::make([
                    Forms\Components\TextInput::make('name')
                        ->label(__('contact.fields.name.label'))
                        ->required()
                        ->maxLength(255),
                    Forms\Components\TextInput::make('email')
                        ->label(__('contact.fields.email.label'))
                        ->email()
                        ->required(),
                    Forms\Components\Textarea::make('message')
                        ->label(__('contact.fields.message.label')),
                ])->columnSpan(
                    [
                        "sm" => 2
                    ]
                ),
                Card::make([
                    Forms\Components\Placeholder::make('created_at')
                        ->label(__('common.fields.created_at'))
                        ->content(fn(?Model $record): string => $record ? $record->created_at->diffForHumans() : '-'),
                ])->columnSpan(1),
            ])->columns(
                [
                    "sm" => 3,
                    "lg" => null,
                ]
            );
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('id', 'desc')
            ->columns([

                Tables\Columns\TextColumn::make('id')
                    ->label(__('common.fields.id'))
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('name')
                    ->label(__('contact.fields.name.label'))
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('email')
                    ->label(__('contact.fields.email.label'))
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('common.fields.created_at'))
                    ->dateTime("Y-m-d h:i A"),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('common.fields.updated_at'))
                    ->dateTime("Y-m-d h:i A"),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                // Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListContacts::route('/'),
            'create' => Pages\CreateContact::route('/create'),
            'view' => Pages\ViewContact::route('/{record}'),
            'edit' => Pages\EditContact::route('/{record}/edit'),
        ];
    }
}

<?php

namespace App\Filament\Resources;

use App\Facades\Panel;
use App\Filament\Resources\ContentCommentResource\Pages;
use App\Filament\Resources\ContentCommentResource\RelationManagers;
use App\Models\ContentComment;
use App\Models\Title;
use App\Traits\MultiPanelResourse;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\HtmlString;
use Mohamedsabil83\FilamentFormsTinyeditor\Components\TinyEditor;

class ContentCommentResource extends Resource
{
    use MultiPanelResourse;
    protected static ?string $model = ContentComment::class;

    protected static ?string $navigationIcon = 'heroicon-o-annotation';


    protected static function getNavigationGroup(): ?string
    {
        return __('common.navigation_groups.books');
    }

    // protected static function shouldRegisterNavigation(): bool
    // {
    //     return Panel::currentPanel() == "kholasah";
    // }

    public static function getModelLabel(): string
    {
        return __('content_comment.singular_label');
    }
    public static function getPluralModelLabel(): string
    {
        return __('content_comment.plural_label');
    }

    protected static function getNavigationBadge(): ?string
    {
        $count = static::getModel()::where('read', 0)->count();
        return $count > 0 ? $count : null;
    }




    public static function form(Form $form): Form
    {
        return $form
            ->columns(1)
            ->schema([

                //
                Forms\Components\Textarea::make('comment')
                    ->label(__('content_comment.fields.comment.label'))->length('255')
                // ->placeholder(__('book.fields.kholasa_name.placeholder'))
                ,
                // TinyEditor::make("content")
                //     ->setRelativeUrls(false)
                //     // ->showMenuBar()
                //     ->maxHeight(300)
                //     ->

                //     ->afterStateHydrated(function ($set, $state, $record) {

                //         $content = $record->content->text;

                //         $set('content', $content);
                //     })
                Forms\Components\TextInput::make('title')
                    ->label(__('content_comment.fields.title.label'))
                    ->afterStateHydrated(function ($set, $state, $record) {

                        $title_name = Title::find($record->commentable->title_id)->name;

                        $set('title', $title_name);
                    }),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('id', 'desc')
            ->columns([
                //
                Tables\Columns\TextColumn::make('id')
                    ->sortable()
                    ->label(__('common.fields.id')),

                Tables\Columns\TextColumn::make('comment')
                    ->sortable()
                    ->searchable()
                    ->limit(50)
                    ->label(__('content_comment.fields.comment.label')),

                Tables\Columns\TextColumn::make('title')
                    ->sortable()
                    ->getStateUsing(fn ($record) => $record->commentable->title->name)
                    ->url(
                        function ($record) {

                            return  TitleResource::getUrl('view', ['record' => $record->commentable->title_id]);
                        }
                    )
                    ->label(__('content_comment.fields.title.label')),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('common.fields.created_at'))
                    ->sortable()
                //     ->dateTime("Y-m-d h:i A"),
                // Tables\Columns\TextColumn::make('updated_at')
                //     ->label(__('common.fields.updated_at'))
                //     ->sortable()
                //     ->dateTime('Y-m-d h:i A'),

            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->mutateRecordDataUsing(function ($record, $data) {
                        $record->read = 1;
                        $record->save();

                        return $data;
                    }),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\Action::make("content")->label(__("content_comment.fields.move_to_title.label"))
                    ->url(
                        function ($record) {

                            return  TitleResource::getUrl('view', ['record' => $record->commentable->title_id]);
                        }
                    ),


            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListContentComments::route('/'),
            // 'create' => Pages\CreateContentComment::route('/create'),
            // 'view' => Pages\ViewContentComment::route('/{record}'),
            // 'edit' => Pages\EditContentComment::route('/{record}/edit'),
        ];
    }
}

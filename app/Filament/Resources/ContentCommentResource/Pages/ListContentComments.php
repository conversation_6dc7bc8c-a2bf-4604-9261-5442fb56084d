<?php

namespace App\Filament\Resources\ContentCommentResource\Pages;

use App\Filament\Resources\ContentCommentResource;
use Closure;
use Filament\Pages\Actions;
use Filament\Resources\Pages\ListRecords;

class ListContentComments extends ListRecords
{
    protected static string $resource = ContentCommentResource::class;

    protected function getActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }

    
    protected function getTableRecordClassesUsing(): ?Closure
    {
        return function ($record) {
            return !$record->read ? 'font-semibold' : "bg-gray-100";
        };
    }
}

<?php

namespace App\Filament\Resources;

use App\Facades\Panel;

use App\Filament\Resources\CountryResource\Pages;
use App\Filament\Resources\CountryResource\RelationManagers;
use App\Filament\Resources\CountryResource\RelationManagers\CitiesRelationManager;
use App\Models\Country;
use App\Traits\MultiPanelResourse;
use Filament\Forms;
use Filament\Forms\Components\Card;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class CountryResource extends Resource
{
    use MultiPanelResourse;
    protected static ?string $model = Country::class;

    protected static ?string $navigationIcon = 'heroicon-o-flag';

    protected static ?int $navigationSort = 1;

    protected static function getNavigationGroup(): ?string
    {
        return __('common.navigation_groups.sale_point');
    }

    public static function getModelLabel(): string
    {
        return __('country.singular_label');
    }
    public static function getPluralModelLabel(): string
    {
        return __('country.plural_label');
    }
    protected static function shouldRegisterNavigation(): bool
    {
        return Panel::currentPanel() == "kholasah";
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Card::make([
                    Forms\Components\TextInput::make('name')
                        ->label(__('country.fields.name.label'))
                        ->placeholder(__('country.fields.name.placeholder'))
                        ->required()
                        ->maxLength(255),
                ])->columnSpan(
                    [
                        "sm" => 2
                    ]
                ),
                Card::make([
                    Forms\Components\Placeholder::make('created_at')
                        ->label(__('common.fields.created_at'))
                        ->content(fn (?Model $record): string => $record ? $record->created_at->diffForHumans() : '-'),
                    Forms\Components\Placeholder::make('updated_at')
                        ->label(__('common.fields.updated_at'))
                        ->content(fn (?Model $record): string => $record ? $record->updated_at->diffForHumans() : '-'),
                ])->columnSpan(1),
            ])->columns(
                [
                    "sm" => 3,
                    "lg" => null,
                ]
            );
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label(__('common.fields.id')),
                Tables\Columns\TextColumn::make('name')
                    ->label(__('country.fields.name.label')),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('common.fields.created_at'))
                    ->dateTime(),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('common.fields.updated_at'))
                    ->dateTime(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            CitiesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCountries::route('/'),
            'create' => Pages\CreateCountry::route('/create'),
            'view' => Pages\ViewCountry::route('/{record}'),
            'edit' => Pages\EditCountry::route('/{record}/edit'),
        ];
    }
}

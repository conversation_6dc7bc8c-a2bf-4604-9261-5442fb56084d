<?php

namespace App\Filament\Resources;

use App\Facades\Panel;

use App\Filament\Resources\EventResource\Pages;
use App\Filament\Resources\EventResource\RelationManagers;
use App\Models\Event;
use App\Traits\MultiPanelPage;
use App\Traits\MultiPanelResourse;
use Filament\Forms;
use Filament\Forms\Components\Card;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Filament\Tables\Columns\BooleanColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class EventResource extends Resource
{
    use MultiPanelResourse;
    protected static ?string $model = Event::class;

    protected static ?int $navigationSort = 2;

    protected static ?string $navigationIcon = 'heroicon-o-calendar';

    protected static ?string $recordTitleAttribute = 'title';

    protected static function getNavigationGroup(): ?string
    {
        return __('common.navigation_groups.media_center');
    }

    public static function getModelLabel(): string
    {
        return __('event.singular_label');
    }
    public static function getPluralModelLabel(): string
    {
        return __('event.plural_label');
    }
    protected static function shouldRegisterNavigation(): bool
    {
        return Panel::currentPanel() == "kholasah";
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Card::make([
                    // title, overview, image, is_active, detail
                    TextInput::make('title')
                        ->label(__('event.fields.title.label'))
                        ->placeholder(__('event.fields.title.placeholder'))
                        ->required()
                        ->maxLength(255),
                    FileUpload::make('image')
                        ->label(__('event.fields.image.label'))
                        ->placeholder(__('event.fields.image.placeholder'))
                        ->image()
                        ->directory('events')
                        ->required(),
                    Textarea::make('overview')
                        ->label(__('event.fields.overview.label'))
                        ->placeholder(__('event.fields.overview.placeholder'))
                        ->required()
                        ->maxLength(255),
                    RichEditor::make('detail')
                        ->label(__('event.fields.detail.label'))
                        ->placeholder(__('event.fields.detail.placeholder'))
                        ->fileAttachmentsDirectory('news/images')
                        ->fileAttachmentsDisk('uploads')
                        ->required(),

                    get_seo_filament_fields(),
                    Toggle::make('is_active')
                        ->dehydrateStateUsing(function ($state) {
                            return $state == true ? '1' : '0';
                        })
                        ->label(__('event.fields.is_active.label'))
                        ->required(),

                ])->columnSpan(
                    [
                        "sm" => 2
                    ]
                ),
                Card::make([
                    Forms\Components\Placeholder::make('created_at')
                        ->label(__('common.fields.created_at'))
                        ->content(fn (?Model $record): string => $record ? $record->created_at->diffForHumans() : '-'),
                    Forms\Components\Placeholder::make('updated_at')
                        ->label(__('common.fields.updated_at'))
                        ->content(fn (?Model $record): string => $record ? $record->updated_at->diffForHumans() : '-'),
                ])->columnSpan(1),
            ])->columns(
                [
                    "sm" => 3,
                    "lg" => null,
                ]
            );
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                ImageColumn::make('image')
                    ->label(__('event.fields.image.label')),
                TextColumn::make('title')
                    ->label(__('event.fields.title.label'))
                    ->searchable()
                    ->sortable(),
                IconColumn::make('is_active')
                    ->boolean()
                    ->label(__('event.fields.is_active.label')),
                // created_at, updated_at
                TextColumn::make('created_at')
                    ->label(__('common.fields.created_at'))
                    ->dateTime()
                    ->sortable(),
                TextColumn::make('updated_at')
                    ->label(__('common.fields.updated_at'))
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListEvents::route('/'),
            'create' => Pages\CreateEvent::route('/create'),
            'view' => Pages\ViewEvent::route('/{record}'),
            'edit' => Pages\EditEvent::route('/{record}/edit'),
        ];
    }
}

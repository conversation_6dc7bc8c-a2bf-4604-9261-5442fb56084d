<?php

namespace App\Filament\Resources;

use App\Facades\Panel;

use App\Filament\Resources\MailListSubscriptionResource\Pages;
use App\Filament\Resources\MailListSubscriptionResource\RelationManagers;
use App\Models\MailListSubscription;
use App\Traits\MultiPanelResourse;
use Closure;
use Filament\Forms;
use Filament\Forms\Components\Card;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class MailListSubscriptionResource extends Resource
{
    use MultiPanelResourse;
    protected static ?string $model = MailListSubscription::class;

    protected static ?string $navigationIcon = 'heroicon-o-mail-open';

    protected static function getNavigationGroup(): ?string
    {
        return __('common.navigation_groups.site_management');
    }

    public static function getModelLabel(): string
    {
        return __('mail_list_subscription.singular_label');
    }
    public static function getPluralModelLabel(): string
    {
        return __('mail_list_subscription.plural_label');
    }
    protected static function shouldRegisterNavigation(): bool
    {
        return Panel::currentPanel() == "kholasah";
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Card::make([
                    Forms\Components\TextInput::make('name')
                        ->label(__('mail_list_subscription.fields.name.label'))
                        ->required()
                        ->maxLength(255),
                    Forms\Components\TextInput::make('email')
                        ->label(__('mail_list_subscription.fields.email.label'))
                        ->email()
                        ->required()
                        ->maxLength(255),
                    Forms\Components\TextInput::make('country')
                        ->label(__('mail_list_subscription.fields.country.label'))
                        ->required()
                        ->maxLength(255),
                    Forms\Components\TextInput::make('city')
                        ->label(__('mail_list_subscription.fields.city.label'))
                        ->required()
                        ->maxLength(255),
                    Forms\Components\Select::make('type')
                        ->label(__('mail_list_subscription.fields.type.label'))
                        ->options(__('mail_list_subscription.options.type'))
                        ->required(),
                    Forms\Components\TextInput::make('organization_name')
                        ->label(__('mail_list_subscription.fields.organization_name.label'))
                        ->hidden(function (Closure $get) {
                            return $get('type') == 1;
                        })
                        ->maxLength(255),
                    Forms\Components\TextInput::make('student_count')
                        ->hidden(function (Closure $get) {
                            return $get('type') == 1;
                        })
                        ->label(__('mail_list_subscription.fields.student_count.label'))
                        ->maxLength(255),
                ])->columnSpan(
                    [
                        "sm" => 2
                    ]
                ),
                Card::make([
                    Forms\Components\Placeholder::make('created_at')
                        ->label(__('common.fields.created_at'))
                        ->content(fn (?Model $record): string => $record ? $record->created_at->diffForHumans() : '-'),
                ])->columnSpan(1),
            ])->columns(
                [
                    "sm" => 3,
                    "lg" => null,
                ]
            );
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('id', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label(__('common.fields.id'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('name')
                    ->label(__('mail_list_subscription.fields.name.label'))
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('email')
                    ->label(__('mail_list_subscription.fields.email.label'))
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('country')
                    ->label(__('mail_list_subscription.fields.country.label'))
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('city')
                    ->label(__('mail_list_subscription.fields.city.label'))
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('type')
                    ->label(__('mail_list_subscription.fields.type.label'))
                    ->enum(__('mail_list_subscription.options.type')),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('common.fields.created_at'))
                    ->dateTime("Y-m-d h:i A"),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('common.fields.updated_at'))
                    ->dateTime("Y-m-d h:i A"),
            ])
            ->filters([
                SelectFilter::make('type')
                    ->label(__('mail_list_subscription.fields.type.label'))
                    ->options(__('mail_list_subscription.options.type')),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                // Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMailListSubscriptions::route('/'),
            'create' => Pages\CreateMailListSubscription::route('/create'),
            'view' => Pages\ViewMailListSubscription::route('/{record}'),
            'edit' => Pages\EditMailListSubscription::route('/{record}/edit'),
        ];
    }
}

<?php

namespace App\Filament\Resources\MailListSubscriptionResource\Pages;

use App\Filament\Resources\MailListSubscriptionResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\ListRecords;

class ListMailListSubscriptions extends ListRecords
{
    protected static string $resource = MailListSubscriptionResource::class;

    protected function getActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }
}

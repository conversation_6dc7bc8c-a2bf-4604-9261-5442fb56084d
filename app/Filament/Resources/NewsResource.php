<?php

namespace App\Filament\Resources;

use App\Facades\Panel;

use App\Filament\Resources\NewsResource\Pages;
use App\Filament\Resources\NewsResource\RelationManagers;
use App\Models\News;
use App\Traits\MultiPanelResourse;
use Filament\Forms;
use Filament\Forms\Components\Card;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Filament\Tables\Columns\BooleanColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use phpDocumentor\Reflection\Types\Boolean;

class NewsResource extends Resource
{
    use MultiPanelResourse;
    protected static ?string $model = News::class;

    protected static ?string $navigationIcon = 'heroicon-o-newspaper';

    protected static ?string $recordTitleAttribute = 'title';

    protected static ?int $navigationSort = 1;

    protected static function getNavigationGroup(): ?string
    {
        return __('common.navigation_groups.media_center');
    }

    public static function getModelLabel(): string
    {
        return __('news.singular_label');
    }
    public static function getPluralModelLabel(): string
    {
        return __('news.plural_label');
    }
    protected static function shouldRegisterNavigation(): bool
    {
        return Panel::currentPanel() == "kholasah";
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Card::make([
                    // title, overview, detail, is_active, image, created_at, updated_at
                    TextInput::make('title')
                        ->label(__('news.fields.title.label'))
                        ->placeholder(__('news.fields.title.placeholder'))
                        ->required()
                        ->maxLength(255),
                    FileUpload::make('image')
                        ->label(__('news.fields.image.label'))
                        ->placeholder(__('news.fields.image.placeholder'))
                        ->directory('news')
                        ->required(),
                    Textarea::make('overview')
                        ->label(__('news.fields.overview.label'))
                        ->placeholder(__('news.fields.overview.placeholder'))
                        ->required(),
                    RichEditor::make('detail')
                        ->label(__('news.fields.detail.label'))
                        ->placeholder(__('news.fields.detail.placeholder'))
                        ->fileAttachmentsDirectory('news/images')
                        ->fileAttachmentsDisk('uploads')
                        ->required(),
                    get_seo_filament_fields(),
                    Toggle::make('is_active')
                        ->dehydrateStateUsing(function ($state) {
                            return $state == true ? '1' : '0';
                        })
                        ->label(__('news.fields.is_active.label'))
                        ->default(1)
                        ->required(),


                ])->columnSpan(
                    [
                        "sm" => 2
                    ]
                ),
                Card::make([
                    Forms\Components\Placeholder::make('created_at')
                        ->label(__('common.fields.created_at'))
                        ->content(fn (?Model $record): string => $record ? $record->created_at->diffForHumans() : '-'),
                    Forms\Components\Placeholder::make('updated_at')
                        ->label(__('common.fields.updated_at'))
                        ->content(fn (?Model $record): string => $record ? $record->updated_at->diffForHumans() : '-'),
                ])->columnSpan(1),
            ])->columns(
                [
                    "sm" => 3,
                    "lg" => null,
                ]
            );
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('title')
                    ->label(__('news.fields.title.label'))
                    ->searchable()
                    ->sortable(),
                ImageColumn::make('image')
                    ->label(__('news.fields.image.label')),
                IconColumn::make('is_active')
                    ->boolean()
                    ->label(__('news.fields.is_active.label')),
                TextColumn::make('created_at')
                    ->label(__('common.fields.created_at'))
                    ->dateTime('Y-m-d h:i A')
                    ->sortable(),
                TextColumn::make('updated_at')
                    ->label(__('common.fields.updated_at'))
                    ->dateTime('Y-m-d h:i A')
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListNews::route('/'),
            'create' => Pages\CreateNews::route('/create'),
            'view' => Pages\ViewNews::route('/{record}'),
            'edit' => Pages\EditNews::route('/{record}/edit'),
        ];
    }
}

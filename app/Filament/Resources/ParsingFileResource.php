<?php

namespace App\Filament\Resources;

use App\Facades\Panel;

use App\Filament\Resources\ParsingFileResource\Pages;
use App\Filament\Resources\ParsingFileResource\RelationManagers;
use App\Models\Book;
use App\Models\ParsingFile;
use App\Models\Title;
use App\Traits\MultiPanelResourse;
use Filament\Forms;
use Filament\Forms\Components\Card;
use Filament\Forms\Components\FileUpload;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Livewire\TemporaryUploadedFile;

class ParsingFileResource extends Resource
{
    use MultiPanelResourse;
    protected static ?string $model = ParsingFile::class;

    protected static ?string $navigationIcon = 'heroicon-o-collection';

    protected static function getNavigationGroup(): ?string
    {
        return __('common.navigation_groups.parsing');
    }

    public static function getModelLabel(): string
    {
        return __('parsing_file.singular_label');
    }
    public static function getPluralModelLabel(): string
    {
        return __('parsing_file.plural_label');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Card::make([
                    Forms\Components\Select::make('book_id')
                        ->label(__('parsing_file.fields.book_id.label'))
                        ->placeholder(__('parsing_file.fields.book_id.placeholder'))
                        ->options(fn() => Book::pluck("name", "id"))
                        ->reactive()
                        ->required(),

                    Forms\Components\Select::make('title_id')
                        ->label(__('parsing_file.fields.title_id.label'))
                        ->placeholder(__('parsing_file.fields.title_id.placeholder'))
                        ->options(fn($get) => Title::where('book_id', $get('book_id'))->pluck("name", "id"))
                        ->visible(fn($get) => $get('book_id'))
                    // ->required()
                    ,

                    Forms\Components\FileUpload::make('file_name')
                        ->label(__('parsing_file.fields.file.label'))
                        ->placeholder(__('parsing_file.fields.file.placeholder'))
                        ->acceptedFileTypes(['text/html'])
                        ->disk('parsing')
                        ->getUploadedFileNameForStorageUsing(fn(TemporaryUploadedFile $file) => ParsingFile::max('id') + 1 . '.' . $file->extension())
                        ->storeFileNamesIn('name')
                        ->required(),
                    FileUpload::make('images')
                        ->label(__('parsing_file.fields.images.label'))
                        ->placeholder(__('parsing_file.fields.images.placeholder'))
                        ->multiple()
                        ->disk('uploads')
                        ->preserveFilenames()
                        ->getUploadedFileNameForStorageUsing(fn(TemporaryUploadedFile $file) => $file->getClientOriginalName())
                        ->directory(function () {
                            return Panel::currentPanel() . "/books/contents/images/" . ParsingFile::max('id') + 1;
                        }),
                ])->columnSpan(
                    [
                        "sm" => 2
                    ]
                ),
                Card::make([
                    Forms\Components\Placeholder::make('created_at')
                        ->label(__('common.fields.created_at'))
                        ->content(fn(?Model $record): string => $record ? $record->created_at->diffForHumans() : '-'),
                    Forms\Components\Placeholder::make('updated_at')
                        ->label(__('common.fields.updated_at'))
                        ->content(fn(?Model $record): string => $record ? $record->updated_at->diffForHumans() : '-'),
                ])->columnSpan(1),
            ])->columns(
                [
                    "sm" => 3,
                    "lg" => null,
                ]
            );
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('id', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('#')
                    ->sortable(),
                Tables\Columns\TextColumn::make('book.name')
                    ->label(__('parsing_file.fields.book_id.label'))
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('name')
                    ->label(__('parsing_file.fields.name.label'))
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('common.fields.created_at'))
                    ->dateTime("Y-m-d h:i A"),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('common.fields.updated_at'))
                    ->dateTime("Y-m-d h:i A"),
            ])
            ->filters([])
            ->actions([
                Tables\Actions\ViewAction::make(),
                // Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListParsingFiles::route('/'),
            'create' => Pages\CreateParsingFile::route('/create'),
            'view' => Pages\ViewParsingFile::route('/{record}'),
            'edit' => Pages\EditParsingFile::route('/{record}/edit'),
        ];
    }
}

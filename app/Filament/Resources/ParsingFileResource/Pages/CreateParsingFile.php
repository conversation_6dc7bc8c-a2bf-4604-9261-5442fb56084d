<?php

namespace App\Filament\Resources\ParsingFileResource\Pages;

use App\Enums\ParsingStatus;
use App\Filament\Resources\ParsingFileResource;
use App\Models\ParsingFile;
use Filament\Facades\Filament;
use Filament\Notifications\Notification;
use Filament\Pages\Actions;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;

class CreateParsingFile extends CreateRecord
{
    protected static string $resource = ParsingFileResource::class;



    // mount
    // public function mount():void
    // {
    //     // $this->file = ParsingFile::find(11);
    // }
    protected function handleRecordCreation(array $data): Model
    {

        $data['status'] = ParsingStatus::PENDING;
        $data['indexes_extracted'] = false;
        $data['indexes_processed'] = false;
        $record = static::getModel()::create($data);

        return $record;
    }



    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('view', $this->record);
        // return $this->getResource()::getUrl('index');
    }
    protected function getCreatedNotification(): ?Notification
    {
        return null;
    }
}

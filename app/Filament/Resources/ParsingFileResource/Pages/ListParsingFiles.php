<?php

namespace App\Filament\Resources\ParsingFileResource\Pages;

use App\Filament\Resources\ParsingFileResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\ListRecords;

class ListParsingFiles extends ListRecords
{
    protected static string $resource = ParsingFileResource::class;

    protected function getActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}

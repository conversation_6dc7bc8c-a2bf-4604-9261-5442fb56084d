<?php

namespace App\Filament\Resources\ParsingFileResource\Pages;

use App\Enums\ParsingStatus;
use App\Enums\ProcessingStatus;
use App\Filament\Resources\ParsingFileResource;
use App\Filament\Resources\ParsingFileResource\Widgets\ParsingFileErrors;
use App\Models\ParsingFile;
use Filament\Facades\Filament;
use Filament\Pages\Actions;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Support\Facades\Log;

class ViewParsingFile extends ViewRecord
{
    protected static string $resource = ParsingFileResource::class;
    protected static string $view = 'filament.pages.view-parsing-file';
    public bool $isProcessing = false;
    public string $currentAction = 'finished';
    public string $message = '';
    public $file;
    public $startActionStatus = ProcessingStatus::PENDING;
    public $processIndexesActionStatus = ProcessingStatus::PENDING;
    public $finishActionStatus = ProcessingStatus::PENDING;
    protected function getActions(): array
    {
        return [
            // Actions\EditAction::make(),
        ];
    }
    public function mount($record): void
    {
        static::authorizeResourceAccess();

        $this->record = $this->resolveRecord($record);
        $this->file = $this->resolveRecord($record);

        abort_unless(static::getResource()::canView($this->getRecord()), 403);

        $this->fillForm();
        if ($this->record->status == ParsingStatus::PENDING) {
            parsing()->setFile($this->file)->convertFileEncoding($this->file->fullPath);
            $this->isProcessing = true;
            $this->currentAction = "start";
            $this->startActionStatus = ProcessingStatus::PROCESSING;
        } else if ($this->record->status == ParsingStatus::PARSED) {
            $this->startActionStatus = ProcessingStatus::SUCCESS;
            $this->processIndexesActionStatus = ProcessingStatus::SUCCESS;
            $this->finishActionStatus = ProcessingStatus::SUCCESS;
            $this->isProcessing = false;
            $this->currentAction = "finished";
        } else {
            $this->startActionStatus = $this->record->indexes_extracted ? ProcessingStatus::SUCCESS : ProcessingStatus::FAILED;
            $this->processIndexesActionStatus = $this->record->indexes_processed ? ProcessingStatus::SUCCESS : ProcessingStatus::FAILED;
            $this->finishActionStatus = ProcessingStatus::SUCCESS;
            $this->isProcessing = false;
            $this->currentAction = "finished";
        }
    }
    public function parse($action)
    {
        // start processIndexes finish
        if ($this->isProcessing == false) {
            return;
        }
        $this->file = ParsingFile::find($this->record->id);
        $res = parsing()
            ->setFile($this->file)
            ->setAction($action)
            ->process();
        $this->message = $res["data"] ?? null;
        if ($res["next"]) {
            if ($action == "start") {
                $this->startActionStatus = ProcessingStatus::SUCCESS;
                $this->processIndexesActionStatus = ProcessingStatus::PROCESSING;
                $this->currentAction = "processIndexes";
            } elseif ($action == "processIndexes") {
                $this->processIndexesActionStatus = ProcessingStatus::SUCCESS;
                $this->finishActionStatus = ProcessingStatus::PROCESSING;
                $this->currentAction = "finish";
            } elseif ($action == "finish") {
                $this->finishActionStatus = ProcessingStatus::SUCCESS;
                $this->isProcessing = false;
                $this->currentAction = "finished";
            }
        }
    }

    protected function getFooterWidgets(): array
    {
        return [
            ParsingFileErrors::class,
        ];
    }
    protected function getFooterWidgetsColumns(): int | array
    {
        return 1;
    }
}

<?php

namespace App\Filament\Resources\ParsingFileResource\Widgets;

use App\Models\ParsingFileError;
use Closure;
use Filament\Tables;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class ParsingFileErrors extends BaseWidget
{
    public ?Model $record = null;
    protected function getTableQuery(): Builder
    {
        return ParsingFileError::where('file_id', $this->record->id);
    }

    protected function getTableColumns(): array
    {
        return [
            Tables\Columns\TextColumn::make('id')
                ->label('#'),
            Tables\Columns\TextColumn::make('error')
                ->label(__('parsing_file_error.fields.error.label'))
                ->searchable(),
            Tables\Columns\TextColumn::make('source')
                ->label(__('parsing_file_error.fields.source.label'))
                ->searchable(),
            Tables\Columns\TextColumn::make('other')
                ->label(__('parsing_file_error.fields.other.label'))
                ->searchable(),
        ];
    }

    protected function getTableHeading(): string | Htmlable | Closure | null
    {
        return "أخطاء الملف";
    }

    protected function getDefaultTableSortColumn(): ?string
    {
        return 'id';
    }

    protected function getDefaultTableSortDirection(): ?string
    {
        return 'desc';
    }
}

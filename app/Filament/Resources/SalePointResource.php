<?php

namespace App\Filament\Resources;

use App\Facades\Panel;

use App\Filament\Resources\SalePointResource\Pages;
use App\Filament\Resources\SalePointResource\RelationManagers;
use App\Models\City;
use App\Models\Country;
use App\Models\SalePoint;
use App\Traits\MultiPanelResourse;
use Closure;
use Filament\Forms;
use Filament\Forms\Components\Card;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SalePointResource extends Resource
{
    use MultiPanelResourse;
    protected static ?string $model = SalePoint::class;

    protected static ?string $navigationIcon = 'heroicon-o-location-marker';

    protected static function getNavigationGroup(): ?string
    {
        return __('common.navigation_groups.sale_point');
    }

    public static function getModelLabel(): string
    {
        return __('sale_point.singular_label');
    }
    public static function getPluralModelLabel(): string
    {
        return __('sale_point.plural_label');
    }

    protected static function shouldRegisterNavigation(): bool
    {
        return Panel::currentPanel() == "kholasah";
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Card::make([
                    Forms\Components\TextInput::make('place')
                        ->label(__('sale_point.fields.place.label'))
                        ->placeholder(__('sale_point.fields.place.placeholder'))

                        ->maxLength(255),
                    // Forms\Components\Select::make('country_id')
                    //     ->label(__('sale_point.fields.country_id.label'))
                    //     ->placeholder(__('sale_point.fields.country_id.placeholder'))

                    //     ->reactive()
                    //     ->options(fn (): array => Country::all()->pluck('name', 'id')->toArray()),
                    // Forms\Components\Select::make('city_id')
                    //     ->label(__('sale_point.fields.city_id.label'))
                    //     ->placeholder(__('sale_point.fields.city_id.placeholder'))

                    //     ->options(fn (Closure $get): array => City::where('country_id', $get('country_id'))->pluck('name', 'id')->toArray()),
                    Forms\Components\TextInput::make('phone_number')
                        ->label(__('sale_point.fields.phone_number.label'))
                        ->placeholder(__('sale_point.fields.phone_number.placeholder'))
                        ->tel()

                        ->maxLength(255),
                    // Forms\Components\Textarea::make('address')
                    //     ->label(__('sale_point.fields.address.label'))
                    //     ->placeholder(__('sale_point.fields.address.placeholder')),
                    // Forms\Components\TextInput::make('map_url')
                    //     ->label(__('sale_point.fields.map_url.label'))
                    //     ->placeholder(__('sale_point.fields.map_url.placeholder'))
                    //     ->url()

                    // ->maxLength(255),
                ])->columnSpan(
                    [
                        "sm" => 2
                    ]
                ),
                Card::make([
                    Forms\Components\Placeholder::make('created_at')
                        ->label(__('common.fields.created_at'))
                        ->content(fn (?Model $record): string => $record ? $record->created_at->diffForHumans() : '-'),
                    Forms\Components\Placeholder::make('updated_at')
                        ->label(__('common.fields.updated_at'))
                        ->content(fn (?Model $record): string => $record ? $record->updated_at->diffForHumans() : '-'),
                ])->columnSpan(1),
            ])->columns(
                [
                    "sm" => 3,
                    "lg" => null,
                ]
            );
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label(__('common.fields.id'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('place')
                    ->label(__('sale_point.fields.place.label'))
                    ->searchable()
                    ->sortable(),
                // Tables\Columns\TextColumn::make('country.name')
                //     ->label(__('sale_point.fields.country_id.label'))
                //     ->searchable()
                //     ->sortable(),
                // Tables\Columns\TextColumn::make('city.name')
                //     ->label(__('sale_point.fields.city_id.label'))
                //     ->searchable()
                //     ->sortable(),
                Tables\Columns\TextColumn::make('phone_number')
                    ->label(__('sale_point.fields.phone_number.label'))
                    ->sortable(),
                // Tables\Columns\IconColumn::make('map_url')
                //     ->options(['heroicon-o-map'])
                //     ->label(__('sale_point.fields.map_url.label'))
                //     ->colors(['primary'])
                //     ->url(fn (?Model $record): string => $record && $record->map_url ? $record->map_url : '#')
                //     ->openUrlInNewTab()
                //     ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('common.fields.created_at'))
                    ->dateTime("Y-m-d h:i A"),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('common.fields.updated_at'))
                    ->dateTime("Y-m-d h:i A"),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSalePoints::route('/'),
            'create' => Pages\CreateSalePoint::route('/create'),
            'view' => Pages\ViewSalePoint::route('/{record}'),
            'edit' => Pages\EditSalePoint::route('/{record}/edit'),
        ];
    }
}

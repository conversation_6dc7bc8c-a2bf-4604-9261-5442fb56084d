<?php

namespace App\Filament\Resources;

use App\Facades\Panel;

use App\Filament\Resources\ServiceOrderResource\Pages;
use App\Filament\Resources\ServiceOrderResource\RelationManagers;
use App\Models\Service;
use App\Models\ServiceOrder;
use App\Traits\MultiPanelResourse;
use Filament\Forms;
use Filament\Forms\Components\Card;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ServiceOrderResource extends Resource
{
    use MultiPanelResourse;
    protected static ?string $model = ServiceOrder::class;

    protected static ?string $navigationIcon = 'heroicon-o-clipboard-list';

    protected static ?int $navigationSort = 2;

    protected static function getNavigationGroup(): ?string
    {
        return __('common.navigation_groups.services');
    }

    public static function getModelLabel(): string
    {
        return __('service_order.singular_label');
    }
    public static function getPluralModelLabel(): string
    {
        return __('service_order.plural_label');
    }
    protected static function shouldRegisterNavigation(): bool
    {
        return Panel::currentPanel() == "kholasah";
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Card::make([
                    Forms\Components\Select::make('service_id')
                        ->label(__('service_order.fields.service_id.label'))
                        ->options(function () {
                            return Service::all()->pluck('name', 'id');
                        })
                        ->required(),
                    Forms\Components\TextInput::make('name')
                        ->label(__('service_order.fields.name.label'))
                        ->required()
                        ->maxLength(255),
                    Forms\Components\TextInput::make('country')
                        ->label(__('service_order.fields.country.label'))
                        ->maxLength(255),
                    Forms\Components\TextInput::make('city')
                        ->label(__('service_order.fields.city.label'))
                        ->maxLength(255),
                    Forms\Components\TextInput::make('email')
                        ->label(__('service_order.fields.email.label'))
                        ->email()
                        ->maxLength(255),
                    Forms\Components\TextInput::make('phone_number')
                        ->label(__('service_order.fields.phone_number.label'))
                        ->tel()
                        ->maxLength(255),
                    Forms\Components\TextInput::make('organization_name')
                        ->label(__('service_order.fields.organization_name.label'))
                        ->maxLength(255),
                    Forms\Components\TextInput::make('student_count')
                        ->label(__('service_order.fields.student_count.label'))
                        ->numeric()
                        ->maxLength(255),
                    Forms\Components\Textarea::make('detail')
                        ->label(__('service_order.fields.detail.label')),
                ])->columnSpan(
                    [
                        "sm" => 2
                    ]
                ),
                Card::make([
                    Forms\Components\Placeholder::make('created_at')
                        ->label(__('common.fields.created_at'))
                        ->content(fn (?Model $record): string => $record ? $record->created_at->diffForHumans() : '-'),
                    Forms\Components\Placeholder::make('updated_at')
                        ->label(__('common.fields.updated_at'))
                        ->content(fn (?Model $record): string => $record ? $record->updated_at->diffForHumans() : '-'),
                ])->columnSpan(1),
            ])->columns(
                [
                    "sm" => 3,
                    "lg" => null,
                ]
            );
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('id', 'asc')
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label(__('common.fields.id'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('service.name')
                    ->searchable()
                    ->label(__('service_order.fields.service_id.label')),
                Tables\Columns\TextColumn::make('name')
                    ->label(__('service_order.fields.name.label'))
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('country')
                    ->label(__('service_order.fields.country.label'))
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('city')
                    ->label(__('service_order.fields.city.label'))
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('email')
                    ->label(__('service_order.fields.email.label'))
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('phone_number')
                    ->label(__('service_order.fields.phone_number.label'))
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('organization_name')
                    ->label(__('service_order.fields.organization_name.label'))
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('student_count')
                    ->label(__('service_order.fields.student_count.label'))
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('detail')
                    ->label(__('service_order.fields.detail.label'))
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime("Y-m-d h:i A"),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime("Y-m-d h:i A"),
            ])
            ->filters([
                SelectFilter::make('service_id')
                    ->label(__('service_order.fields.service_id.label'))
                    ->options(function () {
                        return Service::all()->pluck('name', 'id');
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListServiceOrders::route('/'),
            'create' => Pages\CreateServiceOrder::route('/create'),
            'view' => Pages\ViewServiceOrder::route('/{record}'),
            'edit' => Pages\EditServiceOrder::route('/{record}/edit'),
        ];
    }
}

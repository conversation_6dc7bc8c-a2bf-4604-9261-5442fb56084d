<?php

namespace App\Filament\Resources;

use App\Facades\Panel;

use App\Filament\Resources\ServiceResource\Pages;
use App\Filament\Resources\ServiceResource\RelationManagers;
use App\Forms\Components\FontAwsomePicker;
use App\Models\Service;
use App\Traits\MultiPanelResourse;
use Filament\Forms;
use Filament\Forms\Components\Card;
use Filament\Forms\Components\Select;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Arr;
use Illuminate\Support\HtmlString;

class ServiceResource extends Resource
{
    use MultiPanelResourse;
    protected static ?string $model = Service::class;

    protected static ?string $navigationIcon = 'heroicon-o-light-bulb';

    protected static ?string $recordTitleAttribute = 'name';

    protected static ?int $navigationSort = 1;

    protected static function getNavigationGroup(): ?string
    {
        return __('common.navigation_groups.services');
    }

    public static function getModelLabel(): string
    {
        return __('service.singular_label');
    }
    public static function getPluralModelLabel(): string
    {
        return __('service.plural_label');
    }
    protected static function shouldRegisterNavigation(): bool
    {
        return Panel::currentPanel() == "kholasah";
    }
    public static function form(Form $form): Form
    {

        return $form
            ->schema([
                Card::make([
                    Forms\Components\TextInput::make('name')
                        ->label(__('service.fields.name.label'))
                        ->placeholder(__('service.fields.name.placeholder'))
                        ->required()
                        ->maxLength(255),
                    Select::make('icon')
                        ->allowHtml()
                        ->searchable()
                        ->getSearchResultsUsing(function (string $search) {
                            $array = include(resource_path('icons2.php'));
                            $icons = collect($array);
                            if ($search == '') {
                                return $icons->mapWithKeys(function ($icon) {
                                    return [$icon["title"] => static::getCleanOptionString($icon["title"])];
                                });
                            }
                            $result = $icons->filter(function ($icon) use ($search) {
                                return str_contains($icon["searchTerms"], $search) || str_contains($icon["title"], $search);
                            });
                            return $result->mapWithKeys(function ($icon) {
                                return [$icon["title"] => static::getCleanOptionString($icon["title"])];
                            });
                        })
                        ->options(
                            function () {

                                $array = include(resource_path('icons2.php'));
                                $icons = collect($array);

                                $result = $icons->mapWithKeys(function ($icon) {
                                    return [$icon["title"] => static::getCleanOptionString($icon["title"])];
                                });
                                return $result;
                            }
                        )
                        ->getOptionLabelUsing(function ($value): string {
                            return static::getCleanOptionString($value);
                        })
                        ->label(__('service.fields.icon.label'))
                        ->required(),
                    Forms\Components\FileUpload::make('image')
                        ->label(__('service.fields.image.label'))
                        ->placeholder(__('service.fields.image.placeholder'))
                        ->required()
                        ->image(),
                    Forms\Components\Textarea::make('description')
                        ->label(__('service.fields.description.label'))
                        ->placeholder(__('service.fields.description.placeholder')),
                    Forms\Components\Toggle::make('soon')
                        ->label(__('service.fields.soon.label'))
                        ->default(false),
                ])->columnSpan(
                    [
                        "sm" => 2
                    ]
                ),
                Card::make([
                    Forms\Components\Placeholder::make('created_at')
                        ->label(__('common.fields.created_at'))
                        ->content(fn (?Model $record): string => $record ? $record->created_at->diffForHumans() : '-'),
                    Forms\Components\Placeholder::make('updated_at')
                        ->label(__('common.fields.updated_at'))
                        ->content(fn (?Model $record): string => $record ? $record->updated_at->diffForHumans() : '-'),
                ])->columnSpan(1),
            ])->columns(
                [
                    "sm" => 3,
                    "lg" => null,
                ]
            );
    }
    public static function getCleanOptionString($data): string
    {
        return
            view('filament.components.font-awesome-icon')
            ->with('icon', $data)
            ->render();
    }
    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label(__('common.fields.id'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('name')
                    ->label(__('service.fields.name.label'))
                    ->sortable(),
                Tables\Columns\ViewColumn::make('icon')
                    ->label(__('service.fields.icon.label'))
                    ->View("filament.components.columns.fa-icon"),
                Tables\Columns\ImageColumn::make('image')
                    ->label(__('service.fields.image.label')),
                Tables\Columns\BooleanColumn::make('soon')
                    ->label(__('service.fields.soon.label')),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('common.fields.created_at'))
                    ->dateTime("Y-m-d h:i A"),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('common.fields.updated_at'))
                    ->dateTime("Y-m-d h:i A"),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListServices::route('/'),
            'create' => Pages\CreateService::route('/create'),
            'view' => Pages\ViewService::route('/{record}'),
            'edit' => Pages\EditService::route('/{record}/edit'),
        ];
    }
}

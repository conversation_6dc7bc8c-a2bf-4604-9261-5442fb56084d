<?php

namespace App\Filament\Resources;

use App\Facades\Panel;

use App\Filament\Resources\SocialMediaResource\Pages;
use App\Filament\Resources\SocialMediaResource\RelationManagers;
use App\Models\SocialMedia;
use App\Traits\MultiPanelResourse;
use Filament\Forms;
use Filament\Forms\Components\Card;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\View;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Filament\Tables\Columns\BooleanColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ViewColumn;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use phpDocumentor\Reflection\Types\Boolean;

class SocialMediaResource extends Resource
{
    use MultiPanelResourse;
    protected static ?string $model = SocialMedia::class;

    protected static ?string $navigationIcon = 'heroicon-o-speakerphone';

    protected static function getNavigationGroup(): ?string
    {
        return __('common.navigation_groups.site_management');
    }

    public static function getModelLabel(): string
    {
        return __('social_media.singular_label');
    }
    public static function getPluralModelLabel(): string
    {
        return __('social_media.plural_label');
    }
    protected static function shouldRegisterNavigation(): bool
    {
        return Panel::currentPanel() == "kholasah";
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Card::make(
                    [
                        Select::make('icon')
                            ->allowHtml()
                            ->searchable()
                            ->getSearchResultsUsing(function (string $search) {
                                $array = include(resource_path('icons2.php'));
                                $icons = collect($array);
                                if ($search == '') {
                                    return $icons->mapWithKeys(function ($icon) {
                                        return [$icon["title"] => static::getCleanOptionString($icon["title"])];
                                    });
                                }
                                $result = $icons->filter(function ($icon) use ($search) {
                                    return str_contains($icon["searchTerms"], $search) || str_contains($icon["title"], $search);
                                });
                                return $result->mapWithKeys(function ($icon) {
                                    return [$icon["title"] => static::getCleanOptionString($icon["title"])];
                                });
                            })
                            ->options(
                                function () {

                                    $array = include(resource_path('icons2.php'));
                                    $icons = collect($array);

                                    $result = $icons->mapWithKeys(function ($icon) {
                                        return [$icon["title"] => static::getCleanOptionString($icon["title"])];
                                    });
                                    return $result;
                                }
                            )
                            ->getOptionLabelUsing(function ($value): string {
                                return static::getCleanOptionString($value);
                            })
                            ->label(__('social_media.fields.icon.label'))
                            ->required(),
                        Forms\Components\TextInput::make('url')
                            ->label(__('social_media.fields.url.label'))
                            ->placeholder(__('social_media.fields.url.placeholder'))
                            ->required()
                            ->maxLength(255),
                        Forms\Components\Toggle::make('active')
                            ->label(__('social_media.fields.active.label'))
                            ->required(),
                    ]
                )->columnSpan(
                    [
                        "sm" => 2
                    ]
                ),
                Card::make([
                    Forms\Components\Placeholder::make('created_at')
                        ->label(__('common.fields.created_at'))
                        ->content(fn (?Model $record): string => $record ? $record->created_at->diffForHumans() : '-'),
                    Forms\Components\Placeholder::make('updated_at')
                        ->label(__('common.fields.updated_at'))
                        ->content(fn (?Model $record): string => $record ? $record->updated_at->diffForHumans() : '-'),
                ])->columnSpan(1),
            ])->columns(
                [
                    "sm" => 3,
                    "lg" => null,
                ]
            );
    }
    public static function getCleanOptionString($data): string
    {
        return
            view('filament.components.font-awesome-icon')
            ->with('icon', $data)
            ->render();
    }
    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')
                    ->label(__('common.fields.id'))
                    ->sortable(),
                Tables\Columns\ViewColumn::make('icon')
                    ->label(__('social_media.fields.icon.label'))
                    ->View("filament.components.columns.fa-icon"),
                TextColumn::make('url')
                    ->label(__('social_media.fields.url.label'))
                    ->url(function (?Model $record): ?string {
                        return $record ? $record->url : null;
                    })
                    ->openUrlInNewTab()
                    ->sortable(),
                BooleanColumn::make('active')
                    ->label(__('social_media.fields.active.label'))
                    ->sortable(),
                TextColumn::make('created_at')
                    ->label(__('common.fields.created_at'))
                    ->dateTime('Y-m-d h:i A'),
                TextColumn::make('updated_at')
                    ->label(__('common.fields.updated_at'))
                    ->dateTime('Y-m-d h:i A'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSocialMedia::route('/'),
            'create' => Pages\CreateSocialMedia::route('/create'),
            'view' => Pages\ViewSocialMedia::route('/{record}'),
            'edit' => Pages\EditSocialMedia::route('/{record}/edit'),
        ];
    }
}

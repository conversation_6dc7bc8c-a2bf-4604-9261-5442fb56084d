<?php

namespace App\Filament\Resources;

use App\Facades\Panel;

use App\Filament\Resources\TitleResource\Pages;
use App\Filament\Resources\TitleResource\RelationManagers;
use App\Filament\Resources\TitleResource\RelationManagers\ServicesRelationManager;
use App\Filament\Resources\TitleResource\RelationManagers\VersesRelationManager;
use App\Models\Book;
use App\Models\Quran;
use App\Models\Title;
use App\Traits\MultiPanelResourse;
use Filament\Forms;
use Filament\Forms\Components\Card;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\DB;
use Mohamedsabil83\FilamentFormsTinyeditor\Components\TinyEditor;
use Yepsua\Filament\Forms\Components\Rating;

class TitleResource extends Resource
{
    use MultiPanelResourse;
    protected static ?string $model = Title::class;

    protected static ?string $navigationIcon = 'heroicon-o-film';

    protected static ?string $recordTitleAttribute = 'name';

    protected static ?int $navigationSort = 4;

    protected static function getNavigationGroup(): ?string
    {
        return __('common.navigation_groups.media_center');
    }

    public static function getModelLabel(): string
    {
        return __('title.singular_label');
    }
    public static function getPluralModelLabel(): string
    {
        return __('title.plural_label');
    }
    public static function form(Form $form): Form
    {

        return $form
            ->schema([
                Card::make([
                    Forms\Components\Select::make('book_id')
                        ->label(__('title.fields.book_id.label'))
                        ->placeholder(__('title.fields.book_id.placeholder'))
                        ->options(fn() => Book::all()->pluck('name', 'id'))
                        ->reactive()
                        ->required(),
                    Forms\Components\TextInput::make('name')
                        ->label(__('title.fields.name.label'))
                        ->placeholder(__('title.fields.name.placeholder'))
                        ->required()
                        ->maxLength(255),
                    Forms\Components\Select::make('parent_id')
                        ->label(__('title.fields.parent_id.label'))
                        ->placeholder(__('title.fields.parent_id.placeholder'))
                        ->options(
                            fn($record, $get) => Title::where('book_id', $get('book_id'))
                                ->where('id', '!=', $record->id ?? 0)->pluck('name', 'id')
                        ),


                    Repeater::make('content')
                        ->hidden(function ($record) {
                            return $record?->subtitles()->count() > 0 ?? true;
                        })
                        ->label(__('title.fields.content.label'))
                        ->afterStateHydrated(function (Repeater $component, $state, $record) {
                            $content = $record->content->firstOrCreate([
                                'title_id' => $record->id,
                                "book_id" => $record->book_id,
                                "text" => ""
                            ]);
                            $component->state($content);
                        })
                        // ->mutateRelationshipDataBeforeFillUsing(function ($record, $data) {
                        //     $content = $record->content;
                        //     if (!$content) {
                        //         $record->content()->create([
                        //             "title_id" => $record->id,
                        //             "text" => "",
                        //             "book_id" => $record->book_id,
                        //         ]);
                        //     }
                        //     return $data;
                        // })
                        ->mutateRelationshipDataBeforeSaveUsing(function ($record, $data) {
                            // dd($data);
                            $data['text'] = quran()->decode($data['text'], "quran_othmany_v1", "quranSimpleEnhanced");
                            $data["text"] = footnote()->extract($data["text"]);
                            return $data;
                        })

                        ->minItems(1)
                        ->maxItems(1)
                        ->defaultItems(1)
                        ->relationship()
                        ->disableItemCreation()
                        ->disableItemDeletion(
                            function ($state) {
                                return count($state) == 1;
                            }
                        )
                        ->disableItemMovement()
                        ->schema([
                            TinyEditor::make("text")
                                ->setRelativeUrls(false)
                                // ->showMenuBar()
                                ->maxHeight(300)
                                ->label(__('title.fields.text.label')),
                        ]),

                    //content rating
                    Rating::make('rating')
                        ->label(__('title.fields.rating.label'))
                        ->afterStateHydrated(
                            function ($record, $set) {
                                $rating = $record->rates;
                                $sum = $rating?->sum('stars') ?? 0;
                                $count = $rating?->count('stars') ?? 0;


                                if ($count) {
                                    $set('rating', round(($sum / $count) / 5) * 5);
                                }
                            }
                        )->visibleOn('view')
                        ->hidden(function ($record) {
                            return $record?->subtitles()->count() > 0 ?? true;
                        })
                        ->color('primary'),
                    //verses

                    Section::make(__("title.fields.verses.label"))
                        ->label(__("title.fields.verses.label"))
                        ->hidden(function (\Closure $get) {
                            return !($get("book_id") == 115 || $get("book_id") == 122);
                        })

                        ->schema([
                            Forms\Components\Select::make('sora_num')
                                ->label(__("title.fields.sora_num.label"))
                                ->options(
                                    function () {
                                        return \App\Models\Quran::groupBy(["sora_num", 'sora_name'])->pluck("sora_name", "sora_num");
                                    }
                                )
                                ->reactive()
                                ->columnSpanFull(),


                            Forms\Components\Select::make('from_verse')
                                ->label(__("title.fields.from_verse.label"))
                                ->searchable()
                                ->options(
                                    function (\Closure $get, $state) {
                                        return cache()->remember('sora_' . $get("sora_num") . '_ayat', 10 * 60, function () use ($get) {
                                            $options   = \App\Models\Quran::select(['id', DB::raw("CONCAT(aya_num,' - ',aya_with_diac) AS text")])->where("sora_num", $get("sora_num"))->pluck("text", "id");
                                            return $options;
                                        });
                                    }
                                )
                                ->optionsLimit(function ($get) {
                                    return Quran::where("sora_num", $get("sora_num"))->count();
                                })
                                ->columnSpanFull(),
                            Forms\Components\Select::make('to_verse')
                                ->label(__("title.fields.to_verse.label"))
                                ->searchable()
                                ->optionsLimit(function ($get) {
                                    return Quran::where("sora_num", $get("sora_num"))->count();
                                })
                                ->options(
                                    function (\Closure $get, $state) {
                                        return cache()->remember('sora_' . $get("sora_num") . '_ayat', 10 * 60, function () use ($get) {
                                            $options   = \App\Models\Quran::select(['id', DB::raw("CONCAT(aya_num,' - ',aya_with_diac) AS text")])->where("sora_num", $get("sora_num"))->pluck("text", "id");
                                            return $options;
                                        });
                                    }
                                )
                                ->columnSpanFull(),

                            // Forms\Components\Placeholder::make('help_text')
                            //     ->hidden(function (\Closure $get) {
                            //         return $get("from_verse") == null || $get("to_verse") == null;
                            //     })
                            //     ->content(function (\Closure $get, ?Model $record) {
                            //         $from_verse_number = \App\Models\Quran::find($get("from_verse"))?->aya_num;
                            //         $to_verse_number = \App\Models\Quran::find($get("to_verse"))?->aya_num;
                            //         if ($from_verse_number == $to_verse_number)
                            //             return "الاية " . $from_verse_number;
                            //         return " من الاية " . $from_verse_number . " الى الآية " . $to_verse_number;
                            //     }),


                        ]),
                    get_seo_filament_fields(),
                ])->columnSpan(
                    [
                        "sm" => 2
                    ]
                ),

                Card::make([
                    Forms\Components\Placeholder::make('created_at')
                        ->label(__('common.fields.created_at'))
                        ->content(fn(?Model $record): string => $record ? $record->created_at->diffForHumans() : '-'),
                    Forms\Components\Placeholder::make('updated_at')
                        ->label(__('common.fields.updated_at'))
                        ->content(fn(?Model $record): string => $record ? $record->updated_at->diffForHumans() : '-'),
                ])->columnSpan(1),
            ])->columns(
                [
                    "sm" => 3,
                    "lg" => null,
                ]
            );
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label(__('common.fields.id'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('book.name')
                    ->searchable()
                    ->sortable()
                    ->label(__('book.fields.book_id.label')),
                Tables\Columns\TextColumn::make('parent.name')
                    ->searchable()
                    ->label(__('title.fields.parent_id.label')),
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->label(__('title.fields.name.label')),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('common.fields.created_at'))
                    ->sortable()
                    ->dateTime("Y-m-d h:i A"),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('common.fields.updated_at'))
                    ->dateTime("Y-m-d h:i A"),
            ])
            ->filters([
                SelectFilter::make('book_id')
                    ->label(__('title.fields.book_id.label'))
                    ->placeholder(__('title.fields.book_id.placeholder'))
                    ->options(fn() => Book::all()->pluck('name', 'id')),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            ServicesRelationManager::class,
            // VersesRelationManager::class
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTitles::route('/'),
            'create' => Pages\CreateTitle::route('/create'),
            'view' => Pages\ViewTitle::route('/{record}'),
            'edit' => Pages\EditTitle::route('/{record}/edit'),
            'tree' => Pages\BookTitleTreePage::route('/{record}/tree'),
        ];
    }
    protected static bool $shouldRegisterNavigation = false;
}

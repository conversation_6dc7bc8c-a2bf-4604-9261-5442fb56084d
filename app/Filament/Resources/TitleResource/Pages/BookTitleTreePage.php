<?php

namespace App\Filament\Resources\TitleResource\Pages;

use App\Filament\Resources\TitleResource;
use App\Models\Book;
use Filament\Pages\Actions\CreateAction;
use Illuminate\Database\Eloquent\Builder;
use SolutionForest\FilamentTree\Actions;
use SolutionForest\FilamentTree\Actions\EditAction;
use SolutionForest\FilamentTree\Concern;
use SolutionForest\FilamentTree\Resources\Pages\TreePage as BasePage;
use SolutionForest\FilamentTree\Support\Utils;

class BookTitleTreePage extends BasePage
{
    public $book_id;
    protected static string $resource = TitleResource::class;


    public function mount($record): void
    {
        $this->book_id = $record;

        // $this->authorizeAccess();

        // $this->fillForm();

        // $this->previousUrl = url()->previous();
    }
    public function getPluralModelLabel(): string
    {
        return __('book.actions.view_titles_tree.title');
    }
    protected function getTitle(): string
    {
        return __('book.actions.view_titles_tree.title',['name'=> Book::find($this->book_id)->name]);
    }
    protected function getTreeQuery(): Builder
    {
        return $this->getModel()::where('book_id', $this->book_id);
    }
    protected function getTreeActions(): array
    {
        return [
            // Action::make('helloWorld')
            //     ->action(function () {
            //         Notification::make()->success()->title('Hello World')->send();
            //     }),
            // // ViewAction::make(),
            EditAction::make()
            ->url(fn ( $record) => TitleResource::getUrl('edit',$record))
            ,
            // ActionGroup::make([

            //     ViewAction::make(),
            //     EditAction::make(),
            // ]),
            // DeleteAction::make(),
        ];
    }
    // protected static int $maxDepth = 2;

    protected function getActions(): array
    {
        return [
            // $this->getCreateAction(),
            // SAMPLE CODE, CAN DELETE
            //\Filament\Pages\Actions\Action::make('sampleAction'),
        ];
    }

    protected function hasDeleteAction(): bool
    {
        return false;
    }

    protected function hasEditAction(): bool
    {
        return false;
    }

    protected function hasViewAction(): bool
    {
        return false;
    }

    protected function getHeaderWidgets(): array
    {
        return [];
    }

    protected function getFooterWidgets(): array
    {
        return [];
    }
    public function getNodeCollapsedState(?\Illuminate\Database\Eloquent\Model $record = null): bool
    {
        return true;
    }
    // CUSTOMIZE ICON OF EACH RECORD, CAN DELETE
    // public function getTreeRecordIcon(?\Illuminate\Database\Eloquent\Model $record = null): ?string
    // {
    //     return null;
    // }
}

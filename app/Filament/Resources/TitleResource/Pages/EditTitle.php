<?php

namespace App\Filament\Resources\TitleResource\Pages;

use App\Filament\Resources\TitleResource;
use App\Models\Content;
use App\Models\Title;
use Filament\Pages\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;

class EditTitle extends EditRecord
{
    protected static string $resource = TitleResource::class;

    public function mount($record): void
    {
        $this->record = $this->resolveRecord($record);
        $user = auth()->user();

        $user->last_title_id = $this->record->id;
        $user->save();

        $this->authorizeAccess();

        $this->fillForm();

        $this->previousUrl = url()->previous();
    }

    protected function getActions(): array
    {
        return [
            Actions\Action::make('previous_title')
                ->label(__('title.actions.previous_title.label'))
                ->visible(function ($livewire) {
                    $livewire->record?->getPreviousTitle() != null;
                })
                ->url(function ($livewire) {
                    $currentTitle = $livewire->record;
                    $url = $currentTitle?->getPreviousTitle();
                    if ($url == null) {
                        return "#";
                    }
                    return TitleResource::getUrl('edit', $currentTitle?->getPreviousTitle());
                }),
            Actions\Action::make('next_title')
                ->label(__('title.actions.next_title.label'))
                ->visible(function ($livewire) {
                    $livewire->record?->getNextTitle() != null;
                })
                ->url(function ($livewire) {
                    $currentTitle = $livewire->record;


                    return TitleResource::getUrl('edit', $currentTitle->getNextTitle());
                }),

            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $content = $this->getRecord()->content;
        if (!$content) {
            $this->getRecord()->content()->create([
                "title_id" => $this->getRecord()->id,
                "text" => "",
                "book_id" => $this->getRecord()->book_id,
            ]);
        }
        return $data;
    }
    protected function mutateFormDataBeforeSave(array $data): array
    {
        // dd($data);

        return $data;
    }
}

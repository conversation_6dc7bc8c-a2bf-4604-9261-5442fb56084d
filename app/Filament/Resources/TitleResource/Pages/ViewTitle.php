<?php

namespace App\Filament\Resources\TitleResource\Pages;

use App\Filament\Resources\TitleResource;
use App\Models\Title;
use Filament\Pages\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewTitle extends ViewRecord
{
    protected static string $resource = TitleResource::class;

    public function mount($record): void
    {
        static::authorizeResourceAccess();

        $this->record = $this->resolveRecord($record);

        $user = auth()->user();
        // dd($user);

        $user->last_title_id = $this->record->id;
        $user->save();
        abort_unless(static::getResource()::canView($this->getRecord()), 403);

        $this->fillForm();
    }

    protected function getActions(): array
    {
        return [
            Actions\Action::make('previous_title')
            ->label(__('title.actions.previous_title.label'))
            ->url(function ( $livewire) {
                $currentTitle = $livewire->record;
                $title = Title::where('id', '<', $currentTitle->id)->where('book_id', $currentTitle->book_id)->withCount('subtitles')->having('subtitles_count', '==', 0)->orderBy('id', 'desc')->first();
                if($title==null){
                    return null;
                }
               return TitleResource::getUrl('view',$title)
                ;
            }),
            Actions\Action::make('next_title')
            ->label(__('title.actions.next_title.label'))
            ->url(function ( $livewire) {
                $currentTitle = $livewire->record;
                $title = Title::where('id', '>', $currentTitle->id)->where('book_id', $currentTitle->book_id)->withCount('subtitles')->having('subtitles_count', '==', 0)->orderBy('id')->first();
                if($title==null){
                    return null;
                }
               return TitleResource::getUrl('view',$title)
                ;
            }),

            Actions\EditAction::make(),
        ];
    }
    protected function mutateFormDataBeforeFill(array $data): array
    {
        $content = $this->getRecord()->content;
        if (!$content) {
            $this->getRecord()->content()->create([
                "title_id" => $this->getRecord()->id,
                "text" => "",
                "book_id" => $this->getRecord()->book_id,
            ]);
        }
        return $data;
    }
}

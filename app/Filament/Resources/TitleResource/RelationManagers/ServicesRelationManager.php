<?php

namespace App\Filament\Resources\TitleResource\RelationManagers;

use Closure;
use Filament\Forms;
use Filament\Forms\Components\Card;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Resources\Table;
use Filament\Tables;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\HtmlString;
use Mohamedsabil83\FilamentFormsTinyeditor\Components\TinyEditor;
use Alaouy\Youtube\Facades\Youtube;
use App\Models\ServiceSubType;
use App\Models\Title;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Select;
use SolutionForest\FilamentTree\Forms\Components\Tree;
use Yepsua\Filament\Tables\Components\RatingColumn;

use function League\Uri\parse;

class ServicesRelationManager extends RelationManager
{
    protected static string $relationship = 'services';

    protected static ?string $recordTitleAttribute = 'name';

    public static function getModelLabel(): string
    {
        return __('title_service.singular_label');
    }
    public static function getPluralModelLabel(): string
    {
        return __('title_service.plural_label');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Card::make([
                    Placeholder::make('content')
                        ->label("")
                        ->content(function ($livewire) {
                            $content = $livewire->ownerRecord->content->text;
                            return new HtmlString($content);
                        }),
                    Forms\Components\Select::make('service_type')
                        ->label(__('title_service.fields.service_type.label'))
                        ->placeholder(__('title_service.fields.service_type.placeholder'))
                        ->options(__('title_service.options.service_type'))
                        ->required(),
                    Forms\Components\Select::make('service_sub_type_id')
                        ->label(__('title_service.fields.service_sub_type.label'))
                        ->placeholder(__('common.none'))
                        ->options(ServiceSubType::all()->pluck('name', 'id')->toArray())
                        ->createOptionForm([
                            Forms\Components\TextInput::make('name')
                                ->required(),
                        ])
                        ->createOptionUsing(function ($data) {
                            return ServiceSubType::create($data);
                        }),
                    Forms\Components\TextInput::make('name')
                        ->label(__('title_service.fields.name.label'))
                        ->placeholder(__('title_service.fields.name.placeholder'))
                        ->required()
                        ->datalist(__('title_service.options.service_name'))
                        ->maxLength(255),
                    Forms\Components\TextInput::make('sort')
                        ->label(__('title_service.fields.sort.label'))
                        ->placeholder(__('title_service.fields.sort.placeholder'))
                        ->numeric()
                        ->required(),
                    Forms\Components\Select::make('view_type')
                        ->label(__('title_service.fields.view_type.label'))
                        ->placeholder(__('title_service.fields.view_type.placeholder'))
                        ->reactive()
                        ->options(__('title_service.options.view_type'))
                        ->required(),
                    Group::make(
                        [
                            TinyEditor::make('text')

                                ->minHeight(200)
                                ->label(__('title_service.fields.text.label'))
                                ->placeholder(__('title_service.fields.text.placeholder')),
                        ]
                    )->relationship('text')
                        ->mutateRelationshipDataBeforeCreateUsing(function ($data) {
                            return static::mutateTextBefore($data);
                        })
                        ->mutateRelationshipDataBeforeSaveUsing(function ($data) {
                            return static::mutateTextBefore($data);
                        })
                        ->hidden(fn(Closure $get) => $get('view_type') != 1),



                    Repeater::make('texts')
                        ->minItems(1)
                        ->label(__('title_service.fields.texts.label'))
                        ->hidden(fn(Closure $get) => $get('view_type') != 7)
                        ->relationship('texts')
                        // ->orderable('sort')
                        ->mutateRelationshipDataBeforeCreateUsing(function ($data) {
                            return static::mutateTextBefore($data);
                        })
                        ->mutateRelationshipDataBeforeSaveUsing(function ($data) {
                            return static::mutateTextBefore($data);
                        })
                        ->schema([
                            TinyEditor::make('text')

                                ->minHeight(200)
                                ->label(__('title_service.fields.text.label'))
                                ->placeholder(__('title_service.fields.text.placeholder')),
                            Forms\Components\TextInput::make('sort')
                                ->label(__('title_service.fields.sort.label'))
                                ->placeholder(__('title_service.fields.sort.placeholder'))
                                ->numeric()
                                ->required(),
                        ]),

                    Repeater::make('videos')
                        ->label(__('title_service.fields.videos.label'))
                        ->hidden(fn(Closure $get) => $get('view_type') != 2)
                        ->relationship('videos')
                        ->mutateRelationshipDataBeforeCreateUsing(function ($data) {
                            return static::mutateVideoBefore($data);
                        })
                        ->mutateRelationshipDataBeforeSaveUsing(function ($data) {
                            return static::mutateVideoBefore($data);
                        })
                        ->schema([
                            Forms\Components\Select::make('video_type')
                                ->label(__('title_service.fields.video_type.label'))
                                ->placeholder(__('title_service.fields.video_type.placeholder'))
                                ->options(__('title_service.options.video_type'))
                                ->reactive()
                                ->required(),
                            FileUpload::make('thumbnail')
                                ->label(__('title_service.fields.thumbnail.label'))
                                ->placeholder(__('title_service.fields.thumbnail.placeholder'))
                                ->required()
                                ->directory('videos/thumbnails')
                                ->disk('do')
                                ->acceptedFileTypes(['image/jpeg', 'image/png'])
                                ->maxSize(1024 * 1024 * 1)
                                ->maxFiles(1)
                                ->hidden(function (Closure $get) {
                                    return $get('url') != null || $get('video_type') != 1;
                                })
                                ->multiple(false),
                            FileUpload::make('video_file')
                                ->label(__('title_service.fields.video_file.label'))
                                ->placeholder(__('title_service.fields.video_file.placeholder'))
                                ->acceptedFileTypes(['video/mp4'])
                                ->directory('videos')
                                ->disk('do')
                                ->enableDownload()
                                ->hidden(function (Closure $get) {
                                    return $get('url') != null || $get('video_type') != 1;
                                }),
                            TextInput::make('video_id')
                                ->label(__('title_service.fields.youtube_url.label'))
                                ->placeholder(__('title_service.fields.youtube_url.placeholder'))
                                ->required()
                                ->hidden(fn(Closure $get) => $get('video_type') != 2)
                                ->reactive()
                                ->afterStateUpdated(function (Closure $set, $state) {
                                    $re = '/(?im)\b(?:https?:\/\/)?(?:w{3}\.)?youtu(?:be)?\.(?:com|be)\/(?:(?:\??v=?i?=?\/?)|watch\?vi?=|watch\?.*?&v=|embed\/|)([A-Z0-9_-]{11})\S*(?=\s|$)/';
                                    if (preg_match($re, $state, $matches)) {
                                        $set('video_id', $matches[1]);
                                    }
                                })
                                ->maxLength(255),
                            Placeholder::make('video_preview')
                                // ->dehydrated(false)
                                ->label(__('title_service.fields.video_preview.label'))
                                ->content(function (Closure $get) {
                                    return new HtmlString("<iframe width=\"100%\"  src=\"https://www.youtube.com/embed/{$get('video_id')}\" frameborder=\"0\" allow=\"accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture\" allowfullscreen></iframe>");
                                })
                                ->hidden(function (Closure $get) {
                                    return $get('video_id') == null || $get('video_type') != 2;
                                }),
                            TextInput::make('url')
                                ->label(__('title_service.fields.video_url.label'))
                                ->placeholder(__('title_service.fields.video_url.placeholder'))
                                ->required()
                                ->hidden(fn(Closure $get) => $get('video_type') != 3)
                                ->url()
                                ->maxLength(255),
                            TextInput::make('title')
                                ->label(__('title_service.fields.title.label'))
                                ->placeholder(__('title_service.fields.title.placeholder'))
                                ->required()
                                ->maxLength(255),
                            Textarea::make('detail')
                                ->label(__('title_service.fields.detail.label'))
                                ->placeholder(__('title_service.fields.detail.placeholder'))
                                ->required(),

                        ]),
                    Repeater::make('qoutations')
                        ->label(__('title_service.fields.quotations.label'))
                        ->hidden(fn(Closure $get) => $get('view_type') != 3)
                        ->relationship('qoutations')
                        ->mutateRelationshipDataBeforeCreateUsing(function ($data) {
                            return static::mutateTextBefore($data);
                        })
                        ->mutateRelationshipDataBeforeSaveUsing(function ($data) {
                            return static::mutateTextBefore($data);
                        })
                        // ->orderable()
                        ->schema([
                            TextInput::make('name')
                                ->label(__('title_service.fields.refrence_name.label'))
                                ->placeholder(__('title_service.fields.refrence_name.placeholder'))
                                ->required(),
                            TinyEditor::make('text')

                                ->minHeight(200)
                                ->label(__('title_service.fields.quotation_text.label'))
                                ->placeholder(__('title_service.fields.quotation_text.placeholder'))
                                ->required(),
                            Forms\Components\TextInput::make('sort')
                                ->label(__('title_service.fields.sort.label'))
                                ->placeholder(__('title_service.fields.sort.placeholder'))
                                ->numeric()
                                ->required(),
                        ]),
                    Repeater::make('refrences')
                        ->label(__('title_service.fields.refrences.label'))
                        ->hidden(fn(Closure $get) => $get('view_type') != 4)
                        ->relationship('refrences')
                        ->mutateRelationshipDataBeforeCreateUsing(function ($data) {
                            return static::mutateTextBefore($data);
                        })
                        ->mutateRelationshipDataBeforeSaveUsing(function ($data) {
                            return static::mutateTextBefore($data);
                        })
                        // ->orderable()
                        ->schema([
                            TextInput::make('name')
                                ->label(__('title_service.fields.reference_name.label'))
                                ->placeholder(__('title_service.fields.reference_name.placeholder'))
                                ->required(),
                            TinyEditor::make('description')

                                ->minHeight(200)
                                ->label(__('title_service.fields.reference_description.label'))
                                ->placeholder(__('title_service.fields.reference_description.placeholder'))
                                ->required(),
                            FileUpload::make('file')
                                ->label(__('title_service.fields.reference_file.label'))
                                ->placeholder(__('title_service.fields.reference_file.placeholder'))
                                // pdf word
                                ->acceptedFileTypes(['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'])
                                ->directory('references')
                                ->required(),
                            Forms\Components\TextInput::make('sort')
                                ->label(__('title_service.fields.sort.label'))
                                ->placeholder(__('title_service.fields.sort.placeholder'))
                                ->numeric()
                                ->required(),
                        ]),
                    Repeater::make('activities')
                        ->label(__('title_service.fields.activities.label'))
                        ->hidden(fn(Closure $get) => $get('view_type') != 5)
                        ->relationship()
                        // ->orderable()
                        ->mutateRelationshipDataBeforeCreateUsing(function ($data) {
                            return static::mutateTextBefore($data);
                        })
                        ->mutateRelationshipDataBeforeSaveUsing(function ($data) {
                            return static::mutateTextBefore($data);
                        })
                        ->schema([
                            TextInput::make('style')
                                ->label(__('title_service.fields.activity_style.label'))
                                ->placeholder(__('title_service.fields.activity_style.placeholder'))
                                ->required(),
                            TextInput::make('imp_type')
                                ->label(__('title_service.fields.activity_imp_type.label'))
                                ->placeholder(__('title_service.fields.activity_imp_type.placeholder'))
                                ->required(),
                            TextInput::make('type')
                                ->label(__('title_service.fields.activity_type.label'))
                                ->placeholder(__('title_service.fields.activity_type.placeholder'))
                                ->required(),
                            TinyEditor::make('goal')

                                ->minHeight(200)
                                ->label(__('title_service.fields.activity_goal.label'))
                                ->placeholder(__('title_service.fields.activity_goal.placeholder'))
                                ->required(),
                            TinyEditor::make('needed')

                                ->minHeight(200)
                                ->label(__('title_service.fields.activity_needed.label'))
                                ->placeholder(__('title_service.fields.activity_needed.placeholder'))
                                ->required(),
                            TinyEditor::make('answer')

                                ->minHeight(200)
                                ->label(__('title_service.fields.activity_answer.label'))
                                ->placeholder(__('title_service.fields.activity_answer.placeholder'))
                                ->required(),
                            Forms\Components\TextInput::make('sort')
                                ->label(__('title_service.fields.sort.label'))
                                ->placeholder(__('title_service.fields.sort.placeholder'))
                                ->numeric()
                                ->required(),

                        ]),
                    Repeater::make('calenders')
                        ->label(__('title_service.fields.calender.label'))
                        ->hidden(fn(Closure $get) => $get('view_type') != 6)
                        ->relationship()
                        ->orderable()
                        ->mutateRelationshipDataBeforeCreateUsing(function ($data) {
                            return static::mutateTextBefore($data);
                        })
                        ->mutateRelationshipDataBeforeSaveUsing(function ($data) {
                            return static::mutateTextBefore($data);
                        })
                        ->schema([
                            TextInput::make('style')
                                ->label(__('title_service.fields.calender_style.label'))
                                ->placeholder(__('title_service.fields.calender_style.placeholder'))
                                ->required(),
                            TextInput::make('imp_type')
                                ->label(__('title_service.fields.calender_imp_type.label'))
                                ->placeholder(__('title_service.fields.calender_imp_type.placeholder'))
                                ->required(),
                            TextInput::make('duration')
                                ->label(__('title_service.fields.calender_duration.label'))
                                ->placeholder(__('title_service.fields.calender_duration.placeholder'))
                                ->required(),
                            TinyEditor::make('goal')

                                ->label(__('title_service.fields.calender_goal.label'))
                                ->placeholder(__('title_service.fields.calender_goal.placeholder'))
                                ->required(),
                            TinyEditor::make('tools_questions')

                                ->label(__('title_service.fields.calender_tools_questions.label'))
                                ->placeholder(__('title_service.fields.calender_tools_questions.placeholder'))
                                ->required(),
                            TinyEditor::make('feedback_answers')

                                ->label(__('title_service.fields.calender_feedback_answers.label'))
                                ->placeholder(__('title_service.fields.calender_feedback_answers.placeholder'))
                                ->required(),
                            Forms\Components\TextInput::make('sort')
                                ->label(__('title_service.fields.sort.label'))
                                ->placeholder(__('title_service.fields.sort.placeholder'))
                                ->numeric()
                                ->required(),
                        ]),
                    get_seo_filament_fields(),



                ])->columnSpan(
                    [
                        "sm" => 2
                    ]
                ),
                Card::make([
                    Forms\Components\Placeholder::make('created_at')
                        ->label(__('common.fields.created_at'))
                        ->content(fn(?Model $record): string => $record ? $record->created_at->diffForHumans() : '-'),
                    Forms\Components\Placeholder::make('updated_at')
                        ->label(__('common.fields.updated_at'))
                        ->content(fn(?Model $record): string => $record ? $record->updated_at->diffForHumans() : '-'),
                ])->columnSpan(1),
            ])->columns(
                [
                    "sm" => 3,
                    "lg" => null,
                ]
            );
    }

    public static function getNameWithParent($title)
    {
        if ($parent = $title->parent) {
            return $parent->name . ' > ' . $title->name;
        }
        return  $title->name;
    }

    public static function table(Table $table): Table
    {
        return $table
            ->reorderable('sort')
            ->defaultSort('sort', 'asc')
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label(__('title_service.fields.name.label'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('service_type')
                    ->label(__('title_service.fields.service_type.label'))
                    ->enum(__('title_service.options.service_type'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('service_sub_type_id')
                    ->label(__('title_service.fields.service_sub_type.label'))
                    ->getStateUsing(fn(Model $record) => $record->service_sub_type != null ? $record->service_sub_type->name : __('common.none'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('view_type')
                    ->label(__('title_service.fields.view_type.label'))
                    ->enum(__('title_service.options.view_type'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('sort')
                    ->label(__('title_service.fields.sort.label'))
                    ->sortable(),


                //content rating
                RatingColumn::make('rating')
                    ->label(__('title.fields.rating.label'))
                    ->getStateUsing(
                        function ($record) {
                            $rating = $record->rates;
                            $sum = $rating?->sum('stars') ?? 0;
                            $count = $rating?->count('stars') ?? 0;
                            if ($count == 0) {
                                return 0;
                            }
                            return round(($sum / $count) / 5) * 5;
                        }
                    )

                    ->color('primary'),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('common.fields.created_at'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('common.fields.updated_at'))
                    ->sortable(),
            ])
            ->filters([
                SelectFilter::make('service_type')
                    ->label(__('title_service.fields.service_type.label'))
                    ->options(__('title_service.options.service_type')),
                SelectFilter::make('view_type')
                    ->label(__('title_service.fields.view_type.label'))
                    ->options(__('title_service.options.view_type')),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->mutateFormDataUsing(function (array $data): array {
                        return  static::mutateFormDataUsing($data);
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\Action::make('move')
                    ->label(__('title_service.actions.move'))
                    ->modalButton(__('title_service.actions.move'))
                    ->icon('heroicon-o-reply')
                    ->color('warning')
                    ->form(
                        [
                            Select::make('title_id')
                                ->label(__('title_service.fields.title_id.label'))
                                ->required()
                                ->options(
                                    function ($livewire) {
                                        $bookId = $livewire->ownerRecord->book_id;
                                        $titles = Title::where('book_id', $bookId)->whereDoesntHave('subtitles')->get();
                                        $array = [];
                                        foreach ($titles as $title) {
                                            $array[$title->id] = static::getNameWithParent($title);
                                        }
                                        return $array;
                                    }
                                ),
                        ]
                    )
                    ->action(function (Model $record, array $data) {
                        $record->title_id = $data['title_id'];
                        $record->save();
                    }),
                Tables\Actions\Action::make('copy')
                    ->label(__('title_service.actions.copy'))
                    ->modalButton(__('title_service.actions.copy'))
                    ->color('success')
                    ->icon('heroicon-o-duplicate')
                    ->form(
                        [
                            Select::make('title_id')
                                ->label(__('title_service.fields.title_id.label'))
                                ->required()
                                ->options(
                                    function ($livewire) {
                                        $bookId = $livewire->ownerRecord->book_id;
                                        $titles = Title::where('book_id', $bookId)->whereDoesntHave('subtitles')->get();
                                        $array = [];
                                        foreach ($titles as $title) {
                                            $array[$title->id] = static::getNameWithParent($title);
                                        }
                                        return $array;
                                    }
                                ),
                        ]
                    )
                    ->action(function (Model $record, array $data) {
                        $copy = $record->replicate();
                        $copy->title_id = $data['title_id'];
                        $copy->created_at = now();
                        $copy->save();
                    }),
                Tables\Actions\EditAction::make()
                    ->mutateFormDataUsing(function (array $data): array {
                        return  static::mutateFormDataUsing($data);
                    }),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([

                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }
    public static function mutateFormDataUsing(array $data): array
    {

        //text
        // if ($data['view_type'] == 1) {
        //     $data['text'] = quran()->decode($data['text']);
        // }
        return $data;
    }


    public static function mutateVideoBefore(array $data): array
    {
        if ($data['video_type'] == 2) {
            $videoInfo =  Youtube::getVideoInfo($data['video_id'], ['snippet']);
            $thumbnails = $videoInfo->snippet->thumbnails;
            // dd($thumbnails);
            $thumbnail = "";
            if (isset($thumbnails->maxres)) {
                $thumbnail = $thumbnails->maxres->url;
            } elseif (isset($thumbnails->high)) {
                $thumbnail = $thumbnails->high->url;
            } elseif (isset($thumbnails->medium)) {
                $thumbnail = $thumbnails->medium->url;
            } elseif (isset($thumbnails->default)) {
                $thumbnail = $thumbnails->default->url;
            }
            $data['thumbnail'] = $thumbnail;
        }

        return $data;
    }


    public static function mutateTextBefore(array $data)
    {
        if (isset($data['text'])) {
            $data['text'] = quran()->decode($data['text']);
        }
        if (isset($data['description'])) {
            $data['description'] = quran()->decode($data['description']);
        }
        /*
         $data['data'][$key]['goal'] = quran()->decode($value['goal']);
                $data['data'][$key]['needed'] = quran()->decode($value['needed']);
                $data['data'][$key]['answer'] = quran()->decode($value['answer']);
                $data['data'][$key]['tools_questions'] = quran()->decode($value['tools_questions']);
                $data['data'][$key]['feedback_answers'] = quran()->decode($value['feedback_answers']);
        */
        if (isset($data['goal'])) {
            $data['goal'] = quran()->decode($data['goal']);
        }
        if (isset($data['needed'])) {
            $data['needed'] = quran()->decode($data['needed']);
        }
        if (isset($data['answer'])) {
            $data['answer'] = quran()->decode($data['answer']);
        }
        if (isset($data['tools_questions'])) {
            $data['tools_questions'] = quran()->decode($data['tools_questions']);
        }
        if (isset($data['feedback_answers'])) {
            $data['feedback_answers'] = quran()->decode($data['feedback_answers']);
        }
        return $data;
    }
    public static function canViewForRecord(Model $ownerRecord): bool
    {
        return  $ownerRecord->subtitles()->count() == 0;
    }
}

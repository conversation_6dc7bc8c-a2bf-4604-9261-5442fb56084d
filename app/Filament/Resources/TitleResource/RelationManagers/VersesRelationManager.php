<?php

namespace App\Filament\Resources\TitleResource\RelationManagers;

use App\Models\QuranTitle;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Resources\Table;
use Filament\Tables;

use Filament\Tables\Contracts\HasRelationshipTable;

use Illuminate\Database\Eloquent\Model;

class VersesRelationManager extends RelationManager
{
    protected static string $relationship = 'verses';

    protected static ?string $inverseRelationship = "titles";

    // protected static ?string $recordTitleAttribute = 'id';


    public static function getModelLabel(): string
    {
        return __('quran_titles.singular_label');
    }
    public static function getPluralModelLabel(): string
    {
        return __('quran_titles.plural_label');
    }


    public static function form(Form $form): Form
    {
        return $form

            ->schema([
                Forms\Components\Select::make('sora_num')
                    ->label(__("quran_titles.fields.sora_num.label"))
                    ->options(\App\Models\Quran::groupBy(["sora_num", "SoraName"])->pluck("SoraName", "sora_num"))
                    ->reactive()->columnSpanFull()
                    ->required(),

                Forms\Components\Select::make('verse_id')
                    ->label(__("quran_titles.fields.verse_id.label"))
                    ->options(
                        function (\Closure $get, $state) {
                            return    \App\Models\Quran::where("sora_num", $get("sora_num"))->pluck("aya_with_diac", "id");
                        }
                    )
                    ->columnSpanFull()
                    ->optionsLimit(function (\Closure $get) {
                        return    \App\Models\Quran::where("sora_num", $get("sora_num"))->pluck("aya_with_diac", "id")->count();
                    })
                    ->searchable()
                    ->reactive()

                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table

            ->columns([
                Tables\Columns\TextColumn::make('id'),
                Tables\Columns\TextColumn::make('SoraName')
                    ->label(__("quran_titles.fields.sora_num.label")),
                Tables\Columns\TextColumn::make('verse_id')
                    ->label(__("quran_titles.fields.verse_num.label")),
                Tables\Columns\TextColumn::make('aya_with_diac')
                    ->label(__("quran_titles.fields.verse_id.label")),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()->using(function (HasRelationshipTable $livewire, array $data): Model {

                    $data["title_id"] = ($livewire->ownerRecord->id);

                    return QuranTitle::create($data);
                    // return $livewire->getRelationship()->create($data);
                }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                // Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }
}

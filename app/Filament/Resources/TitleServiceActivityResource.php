<?php

namespace App\Filament\Resources;

use App\Facades\Panel;

use App\Filament\Actions\VisitTitleAction;
use App\Filament\Resources\TitleServiceActivityResource\Pages;
use App\Filament\Resources\TitleServiceActivityResource\RelationManagers;
use App\Models\TitleServiceActivity;
use App\Traits\MultiPanelResourse;
use Filament\Forms;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Mohamedsabil83\FilamentFormsTinyeditor\Components\TinyEditor;

class TitleServiceActivityResource extends Resource
{
    use MultiPanelResourse;
    protected static ?string $model = TitleServiceActivity::class;

    protected static ?string $navigationIcon = 'heroicon-o-beaker';

    protected static ?int $navigationSort = 5;

    protected static function getNavigationGroup(): ?string
    {
        return __('common.navigation_groups.book_services');
    }

    public static function getModelLabel(): string
    {
        return __('title_service_activity.singular_label');
    }
    public static function getPluralModelLabel(): string
    {
        return __('title_service_activity.plural_label');
    }
    public static function form(Form $form): Form
    {
        return $form
            ->columns(1)
            ->schema([
                Forms\Components\TextInput::make('type')
                    ->label(__('title_service_activity.fields.type.label')),
                Forms\Components\TextInput::make('style')
                    ->label(__('title_service_activity.fields.style.label')),
                Textarea::make('imp_type')
                    ->label(__('title_service_activity.fields.imp_type.label'))
                    ->rows(2),
                TinyEditor::make('goal')
                    ->label(__('title_service_activity.fields.goal.label')),
                TinyEditor::make('answer')
                    ->label(__('title_service_activity.fields.answer.label')),
                TinyEditor::make('needed')
                    ->label(__('title_service_activity.fields.needed.label')),
                Forms\Components\TextInput::make('sort')
                    ->label(__('common.fields.sort.label'))
                    ->numeric(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('service.title.book.name')
                    ->label(__('common.fields.book_name.label'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('service.title.name')
                    ->label(__('common.fields.title_name.label'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('service.name')
                    ->label(__('common.fields.service_name.label'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('service.view_type')
                    ->label(__('common.fields.view_type.label'))
                    ->sortable()
                    ->formatStateUsing(fn($state): string => __('common.options.view_type.' . $state)),
                Tables\Columns\TextColumn::make('type')
                    ->label(__('title_service_activity.fields.type.label'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('style')
                    ->label(__('title_service_activity.fields.style.label'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('imp_type')
                    ->label(__('title_service_activity.fields.imp_type.label'))
                    ->searchable()
                    ->sortable()
                    ->limit(50),
                Tables\Columns\TextColumn::make('goal')
                    ->label(__('title_service_activity.fields.goal.label'))
                    ->searchable()
                    ->limitHtml(50),
                Tables\Columns\TextColumn::make('needed')
                    ->label(__('title_service_activity.fields.needed.label'))
                    ->searchable()
                    ->limitHtml(50),
                Tables\Columns\TextColumn::make('answer')
                    ->label(__('title_service_activity.fields.answer.label'))
                    ->searchable()
                    ->limitHtml(50),

                Tables\Columns\TextColumn::make('sort')
                    ->label(__('common.fields.sort.label'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('common.fields.created_at'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->dateTime(),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('common.fields.updated_at'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->dateTime(),
            ])
            ->filters([
                \App\Filament\Filters\ServiceBookFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                VisitTitleAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkAction::make("export")
                    ->icon('heroicon-o-document-text')
                    ->label(__('title.actions.export'))
                    ->action(fn($records) =>  \Maatwebsite\Excel\Facades\Excel::download(new \App\Exports\ServicesExport('activity', $records->load('service', 'service.title', 'service.title.book')), 'services.xlsx')),
                \App\Filament\BulkActions\RenameServicesBulkAction::make(),
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageTitleServiceActivities::route('/'),
        ];
    }
}

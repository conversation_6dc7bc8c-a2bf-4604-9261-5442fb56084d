<?php

namespace App\Filament\Resources\TitleServiceActivityResource\Pages;

use App\Filament\Resources\TitleServiceActivityResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageTitleServiceActivities extends ManageRecords
{
    protected static string $resource = TitleServiceActivityResource::class;

    protected function getActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}

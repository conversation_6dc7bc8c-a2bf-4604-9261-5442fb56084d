<?php

namespace App\Filament\Resources;

use App\Facades\Panel;

use App\Filament\Actions\VisitTitleAction;
use App\Filament\Resources\TitleServiceCalenderResource\Pages;
use App\Filament\Resources\TitleServiceCalenderResource\RelationManagers;
use App\Models\TitleServiceCalender;
use App\Traits\MultiPanelResourse;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Mohamedsabil83\FilamentFormsTinyeditor\Components\TinyEditor;

class TitleServiceCalenderResource extends Resource
{
    use MultiPanelResourse;
    protected static ?string $model = TitleServiceCalender::class;

    protected static ?string $navigationIcon = 'heroicon-o-calendar';

    protected static ?int $navigationSort = 6;

    protected static function getNavigationGroup(): ?string
    {
        return __('common.navigation_groups.book_services');
    }

    public static function getModelLabel(): string
    {
        return __('title_service_calender.singular_label');
    }
    public static function getPluralModelLabel(): string
    {
        return __('title_service_calender.plural_label');
    }
    public static function form(Form $form): Form
    {
        return $form
            ->columns(1)
            ->schema([
                Forms\Components\TextInput::make('style')
                    ->label(__('title_service_calender.fields.style.label'))
                    ->maxLength(255),
                Forms\Components\TextInput::make('imp_type')
                    ->label(__('title_service_calender.fields.imp_type.label'))
                    ->maxLength(65535),
                Forms\Components\TextInput::make('duration')
                    ->label(__('title_service_calender.fields.duration.label'))
                    ->maxLength(255),
                TinyEditor::make('goal')

                    ->label(__('title_service_calender.fields.goal.label')),
                TinyEditor::make('tools_questions')
                    ->label(__('title_service_calender.fields.tools_questions.label')),
                TinyEditor::make('feedback_answers')

                    ->label(__('title_service_calender.fields.feedback_answers.label')),
                Forms\Components\TextInput::make('sort')
                    ->label(__('common.fields.sort.label'))
                    ->numeric()
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('service.title.book.name')
                    ->label(__('common.fields.book_name.label'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('service.title.name')
                    ->label(__('common.fields.title_name.label'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('service.name')
                    ->label(__('common.fields.service_name.label'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('style')
                    ->label(__('title_service_calender.fields.style.label'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('imp_type')
                    ->label(__('title_service_calender.fields.imp_type.label'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('duration')
                    ->label(__('title_service_calender.fields.duration.label'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('goal')
                    ->label(__('title_service_calender.fields.goal.label'))
                    ->searchable()
                    ->limitHtml(50),
                Tables\Columns\TextColumn::make('tools_questions')
                    ->label(__('title_service_calender.fields.tools_questions.label'))
                    ->searchable()
                    ->limitHtml(50),
                Tables\Columns\TextColumn::make('feedback_answers')
                    ->label(__('title_service_calender.fields.feedback_answers.label'))
                    ->searchable()
                    ->limitHtml(50),

                Tables\Columns\TextColumn::make('sort')
                    ->label(__('common.fields.sort.label'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('common.fields.created_at'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->dateTime(),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('common.fields.updated_at'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->dateTime(),
            ])
            ->filters([
                \App\Filament\Filters\ServiceBookFilter::make()
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                VisitTitleAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkAction::make("export")
                    ->icon('heroicon-o-document-text')
                    ->label(__('title.actions.export'))
                    ->action(fn($records) =>  \Maatwebsite\Excel\Facades\Excel::download(new \App\Exports\ServicesExport('calender', $records->load('service', 'service.title', 'service.title.book')), 'services.xlsx')),
                \App\Filament\BulkActions\RenameServicesBulkAction::make(),
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageTitleServiceCalenders::route('/'),
        ];
    }
}

<?php

namespace App\Filament\Resources;

use App\Facades\Panel;

use App\Filament\Actions\VisitTitleAction;
use App\Filament\Filters\ServiceBookFilter;
use App\Filament\Resources\TitleServiceQuotationResource\Pages;
use App\Filament\Resources\TitleServiceQuotationResource\RelationManagers;
use App\Models\TitleServiceQuotation;
use App\Traits\MultiPanelResourse;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Mohamedsabil83\FilamentFormsTinyeditor\Components\TinyEditor;

class TitleServiceQuotationResource extends Resource
{
    use MultiPanelResourse;
    protected static ?string $model = TitleServiceQuotation::class;

    protected static ?string $navigationIcon = 'heroicon-o-chat';

    protected static ?int $navigationSort = 3;

    protected static function getNavigationGroup(): ?string
    {
        return __('common.navigation_groups.book_services');
    }

    public static function getModelLabel(): string
    {
        return __('title_service_quotation.singular_label');
    }
    public static function getPluralModelLabel(): string
    {
        return __('title_service_quotation.plural_label');
    }
    public static function form(Form $form): Form
    {
        return $form
            ->columns(1)
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label(__('title_service_quotation.fields.name.label')),
                TinyEditor::make('text')

                    ->label(__('title_service_quotation.fields.text.label')),
                Forms\Components\TextInput::make('sort')
                    ->label(__('common.fields.sort.label'))
                    ->numeric(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('service.title.book.name')
                    ->label(__('common.fields.book_name.label'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('service.title.name')
                    ->label(__('common.fields.title_name.label'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('service.name')
                    ->label(__('common.fields.service_name.label'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('name')
                    ->label(__('title_service_quotation.fields.name.label'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('text')
                    ->label(__('title_service_quotation.fields.text.label'))
                    ->searchable()
                    ->limitHtml(70),
                Tables\Columns\TextColumn::make('sort')
                    ->label(__('common.fields.sort.label'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('common.fields.created_at'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->dateTime(),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('common.fields.updated_at'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->dateTime(),
            ])
            ->filters([
                ServiceBookFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                VisitTitleAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkAction::make("export")
                    ->icon('heroicon-o-document-text')
                    ->label(__('title.actions.export'))
                    ->action(fn($records) =>  \Maatwebsite\Excel\Facades\Excel::download(new \App\Exports\ServicesExport('quotation', $records->load('service', 'service.title', 'service.title.book')), 'services.xlsx')),
                \App\Filament\BulkActions\RenameServicesBulkAction::make(),
                \App\Filament\BulkActions\RenameQoutsBulkAction::make(),
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageTitleServiceQuotations::route('/'),
        ];
    }
}

<?php

namespace App\Filament\Resources;

use App\Facades\Panel;

use App\Filament\Actions\VisitTitleAction;
use App\Filament\Resources\TitleServiceReferenceResource\Pages;
use App\Filament\Resources\TitleServiceReferenceResource\RelationManagers;
use App\Models\TitleServiceReference;
use App\Traits\MultiPanelResourse;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\App;
use Mohamedsabil83\FilamentFormsTinyeditor\Components\TinyEditor;

class TitleServiceReferenceResource extends Resource
{
    use MultiPanelResourse;
    protected static ?string $model = TitleServiceReference::class;

    protected static ?string $navigationIcon = 'heroicon-o-bookmark';

    protected static ?int $navigationSort = 4;

    protected static function getNavigationGroup(): ?string
    {
        return __('common.navigation_groups.book_services');
    }

    public static function getModelLabel(): string
    {
        return __('title_service_reference.singular_label');
    }
    public static function getPluralModelLabel(): string
    {
        return __('title_service_reference.plural_label');
    }
    public static function form(Form $form): Form
    {
        return $form
            ->columns(1)
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label(__('title_service_reference.fields.name.label')),
                TinyEditor::make('description')
                    ->toolbarSticky(true)
                    ->label(__('title_service_reference.fields.description.label')),
                Forms\Components\FileUpload::make('file')
                    ->label(__('title_service_reference.fields.file.label'))
                    ->enableDownload()
                    ->enableOpen(),
                Forms\Components\TextInput::make('sort')
                    ->label(__('common.fields.sort.label'))
                    ->numeric(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('service.title.book.name')
                    ->label(__('common.fields.book_name.label'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('service.title.name')
                    ->label(__('common.fields.title_name.label'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('service.name')
                    ->label(__('common.fields.service_name.label'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('name')
                    ->label(__('title_service_reference.fields.name.label')),
                Tables\Columns\TextColumn::make('description')
                    ->label(__('title_service_reference.fields.description.label'))
                    ->searchable()
                    ->limitHtml(70),
                Tables\Columns\IconColumn::make('file')
                    ->trueIcon('heroicon-o-download')
                    ->getStateUsing(fn($record) => $record->file !== null)
                    ->label(__('title_service_reference.fields.file.label'))
                    ->url(function ($record) {
                        return url($record->file);
                    }, shouldOpenInNewTab: true),
                Tables\Columns\TextColumn::make('sort')
                    ->label(__('common.fields.sort.label'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('common.fields.created_at'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->dateTime(),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('common.fields.updated_at'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->dateTime(),
            ])
            ->filters([
                \App\Filament\Filters\ServiceBookFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                VisitTitleAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkAction::make("export")
                    ->icon('heroicon-o-document-text')
                    ->label(__('title.actions.export'))
                    ->action(fn($records) =>  \Maatwebsite\Excel\Facades\Excel::download(new \App\Exports\ServicesExport('reference', $records->load('service', 'service.title', 'service.title.book')), 'services.xlsx')),
                \App\Filament\BulkActions\RenameServicesBulkAction::make(),
                \App\Filament\BulkActions\RenameRefrencesBulkAction::make(),
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageTitleServiceReferences::route('/'),
        ];
    }
}

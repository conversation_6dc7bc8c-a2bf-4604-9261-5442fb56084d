<?php

namespace App\Filament\Resources\TitleServiceReferenceResource\Pages;

use App\Filament\Resources\TitleServiceReferenceResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageTitleServiceReferences extends ManageRecords
{
    protected static string $resource = TitleServiceReferenceResource::class;

    protected function getActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}

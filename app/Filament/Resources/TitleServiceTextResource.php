<?php

namespace App\Filament\Resources;

use App\Facades\Panel;

use App\Filament\Actions\VisitTitleAction;
use App\Filament\BulkActions\RenameServicesBulkAction;
use App\Filament\Filters\ServiceBookFilter;
use App\Filament\Resources\TitleServiceTextResource\Pages;
use App\Filament\Resources\TitleServiceTextResource\RelationManagers;
use App\Models\TitleServiceText;
use App\Traits\MultiPanelResourse;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Mohamedsabil83\FilamentFormsTinyeditor\Components\TinyEditor;


class TitleServiceTextResource extends Resource
{
    use MultiPanelResourse;
    protected static ?string $model = TitleServiceText::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static function getNavigationGroup(): ?string
    {
        return __('common.navigation_groups.book_services');
    }

    public static function getModelLabel(): string
    {
        return __('title_service_text.singular_label');
    }
    public static function getPluralModelLabel(): string
    {
        return __('title_service_text.plural_label');
    }
    protected static ?int $navigationSort = 1;


    public static function form(Form $form): Form
    {
        return $form
            ->columns(1)
            ->schema([
                TinyEditor::make('text')
        
                    ->label(__('title_service_text.fields.text.label')),
                Forms\Components\TextInput::make('sort')
                    ->label(__('common.fields.sort.label')),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('service.title.book.name')
                    ->label(__('common.fields.book_name.label'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('service.title.name')
                    ->label(__('common.fields.title_name.label'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('service.name')
                    ->label(__('common.fields.service_name.label'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('service.view_type')
                    ->label(__('common.fields.view_type.label'))
                    ->sortable()
                    ->formatStateUsing(fn($state): string => __('common.options.view_type.' . $state)),
                Tables\Columns\TextColumn::make('text')
                    ->label(__('title_service_text.fields.text.label'))
                    ->searchable()
                    ->limitHtml(70),
                Tables\Columns\TextColumn::make('sort')
                    ->label(__('common.fields.sort.label'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('common.fields.created_at'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->dateTime(),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('common.fields.updated_at'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->dateTime(),
            ])
            ->filters([
                ServiceBookFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                VisitTitleAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkAction::make("export")
                    ->icon('heroicon-o-document-text')
                    ->label(__('title.actions.export'))
                    ->action(fn($records) =>  \Maatwebsite\Excel\Facades\Excel::download(new \App\Exports\ServicesExport('text', $records->load('service', 'service.title', 'service.title.book')), 'services.xlsx')),
                RenameServicesBulkAction::make(),
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageTitleServiceTexts::route('/'),
        ];
    }
}

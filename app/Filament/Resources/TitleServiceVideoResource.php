<?php

namespace App\Filament\Resources;

use App\Facades\Panel;

use App\Filament\Actions\VisitTitleAction;
use App\Filament\BulkActions\RenameServicesBulkAction;
use App\Filament\Filters\ServiceBookFilter;
use App\Filament\Resources\TitleServiceVideoResource\Pages;
use App\Filament\Resources\TitleServiceVideoResource\RelationManagers;
use App\Models\Book;
use App\Models\TitleService;
use App\Models\TitleServiceVideo;
use App\Traits\MultiPanelResourse;
use Filament\Forms;
use Filament\Forms\Components\Actions\Modal\Actions\Action;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class TitleServiceVideoResource extends Resource
{
    use MultiPanelResourse;
    protected static ?string $model = TitleServiceVideo::class;

    protected static ?string $navigationIcon = 'heroicon-o-video-camera';
    protected static ?int $navigationSort = 2;

    protected static function getNavigationGroup(): ?string
    {
        return __('common.navigation_groups.book_services');
    }

    public static function getModelLabel(): string
    {
        return __('title_service_video.singular_label');
    }
    public static function getPluralModelLabel(): string
    {
        return __('title_service_video.plural_label');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('service_id'),
                Forms\Components\Toggle::make('video_type')
                    ->required(),
                Forms\Components\Textarea::make('video_file')
                    ->maxLength(65535),
                Forms\Components\TextInput::make('video_id')
                    ->maxLength(255),
                Forms\Components\Textarea::make('thumbnail')
                    ->maxLength(65535),
                Forms\Components\TextInput::make('title')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Textarea::make('detail')
                    ->required()
                    ->maxLength(65535),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('service.title.book.name')
                    ->label(__('common.fields.book_name.label'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('service.title.name')
                    ->label(__('common.fields.title_name.label'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('service.name')
                    ->label(__('common.fields.service_name.label'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('video_type')
                    ->label(__('title_service_video.fields.video_type.label'))
                    ->formatStateUsing(fn ($state) =>  __('title_service_video.options.video_type.' . $state))
                    ->sortable(),
                Tables\Columns\ImageColumn::make('thumbnail')
                    ->label(__('title_service_video.fields.thumbnail.label'))
                    ->disk('do'),
                Tables\Columns\TextColumn::make('title')
                    ->label(__('title_service_video.fields.title.label'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('detail')
                    ->label(__('title_service_video.fields.detail.label'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('common.fields.created_at'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->dateTime(),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('common.fields.updated_at'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->dateTime(),
            ])
            ->filters([
                SelectFilter::make('video_type')
                    ->label(__('title_service_video.fields.video_type.label'))
                    ->options(__('title_service_video.options.video_type')),
                ServiceBookFilter::make(),
                Filter::make('uploaded')
                    ->label('لم يكتمل رفع الملف')
                    ->query(fn (Builder $query): Builder => $query->where('video_type', 1)->whereNull('video_file'))
                    ->toggle()

            ])
            ->actions([
                Tables\Actions\Action::make('upload')
                    ->label(__('title_service_video.actions.upload_video'))
                    ->icon('heroicon-o-upload')
                    ->form([
                        Forms\Components\FileUpload::make('file')
                            ->label(__('title_service.fields.video_file.label'))
                            ->placeholder(__('title_service.fields.video_file.placeholder'))
                            ->acceptedFileTypes(['video/mp4'])
                            ->directory('videos')
                            ->disk('do')
                            ->enableDownload(),
                    ])
                    ->action(fn ($record, array $data) => $record->update(['video_file' => $data['file']]))
                    ->visible(function ($record) {
                        return $record->video_type == 1 && $record->video_file == null;
                    }),
                Tables\Actions\Action::make('show_video')
                    ->label(__('title_service_video.actions.show_video'))
                    ->url(fn (TitleServiceVideo $record) => $record->video_type == 1 ? url('uploads/' . $record->video_file) : 'https://www.youtube.com/watch?v=' . $record->video_id, shouldOpenInNewTab: true)
                    ->icon('heroicon-o-video-camera'),
                VisitTitleAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkAction::make("export")
                ->icon('heroicon-o-document-text')
                ->label(__('title.actions.export'))
                ->action(fn($records) =>  \Maatwebsite\Excel\Facades\Excel::download(new \App\Exports\ServicesExport('video', $records->load('service', 'service.title', 'service.title.book')), 'services.xlsx')),
                RenameServicesBulkAction::make(),
                Tables\Actions\DeleteBulkAction::make(),

            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageTitleServiceVideos::route('/'),
        ];
    }
}

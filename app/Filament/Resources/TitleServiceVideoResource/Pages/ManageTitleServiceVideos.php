<?php

namespace App\Filament\Resources\TitleServiceVideoResource\Pages;

use App\Filament\Resources\TitleServiceVideoResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageTitleServiceVideos extends ManageRecords
{
    protected static string $resource = TitleServiceVideoResource::class;

    protected function getActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }
}

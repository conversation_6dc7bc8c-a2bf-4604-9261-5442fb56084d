<?php

namespace App\Filament\Resources;

use App\Facades\Panel;
use App\Filament\Resources\VideoResource\Pages;
use App\Filament\Resources\VideoResource\RelationManagers;
use App\Models\Video;
use App\Traits\MultiPanelResourse;
use Closure;
use Filament\Forms;
use Filament\Forms\Components\Card;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Filament\Tables\Columns\BooleanColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ViewColumn;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\HtmlString;
use phpDocumentor\Reflection\Types\Boolean;

class VideoResource extends Resource
{
    use MultiPanelResourse;
    protected static ?string $model = Video::class;

    protected static ?string $navigationIcon = 'heroicon-o-film';

    protected static ?string $recordTitleAttribute = 'title';

    protected static ?int $navigationSort = 4;

    protected static function getNavigationGroup(): ?string
    {
        return __('common.navigation_groups.media_center');
    }

    public static function getModelLabel(): string
    {
        return __('video.singular_label');
    }
    public static function getPluralModelLabel(): string
    {
        return __('video.plural_label');
    }
    protected static function shouldRegisterNavigation(): bool
    {
        return Panel::currentPanel() == "kholasah";
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Card::make([
                    TextInput::make('title')
                        ->label(__('video.fields.title.label'))
                        ->placeholder(__('video.fields.title.placeholder'))
                        ->required()
                        ->maxLength(255),
                    Select::make('video_type')
                        ->label(__('video.fields.video_type.label'))
                        ->placeholder(__('video.fields.video_type.placeholder'))
                        ->reactive()
                        ->options(__('video.options.video_types')),
                    TextInput::make('url')
                        ->label(__('video.fields.url.label'))
                        ->placeholder(__('video.fields.url.placeholder'))
                        ->required()
                        ->hidden(function ($get) {
                            return $get('video_type') != 0 || $get('video_type') == null;
                        })
                        ->reactive()
                        ->afterStateUpdated(function (Closure $set, $state) {
                            $re = '/(?im)\b(?:https?:\/\/)?(?:w{3}\.)?youtu(?:be)?\.(?:com|be)\/(?:(?:\??v=?i?=?\/?)|watch\?vi?=|watch\?.*?&v=|embed\/|)([A-Z0-9_-]{11})\S*(?=\s|$)/';
                            if (preg_match($re, $state, $matches)) {
                                $set('url', $matches[1]);
                            }
                        })
                        ->maxLength(255),
                    Placeholder::make('video_preview')
                        ->label(__('video.fields.video_preview.label'))
                        ->content(function (Closure $get) {
                            return new HtmlString("<iframe width=\"560\" height=\"315\" src=\"https://www.youtube.com/embed/{$get('url')}\" frameborder=\"0\" allow=\"accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture\" allowfullscreen></iframe>");
                        })
                        ->hidden(function (Closure $get) {
                            return $get('url') == null || $get('video_type') != 0;
                        }),
                    FileUpload::make('file')
                        ->label(__('video.fields.file.label'))
                        ->directory('videos')
                        ->disk('do')
                        ->acceptedFileTypes(['video/mp4'])
                        ->hidden(function ($get) {
                            return $get('video_type') != 1 || $get('video_type') == null;
                        }),
                    FileUpload::make('thumb')
                        ->label(__('video.fields.thumb.label'))
                        ->directory('videos/thumbs')
                        ->disk('do')
                        ->hidden(function ($get) {
                            return $get('video_type') != 1 || $get('video_type') == null;
                        }),
                    // description
                    Textarea::make('description')
                        ->label(__('video.fields.description.label'))
                        ->placeholder(__('video.fields.description.placeholder'))
                        ->required(),

                    // get_seo_filament_fields(),
                    // is active
                    Toggle::make('is_active')
                        ->dehydrateStateUsing(function ($state) {
                            return $state == true ? '1' : '0';
                        })
                        ->label(__('video.fields.is_active.label'))
                        ->required(),
                ])->columnSpan(
                    [
                        "sm" => 2
                    ]
                ),
                Card::make([
                    Forms\Components\Placeholder::make('created_at')
                        ->label(__('common.fields.created_at'))
                        ->content(fn (?Model $record): string => $record ? $record->created_at->diffForHumans() : '-'),
                    Forms\Components\Placeholder::make('updated_at')
                        ->label(__('common.fields.updated_at'))
                        ->content(fn (?Model $record): string => $record ? $record->updated_at->diffForHumans() : '-'),
                ])->columnSpan(1),
            ])->columns(
                [
                    "sm" => 3,
                    "lg" => null,
                ]
            );
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                // id
                TextColumn::make('id')
                    ->label(__('common.fields.id'))
                    ->sortable(),
                // title
                TextColumn::make('title')
                    ->searchable()
                    ->label(__('video.fields.title.label'))
                    ->sortable(),
                ImageColumn::make('thumb')
                    ->label(__('video.fields.thumb.label'))
                    ->getStateUsing(function ($record) {
                        if ($record->video_type == 0) {
                            return $record->thumb;
                        } else {
                            return url('uploads/' . $record->thumb);
                        }
                    }),
                // is active
                IconColumn::make('is_active')
                    ->boolean()
                    ->label(__('video.fields.is_active.label'))
                    ->sortable(),
                // created_at
                TextColumn::make('created_at')
                    ->label(__('common.fields.created_at'))
                    ->sortable()
                    ->dateTime('Y-m-d h:i A'),
                // updated_at
                TextColumn::make('updated_at')
                    ->label(__('common.fields.updated_at'))
                    ->sortable()
                    ->dateTime('Y-m-d h:i A'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [

            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListVideos::route('/'),
            'create' => Pages\CreateVideo::route('/create'),
            'view' => Pages\ViewVideo::route('/{record}'),
            'edit' => Pages\EditVideo::route('/{record}/edit'),
        ];
    }
}

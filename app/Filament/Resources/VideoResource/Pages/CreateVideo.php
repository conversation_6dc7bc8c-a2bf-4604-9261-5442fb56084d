<?php

namespace App\Filament\Resources\VideoResource\Pages;

use Alaouy\Youtube\Facades\Youtube;
use App\Filament\Resources\VideoResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateVideo extends CreateRecord
{
    protected static string $resource = VideoResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        if ($data['video_type'] == 0) {
            $videoInfo =  Youtube::getVideoInfo($data['url'], ['snippet']);
            $thumbnails = $videoInfo->snippet->thumbnails;
            $thumbnail = "";
            if (isset($thumbnails->maxres)) {
                $thumbnail = $thumbnails->maxres->url;
            } elseif (isset($thumbnails->high)) {
                $thumbnail = $thumbnails->high->url;
            } elseif (isset($thumbnails->medium)) {
                $thumbnail = $thumbnails->medium->url;
            } elseif (isset($thumbnails->default)) {
                $thumbnail = $thumbnails->default->url;
            }
            $data['thumb'] = $thumbnail;
        }
        return $data;
    }
}

<?php

namespace App\Filament\Widgets;

use App\Facades\Panel;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Card;

class StatsOverview extends BaseWidget
{
    protected function getCards(): array
    {
        $books_count = 0;
        if (Panel::currentPanel() == 'kholasah') {
            $books_count = \App\Models\Book::where('type', 0)->count();
        } else {
            $books_count = \App\Models\Book::count();
        }
        // text
        $text_services_count = \App\Models\TitleServiceText::count();
        // video
        $video_services_count =  \App\Models\TitleServiceVideo::count();
        // qoutation
        $qoutation_service_count = \App\Models\TitleServiceQuotation::count();
        // reference
        $reference_service_count = \App\Models\TitleServiceReference::count();
        // activity
        $activity_service_count = \App\Models\TitleServiceActivity::count();
        // calender
        $calender_service_count = \App\Models\TitleServiceCalender::count();

        return [
            Card::make('عدد الكتب', $books_count),
            Card::make('عدد الخدمات النصية', $text_services_count),
            Card::make('عدد الخدمات المرئية', $video_services_count),
            Card::make('عدد خدمات الإقتباسات', $qoutation_service_count),
            Card::make('عدد خدمات المراجع', $reference_service_count),
            Card::make('عدد خدمات الانشطة', $activity_service_count),
            Card::make('عدد خدمات التقويم', $calender_service_count),
        ];
    }
}

<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Title;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\App;

class PdfController extends Controller
{
    // download pdf
    public function download(Request $request, $id)


    {
        $title = Title::find($id);

        $book_name = $title->book->name;
        $file_name = $title->name . ' - ' . $book_name . ".pdf";
        $footnote = "";
        foreach (footnote()->inject($title->content->text ?? "") as $key => $value) {
            $footnote .= "<p dir='rtl'> [" . strval($key + 1) . "] $value </p>";
        }
        if (!empty($footnote)) {
            $footnote = "<hr/> $footnote ";
        }
        $text =  $title->content->text;
        $pattern = '/src="([^"]+)"/';
        $text = preg_replace($pattern, 'src="'.url('/').'/$1"', $text);
        $html = $text . $footnote;
        // dd($html);
        $content = '<html dir="rtl">
                <head>
                <meta charset="utf-8" />
                <style>
                @font-face {
                    src: url("' . url('/fonts/me_quran.ttf') . '");
                    font-family: "UthmanicHafs";
                }
                @font-face {
                    src: url("' . url('/fonts/al_majeed.ttf') . '");
                    font-family: "Almajeed";
                }

                @font-face {
                    font-family: "Droid Arabic Naskh";
                    src: url("' . url('/fonts/Droid-Arabic-Naskh.ttf.woff') . '") format("woff"),
                        url("' . url('/fonts/Droid-Arabic-Naskh.ttf.svg#Droid-Arabic-Naskh') . '") format("svg"),
                        url("' . url('/fonts/Droid-Arabic-Naskh.ttf.eot') . '"),
                        url("' . url('/fonts/Droid-Arabic-Naskh.ttf.eot?#iefix') . '") format("embedded-opentype");
                    font-weight: normal;
                    font-style: normal;
                }

                @font-face {
                    font-family: "Droid Arabic Naskh";
                    src: url("' . url('/fonts/Droid-Arabic-Naskh-bold.ttf') . '") format("truetype");
                    font-weight: 700;
                    font-style: normal;
                }
                img{
                    display:none;
                }
                img[src*="uploads"]{
                    display: block !important;
                    width: 100% !important;
                    height: auto !important;
                }
                .content::first-line {
                    position: relative;
                    color: #e78c3a;
                    font-size: 2rem;
                    font-weight: bold;
                    text-decoration: underline #e78c3a 2px;
                    font-family: "Greta Arabic" !important;
                }
                .content * {
                    font-family: "Droid Arabic Naskh", serif !important;
                    text-align: justify !important;
                    line-height: 2 !important;
            top: unset !important;
            bottom: unset !important;
            right: unset !important;
            left: unset !important;
                }
                .content{}
                .ayah,
                .ayah .ayah-bracket {
    font-family: "UthmanicHafs" !important;
    color: #e78c3a;
    font-size: 1.4rem !important;
    line-height: 1.5 !important;
    font-weight: 400;
}
.ayah-bracket{
    font-family: "Almajeed", serif !important;
    color: #e78c3a;
    font-size: 1.4rem !important;

}
                </style>
                </head>
                <body class="content">
                ' . $html . '
                </body>
                </html>';
        // return $content;
        $pdf = App::make('snappy.pdf.wrapper');
        $pdf->setOption('enable-local-file-access', true);
        return $pdf->loadHTML($content)->download($file_name);
    }
}

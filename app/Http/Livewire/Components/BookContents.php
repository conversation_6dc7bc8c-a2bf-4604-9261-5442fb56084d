<?php

namespace App\Http\Livewire\Components;

use App\Facades\Panel;
use App\Models\Activity;
use App\Models\Calender;
use App\Models\Content;
use App\Models\ContentComment;
use App\Models\ContentComments;
use App\Models\ContentRate;
use App\Models\ContentRates;
use App\Models\Goal;
use App\Models\Other;
use App\Models\Quotation;
use App\Models\Reference;
use App\Models\Tadabor;
use App\Models\Title;
use App\Models\TitleService;
use App\Models\User;
use App\Models\VideoContent;
use Filament\Notifications\Notification;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Str;
use Livewire\Component;
use Livewire\Livewire;
use Livewire\LivewireManager;
use Livewire\WithPagination;


class BookContents extends Component
{
    use WithPagination;
    protected $paginationTheme = 'book';
    public  $content_id;
    public  $queryContent;
    public $title_id;
    public $queryTitle;
    // public $title;
    public $book_id;
    public $hightlight;
    public $book_cover;

    //comment
    public $comment;
    public $isCommentSave = false;

    //rate content
    public $rate;
    // public $isRateSaved = false;

    //reuest uri
    public $requestUri;

    public $isRefreshGallaryTriggered = false;

    protected $listeners = [
        'titleChanged' => 'changeTitle',
        'refreshComponent' => '$refresh'
    ];
    protected $queryString = [
        'queryContent' => ['except' => 0, 'as' => 'ctype'],
        'queryTitle' => ['as' => 't', 'except' => 0],
    ];


    public function setTitleId($id)
    {
        $this->title_id = $id;
        if ($id != 0) {
            $this->queryTitle = str($this->title->name . "-" . $id)->replace(' ', '-')->toString();
        } else {
            $this->queryTitle = 0;
        }
    }

    public function setContentId($id)
    {
        $this->content_id = (int) $id;
        if ($this->content_id != 0 && $this->book_id == 122) {
            $this->queryContent =
                match ((int)$id) {
                    1 => "الوقفات-التدبرية-$id",
                    2 => "العمل-بالآيات-$id",
                    3 => "التوجيهات-$id",
                    4 => "غريب-الكلمات-$id",
                };
        } else if ($this->content_id != 0 && $this->book_id == 115) {
            $this->queryContent = str($this->tafseers[$id]['name'])->replace(' ', '-')->toString() . "-$id";
        } else if ($this->content_id != 0) {
            $this->queryContent = str($this->Content->name . "-" . $id)->replace(' ', '-')->toString();
        } else {
            $this->queryContent = 0;
        }
    }

    public function changeTitle($id)
    {

        $this->isRefreshGallaryTriggered = false;
        if ($this->content_id != 0 && $this->book_id != 122 && $this->book_id != 115) {

            $view_type = $this->content->view_type;
            $service_name = $this->content->name;
            $this->setTitleId($id);

            $this->setContentId($this->title->services()->where('view_type', $view_type)->where('name', $service_name)?->first()->id ?? 0);
        } else {
            $this->setTitleId($id);
            // $this->title = Title::find($id);
            $this->setContentId(0);

            $ctype = str(request('ctype') ?? 0)->afterLast('-')->toString();
            if ($ctype == null && $this->book_id == 122) {
                $ctype = 1;
            }
            $this->setContentId($ctype ?? 0);
        }

        $this->emitUp('changeTitleId', $id);
    }

    public function render()
    {

        if ($this->title_id) {
            if ($this->Title) {
                //save last opend title to cookie
                Cookie::queue(
                    Cookie::forever(
                        $this->book_id . '_lastTitle_id',
                        $this->book_id . '?t=' . $this->title_id
                    ),
                );
                Cookie::queue(

                    Cookie::forever(
                        $this->book_id . '_lastTitle_title',
                        $this->Title->name
                    )
                );
            }
        }
        if ($this->content_id == 0)
            $this->emit('pageChanged');
        $this->emit('contentChanged');
        if ($this->content != null)
            $this->triggerRefreshLightgallary();
        return view('livewire.components.book-contents');
    }

    public function triggerRefreshLightgallary()
    {
        if ($this->content?->view_type == 2 && $this->isRefreshGallaryTriggered == false) {
            $this->isRefreshGallaryTriggered = true;
            $this->emit('refreshLightgallery');
        }
    }
    public function mount($book_id, $title_id, $cover)
    {
        $ctype = str(request('ctype') ?? 0)->afterLast('-')->toString();
        if ($ctype == null && $book_id == 122) {
            $ctype = 1;
        }

        if ($table = request('table')) {

            if ($table == "tadabor") {

                $verse_ids = request('verse_id') ?? [];
                sort($verse_ids);

                $title = Title::where('book_id', 122)
                    ->whereRAW(Arr::first($verse_ids) . ' >= ' . 'from_verse')
                    ->whereRAW(Arr::first($verse_ids) . ' <= ' . 'to_verse')
                    ->first();
                $title_id =  $title?->id;
            } else {

                $title_id = Title::where('book_id', 115)->whereRAW(request('verse_id') . ' >= ' . 'from_verse')->whereRAW(request('verse_id') . ' <= ' . 'to_verse')->first()->id;
                $ctype = Arr::first(
                    array_values($this->tafseers),

                    function ($tafseer) use ($table) {
                        return   $tafseer['table'] == $table;
                    }
                )["id"] ?? null;

                if ($ctype == null) {
                    $ctype = Arr::first(
                        array_values($this->ghareeb_alquran),
                        function ($tafseer) use ($table) {
                            return $tafseer['table'] == $table;
                        }
                    )["id"] ?? null;
                }
            }
        }

        $this->isRefreshGallaryTriggered = false;

        $this->setContentId($ctype ?? 0);
        $this->setTitleId($title_id);
        $this->hightlight = request('hl');
        $this->book_id = $book_id;
        $this->book_cover = $cover;
        $this->requestUri = request()->getRequestUri();
    }
    public function toggle_content(int $type)
    {
        $this->isRefreshGallaryTriggered = false;
        $this->resetPage();
        $this->setContentId($type);
    }
    public function getServicesProperty()
    {

        $services =  TitleService::select(["id", "service_type", "service_sub_type_id", "name"])->with('service_sub_type')->where('title_id', $this->title_id)
            ->when(Panel::currentPanel() == "kholasah", fn($qr) => $qr->where('service_type', 1))
            ->orderBy('sort')->get()->map(function ($e) {
                $e['service_sub_type_id'] = null;
                return $e;
            });
        if ($services->count() > 0) {
            $services->prepend(new TitleService(["id" => 0, "service_type" => 1, "name" => "الخلاصة", "sort" => 0]));
        }
        if (Panel::currentPanel() == "curriculums") {
            $data = [];
            $data[1]['items']['']['items'] = $services->map(function ($item) {
                $item->service_type = 1;
                return $item;
            })->toArray();
            $data[1]['items']['']['selected'] = $services->contains('id', $this->content_id);
            $data[1]['selected'] = true;
            // dd($data);
            return $data;
        }
        // group by service_type
        $services = $services->groupBy('service_type');
        $data = [];

        foreach ($services as $key => $value) {
            $temp = $value->groupBy('service_sub_type_id');
            foreach ($temp as $key2 => $value2) {
                $data[$key]['items'][$key2]['name'] = $value2->first()->service_sub_type?->name ?? "";
                $data[$key]['items'][$key2]['selected'] = $value2->contains('id', $this->content_id);
                $data[$key]['items'][$key2]['items'] = $value2->toArray();
            }
            $data[$key]['selected'] = $value->contains('id', $this->content_id);
        }
        if ($this->book_id == 115) {
            if ($this->title->sora_num != null) {
                $temp = [];
                foreach (__('title_service.options.tafseers') as $key => $value) {
                    $temp[$key] = $value;
                    foreach ($value['items'] as $key2 => $value2) {
                        $temp[$key]['items'][$key2]['selected'] = $value2['id'] == $this->content_id;
                    }
                    $temp[$key]['selected'] = collect($value)->contains('id', $this->content_id);
                }
                $data[1] = [
                    'items' => [
                        "" => [
                            'name' => "",
                            'selected' => false,
                            'items' => [
                                [
                                    'id' => 0,
                                    'service_type' => 1,
                                    'name' => "الخلاصة",
                                    'sort' => 0,
                                    'service_sub_type_id' => null,
                                ]
                            ]
                        ],
                        2 => [
                            'name' => "التفاسير",
                            'selected' => false,
                            'items' => [

                                $this->tafseers[1],
                                $this->tafseers[2],
                                $this->tafseers[3],
                                $this->tafseers[4],
                                $this->tafseers[5],
                                $this->tafseers[6],
                                $this->tafseers[7],
                                $this->tafseers[8],
                                $this->tafseers[9],

                            ]
                        ],
                        // 3 => [
                        //     'name' => "القرآن تدبر وعمل",
                        //     'selected' => false,
                        //     'items' => [
                        //         [
                        //             'id' => 2,
                        //             'service_type' => 1,
                        //             'name' => "ابن كثير",
                        //             'sort' => 0,
                        //             'service_sub_type_id' => 3,
                        //         ]
                        //     ]
                        // ],
                        4 => [
                            'name' => "غريب الكلمات",
                            'selected' => false,
                            'items' => [
                                $this->tafseers[12],
                                $this->tafseers[13],
                            ]
                        ],

                        5 => [
                            'name' => "القراءات",
                            'selected' => false,
                            'items' => [

                                $this->tafseers[10],
                                $this->tafseers[11],

                            ]
                        ],

                        6 => [
                            'name' => "اللغة",
                            'selected' => false,
                            'items' => [
                                $this->tafseers[14],
                                $this->tafseers[15],
                                $this->tafseers[16],
                            ]
                        ],
                    ],
                ];
            }
        } else if ($this->book_id == 122) {
            if ($this->title->sora_num != null) {
                $data[1] = [
                    'items' => [
                        "" => [
                            'name' => "",
                            'selected' => false,
                            'items' => [
                                [
                                    "id" => 1,
                                    'name' => "الوقفات التدبرية",
                                    'selected' => true,
                                    'service_type' => 1,
                                    'service_sub_type_id' => null,
                                ],
                                [
                                    "id" => 2,
                                    'name' => "العمل بالآيات",
                                    'selected' => false,
                                    'service_type' => 1,
                                    'service_sub_type_id' => null,
                                ],
                                [
                                    "id" => 3,
                                    'name' => "التوجيهات",
                                    'selected' => false,
                                    'service_type' => 1,
                                    'service_sub_type_id' => null,

                                ],
                                [
                                    "id" => 4,
                                    'name' => "غريب الكلمات",
                                    'selected' => false,
                                    'service_type' => 1,
                                    'service_sub_type_id' => null,

                                ],
                            ]
                        ],
                    ]
                ];
            }
        }
        // dd($data);
        return  $data;
    }
    public function getContentProperty()
    {

        if ($this->content_id == 0) {
            return Content::where("title_id", $this->title_id)->first();
        } else {
            if ($this->book_id == 115 && $this->content_id <= 16) {
                return $this->getContentByTypeId($this->content_id, $this->title_id);
            } else if ($this->book_id == 122) {
                return $this->getTadabor($this->content_id);
            } else {
                $content = TitleService::find($this->content_id);
                if (Panel::currentPanel() == 'curriculums') {
                    $content->service_type = 1;
                }

                return $content;
            }
        }
    }

    private $tafseers = [
        1 => [
            'id' => 1,
            'service_type' => 1,
            'name' => "ابن كثير",
            'sort' => 0,
            'table' => 'ibn_katheer',
            'service_sub_type_id' => 2,
        ],
        2 => [
            'id' => 2,
            'service_type' => 1,
            'name' => "البغوي",
            'sort' => 2,
            'table' => 'baghawy',
            'service_sub_type_id' => 2,
        ],
        3 => [
            'id' => 3,
            'service_type' => 1,
            'name' => "السعدي",
            'sort' => 5,
            'table' => 'saady',
            'service_sub_type_id' => 2,
        ],
        4 => [
            'id' => 4,
            'service_type' => 1,
            'name' => "القرطبي",
            'sort' => 3,
            'table' => 'qortoby',
            'service_sub_type_id' => 2,
        ],
        5 => [
            'id' => 5,
            'service_type' => 1,
            'name' => "الطبري",
            'sort' => 4,
            'table' => 'tabary',
            'service_sub_type_id' => 2,
        ],
        6 => [
            'id' => 6,
            'service_type' => 1,
            'name' => "ابن عاشور",
            'sort' => 1,
            'table' => 'tanweer',
            'service_sub_type_id' => 2,
        ],
        7 => [
            'id' => 7,
            'service_type' => 1,
            'name' => "ابن الجوزي",
            'sort' => 7,
            'table' => 'zad_almaseer',
            'service_sub_type_id' => 2,
        ],
        8 => [
            'id' => 8,
            'service_type' => 1,
            'name' => "البقاعي",
            'sort' => 8,
            'table' => 'nathm_aldurar',
            'service_sub_type_id' => 2,
        ],
        9 => [
            'id' => 9,
            'service_type' => 1,
            'name' => "ابن القيم",
            'sort' => 6,
            'table' => 'ibn_alqayyem',
            'service_sub_type_id' => 2,
        ],



        //qiraat
        10 => [
            'id' => 10,
            'service_type' => 1,
            'name' => "تحبير التيسير",
            'sort' => 0,
            'table' => 'tahbeer_altayseer',
            'service_sub_type_id' => 5,
        ],
        11 => [
            'id' => 11,
            'service_type' => 1,
            'name' => "الموسوعة القرآنية",
            'sort' => 1,
            'table' => 'qiraat_almawsoah',
            'service_sub_type_id' => 5,
        ],

        //ghateeb
        12 => [
            'id' => 12,
            'service_type' => 1,
            'name' => "السراج في بيان غريب الكلمات",
            'sort' => 0,
            'table' => 'siraaj_ghareeb',
            'service_sub_type_id' => 4,
        ],
        13 => [
            'id' => 13,
            'service_type' => 1,
            'name' => "غريب القرآن لابن قتيبة",
            'sort' => 1,
            'table' => 'ghareeb_ibn_qutaybah',
            'service_sub_type_id' => 4,
        ],
        //lang
        14 => [
            'id' => 14,
            'service_type' => 1,
            'name' => "إعراب القرآن وبيانه",
            'sort' => 0,
            'table' => 'iraab_aldarweesh',
            'service_sub_type_id' => 6,
        ],
        15 => [
            'id' => 15,
            'service_type' => 1,
            'name' => "الجدول في الإعراب ",
            'sort' => 1,
            'table' => 'aljadwal',
            'service_sub_type_id' => 6,
        ],
        16 => [
            'id' => 16,
            'service_type' => 1,
            'name' => "إعراب القران للنحاس",
            'sort' => 2,
            'table' => 'iraab_alnahas',
            'service_sub_type_id' => 6,
        ],

    ];


    private $ghareeb_alquran = [
        [
            'id' => 7,
            'service_type' => 1,
            'name' => "التنوير",
            'sort' => 1,
            'table' => 'aljadwal',
            'service_sub_type_id' => 2,
        ],
        8 => [
            'id' => 8,
            'service_type' => 1,
            'name' => "التنوير",
            'sort' => 1,
            'table' => 'ghareeb_ibn_qutaybah',
            'service_sub_type_id' => 2,
        ],
        9 => [
            'id' => 9,
            'service_type' => 1,
            'name' => "التنوير",
            'sort' => 1,
            'table' => 'ibn_alqayyim',
            'service_sub_type_id' => 2,
        ],
        10 => [
            'id' => 10,
            'service_type' => 1,
            'name' => "التنوير",
            'sort' => 1,
            'table' => 'iraab_aldarweesh',
            'service_sub_type_id' => 2,
        ],
        11 => [
            'id' => 11,
            'service_type' => 1,
            'name' => "التنوير",
            'sort' => 1,
            'table' => 'iraab_alnahas',
            'service_sub_type_id' => 2,
        ],
        12 => [
            'id' => 13,
            'service_type' => 1,
            'name' => "التنوير",
            'sort' => 1,
            'table' => 'nathm_aldurar',
            'service_sub_type_id' => 2,
        ],
        13 => [
            'id' => 13,
            'service_type' => 1,
            'name' => "التنوير",
            'sort' => 1,
            'table' => 'qiraat_almawsoah',
            'service_sub_type_id' => 2,
        ],
        14 => [
            'id' => 14,
            'service_type' => 1,
            'name' => "التنوير",
            'sort' => 1,
            'table' => 'siraaj_ghareeb',
            'service_sub_type_id' => 2,
        ],
        15 => [
            'id' => 15,
            'service_type' => 1,
            'name' => "التنوير",
            'sort' => 1,
            'table' => 'tahbeer_altayseer',
            'service_sub_type_id' => 2,
        ],

        18 => [
            'id' => 18,
            'service_type' => 1,
            'name' => "التنوير",
            'sort' => 1,
            'table' => 'tahbeer_altayseer',
            'service_sub_type_id' => 2,
        ]
    ];


    public function getContentByTypeId($type_id, $title_id)
    {
        $table = $this->tafseers[$type_id]['table'];
        $title = $this->title;
        $data = (object)[
            "service_type" => $this->tafseers[$type_id]['service_type'],
            "service_sub_type_id" => $this->tafseers[$type_id]['service_sub_type_id'],
            "view_type" => 8,
            "data" => DB::table($table)
                ->whereBetween('verse_id', [$title->from_verse, $title->to_verse])
                ->whereNotNull('text')
                ->join('quran', "quran.id", '=',  "$table.verse_id")
                ->select(["$table.*", "quran.aya_with_diac"])
                ->get(),
        ];
        // dd($data);
        // dd($data);
        // return null;
        return $data;
    }

    public function getTadabor($type_id)
    {

        $title = $this->title;
        // dd($this->title_id);
        $data = (object)[
            "service_type" => 1,
            "service_sub_type_id" => null,
            "view_type" => 9,
            "data" => Tadabor::whereNotNull('text')
                ->where("type_id", $type_id)
                ->withWhereHas("verses", function ($query) use ($title) {
                    $query->whereBetween('verse_id', [$title?->from_verse ?? 0, $title?->to_verse ?? 0]);
                })
                ->with("verses.verse")

                // ->join('quran', "quran.id", '=',  "$table.verse_id")

                ->get(),
        ];



        return $data;
    }

    public function getNextTitleProperty()
    {
        return $this->title->getNextTitle()?->id ?? 0;
    }
    public function getPreviousTitleProperty()
    {
        return $this->title->getPreviousTitle()?->id ?? 0;
    }

    public function getPageUrlProperty()
    {
        $qs = "";
        if ($this->title_id != 0) {
            $qs .= "t=" . $this->title_id . "&";
        }
        if ($this->content_id != 1) {
            $qs .= "ctype=" . $this->content_id . "&";
        }
        if ($this->page > 1) {
            $qs .= "page=" . $this->page . "&";
        }
        $qs = Str::replaceLast('&', '', $qs);
        if (!empty($qs)) {
            $qs = "?" . $qs;
        }
        return urlencode(url("book/$this->book_id" . $qs));
    }
    public function getTitleProperty()
    {
        return Title::where('id', $this->title_id)
            ->first();
    }


    public function saveComment()
    {

        $this->validate(['comment' => ['required_without:rate']], [
            'comment.required_without' => 'حقل التعليق او الملاحظة مطلوب'
        ]);

        //
        if ($this->comment) {

            $this->content->comments()->create([
                'comment' => $this->comment
            ]);
        }

        $this->content->rates()->create([
            'stars' => $this->rate
        ]);

        $this->isCommentSave = true;

        Cookie::queue(
            Cookie::forever(
                'content_rate_star_' .  $this->content->id,
                $this->rate,
            ),
        );


        $users = User::where('is_admin', 1)->get();


        foreach ($users as $user) {
            $user->notify(Notification::make()
                ->title('ملاحظة جديدة على الخلاصة')
                ->toDatabase($users));
        }
    }

    public function rateContent($rate)
    {
        $this->rate = $rate;
    }


    public function getRateContentByCookies($content_id)
    {
        return  Cookie::get('content_rate_star_' .  $this->content->id);
    }

    public function resetValue(array $array)
    {
        $this->reset($array);
    }


    public function getMetaProperty()
    {

        if ($this->content_id == 0 && $this->title?->CachedSeo()) {
            return $this->title?->CachedSeo();
        } elseif ($this->content_id != 0 && !($this->book_id == 115 || $this->book_id == 122)) {
            return $this->Content?->CachedSeo();
        }
        // else {
        //     $this->title->book?->CachedSeo();
        // }
    }
}

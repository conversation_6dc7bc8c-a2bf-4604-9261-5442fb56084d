<?php

namespace App\Http\Livewire\Components;

use App\Facades\Panel;
use App\Models\Book;
use Livewire\Component;

class Books extends Component
{
    public $selected_id;
    public $from;
    public $platform;
    public $to;
    public function render()
    {
        $this->emit('bookChanged');
        if ($this->platform == 'religions') {
            return view('livewire.components.religion-books ');
        }
        return view('livewire.components.books');
    }
    public function mount()
    {
        $this->platform = Panel::currentPanel();
        $this->selected_id = 0;
        $this->from = cache()->get('from');
        $this->to = cache()->get('to');
    }
    public function getBookProperty()
    {
        return Book::find($this->selected_id);
    }
    public function getBooksProperty()
    {
        if ($this->platform == 'kholasah') {
            return Book::where('published', true)->where('type', 0)->select(["id", "name"])->orderBy('sort', 'asc')->get()->groupBy('type');
        }
        if ($this->platform == 'religions') {
            return Book::where('published', true)->orderBy('type')->orderBy('sort', 'asc')->get()->groupBy('type');
        }
        return Book::where("published", true)->select(["id", "name"])->orderBy('sort', 'asc')->get()->groupBy('type');
    }
    public function toggle_book(int $id)
    {
        $this->selected_id = $id;
    }
}

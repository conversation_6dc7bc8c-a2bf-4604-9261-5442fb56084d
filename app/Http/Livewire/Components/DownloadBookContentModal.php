<?php

namespace App\Http\Livewire\Components;

use App\Models\Book;
use App\Models\Title;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Storage;
use League\CommonMark\Extension\CommonMark\Node\Inline\Strong;
use Livewire\Component;
use Symfony\Component\HttpFoundation\StreamedResponse;

class DownloadBookContentModal extends Component
{

    public  $book_id;
    public $title_id;


    public $from_title;
    public $to_title;

    public $closeModal = null;

    public $download_type = "current_page";


    protected $listeners = [
        'closeModalFun'
    ];


    public function render()
    {
        return view('livewire.components.download-book-content-modal');
    }

    public function mount()
    {
        $superParentTitleId = $this->getSuperParentTitle($this->title_id);
        $this->from_title = $superParentTitleId;
        $this->to_title = $superParentTitleId;

        if (!empty($this->book->files)) {
            $this->book_file = array_values($this->book->files)[0]['file'];
        }
    }


    public function getTitlesProperty()
    {

        return Cache::remember("book." . $this->book_id . ".titles", 1 * 60, function () {
            $titles = Title::with([
                "subtitles" => function ($query) {
                    $query->orderBy('order');
                }
            ])->where("book_id", $this->book_id)->where("parent_id", -1)->orderBy('order')->get();
            return $titles;
        });
    }


    public function getSuperParentTitle($title_id)
    {
        $titles = [];
        foreach ($this->Titles as $title) {

            $titles[$title->id] = $this->get_last_titles_id($title);
        }

        $key = collect(Arr::dot($titles))->where('id', $title_id)->keys()->first();
        $superParentId = Arr::first(explode('.', $key ?? ''));
        return $superParentId;
    }

    public function save()
    {
        $this->validate(
            [
                'from_title' => 'required',
                'to_title' => 'required',

            ],
            [
                'from_title.required' => 'حقل  من العنوان  مطلوب',
                'to_title.required' => 'حقل الى العنوان  مطلوب',

            ]
        );
        //

        if ($this->download_type != null && $this->download_type == 'current_page') {
            //TODO download pdf
            $output = download_titles($this->title_id,   $this->title_id);
            $file_name = now() . ".pdf";

            $this->closeModal = true;

            $this->emit('closeModalFun');
            return $this->download($output, $file_name);
        }
        //TODO download pdf
        $output = download_titles($this->from_title, $this->to_title);
        $file_name = now() . ".pdf";

        $this->closeModal = true;

        $this->emit('closeModalFun');
        return $this->download($output, $file_name);
    }

    public function getBookProperty()
    {
        if (auth("web")->check()) {
            return Book::find($this->book_id);
        } else {
            return Book::where("published", true)->where("id", $this->book_id)->first();
        }
    }


    public function download($output, $filename)
    {


        return new StreamedResponse(function () use ($output) {
            echo ($output);
            flush();
        }, 200, array(
            'Content-Type' => 'application/pdf',
            'Content-Length' => strlen($output),
            'Content-Disposition' =>  'attachment; filename="' . $filename . '"'
        ),);
    }



    public function closeModalFun()
    {
        $this->closeModal = false;
    }


    function get_last_titles_id($title)
    {

        $titles = [];

        if ($title->subtitles->count() == 0) {
            return ($title);
        }

        foreach ($title->subtitles as $tit) {
            $titles[$tit->id] = $this->get_last_titles_id($tit);
        }

        return $titles;
    }
}

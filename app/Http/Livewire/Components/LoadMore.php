<?php

namespace App\Http\Livewire\Components;

use App\Http\Livewire\Traits\WithSearch;
use App\Models\Content;
use App\Models\VideoContent;
use Livewire\Component;
use Livewire\LivewireManager;
use ProtoneMedia\LaravelCrossEloquentSearch\Search;

class LoadMore extends Component
{
    use WithSearch;
    public $readyToLoadMore = false;
    public $page;
    public $search;
    public function render()
    {
        return view('livewire.components.load-more', [
            "result" => $this->readyToLoadMore ? $this->getSearchResult($this->page) : null
        ]);
    }
    public function mount($search, $page, $is_advanced_search, $services, $book, $type)
    {
    

        $this->page = $page;
        $this->search = $search;
        $this->is_advanced_search = $is_advanced_search ?? 0;
        $this->services = $services ?? [];
        $this->book = $book;
        $this->type = $type ?? 1;
    }

    public function loadMore()
    {
        $this->readyToLoadMore = true;
    }
}

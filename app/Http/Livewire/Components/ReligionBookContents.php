<?php

namespace App\Http\Livewire\Components;

use App\Models\Content;
use App\Models\Title;

use Illuminate\Support\Facades\Cookie;

use Illuminate\Support\Str;
use Livewire\Component;

use Livewire\WithPagination;


class ReligionBookContents extends Component
{
    use WithPagination;
    protected $paginationTheme = 'book';

    public $title_id;
    // public $title;
    public $book_id;
    public $hightlight;
    public $book_cover;
    //reuest uri
    public $requestUri;


    protected $listeners = [
        'titleChanged' => 'changeTitle',
        'refreshComponent' => '$refresh'
    ];
    protected $queryString = [
        'title_id' => ['as' => 't', 'except' => 0],
    ];

    public function mount($book_id, $title_id, $cover)
    {
        $ctype = request('ctype');
        $this->title_id = $title_id;
        $this->hightlight = request('hl');
        $this->book_id = $book_id;
        $this->book_cover = $cover;
        $this->requestUri = request()->getRequestUri();
    }



    public function changeTitle($id)
    {
        $this->title_id = $id;
    }

    public function render()
    {
        return view('livewire.components.religion-book-contents');
    }


    // public function toggle_content(int $type)
    // {
    //     $this->isRefreshGallaryTriggered = false;
    //     $this->resetPage();
    //     $this->content_id = $type;
    // }

    public function getNextTitleProperty()
    {
        return $this->title->getNextTitle()?->id ?? 0;
    }
    public function getPreviousTitleProperty()
    {
        return $this->title->getPreviousTitle()?->id ?? 0;
    }

    public function getPageUrlProperty()
    {
        $qs = "";
        if ($this->title_id != 0) {
            $qs .= "t=" . $this->title_id . "&";
        }
       
        return urlencode(url("book/$this->book_id" . $qs));
    }


    public function getTitleProperty()
    {
        return Title::where('id', $this->title_id)
            ->first();
    }




    public function resetValue(array $array)
    {
        $this->reset($array);
    }

    public function getTitlesProperty()
    {
        $titles = Title::with([
            "subtitles" => function ($query) {
                $query->orderBy('order');
            }
        ])->where("book_id", $this->book_id)->where("parent_id", -1)->orderBy('order')->get();
        return $titles;
    }


    public function getContentProperty()
    {

        return Content::where("title_id", $this->title_id)->first();

    }
}

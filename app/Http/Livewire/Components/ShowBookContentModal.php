<?php

namespace App\Http\Livewire\Components;

use App\Models\Content;
use App\Models\Title;
use App\Models\TitleService;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Response;
use Livewire\Component;
use Symfony\Component\HttpFoundation\StreamedResponse;

class ShowBookContentModal extends Component
{

    public $closeModal = null;

    public $content = null;
    public $title = null;



    protected $listeners = ['loadContent' => 'loadContent'];


    public function render()
    {
        return view('livewire.components.show-book-content-modal');
    }

    public function mount() {}



    public function loadContent($url)
    {
        $title_id = str($url)->after("?t=")->afterLast("-")->toString() ?? 0;
        $book_id = str($url)->after("book/")->before("?t=")->afterLast("-")->toString() ?? 0;
        $ctype = str($url)->contains("ctype=") ? (str($url)->after("ctype=")->after("-")->toString() ?? 0) : 0;

        $domain = str($url)->before("/book/")
            ->replace("https://", "")
            ->replace("http://", "")->toString() ?? config('panels.panels.kholasah.domain');
        // dd($ctype);
        $panel = $this->getPanelFromDomain($domain);
        $connection = $panel['database_connection'];
        if (empty($ctype) || $ctype == 0) {
            $this->content = Content::on($connection)->where('title_id', $title_id)->first();
        } else {
            $this->content = TitleService::on($connection)->find($ctype);
        }
        $this->title = Title::on($connection)->where('id', $title_id)->first();
        // dd($this->title);
    }

    private function getPanelFromDomain($domain)
    {
        $panels = config('panels.panels');
        $panel = Arr::first($panels, function ($panel) use ($domain) {
            return $panel['domain'] === $domain;
        });

        return $panel;
    }
}

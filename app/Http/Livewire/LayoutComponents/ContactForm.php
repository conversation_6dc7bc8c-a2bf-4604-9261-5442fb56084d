<?php

namespace App\Http\Livewire\LayoutComponents;

use App\Models\Contact;
use Illuminate\Support\Facades\Mail;
use Livewire\Component;

class ContactForm extends Component
{
    public $name;
    public $email;
    public $message;
    public $isSent = false;

    protected $rules = [
        'name' => 'required|min:6',
        'email' => 'required|email',
        'message' => 'required',
    ];

    public function submit()
    {
        $this->validate(null, [
            "name.required" => ":attribute مطلوب",
            "email.required" => ":attribute مطلوب",
            "message.required" => ":attribute مطلوب",
            "email.email" => ":attribute يجب ان يكون بريد الكتروني صالح",
            "name.min" => ":attribute يجب ان يكون على الاقل :min حروف",
        ], [
            "name" => "اسمك",
            "email" => "بريدك الالكتروني",
            "message" => "الرسالة",
        ],);

        // Execution doesn't reach here if validation fails.

        $result =  Contact::create([
            'name' => $this->name,
            'email' => $this->email,
            'message' => $this->message,
        ]);
        if ($result != null) {
            $this->isSent = true;
            Mail::to('<EMAIL>')->send(new \App\Mail\ContactMail($result));
        }
    }
    // protected $listeners = ['testEvent' => 'test'];

    // public function test()
    // {
    //     $this->name = "aymen";
    // }
    public function render()
    {
        return view('livewire.layout-components.contact-form');
    }
}

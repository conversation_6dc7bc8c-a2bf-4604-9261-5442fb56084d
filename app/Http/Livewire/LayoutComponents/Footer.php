<?php

namespace App\Http\Livewire\LayoutComponents;

use App\Models\SocialMedia;
use App\Models\Page;
use Illuminate\Support\Facades\DB;
use Livewire\Component;

class Footer extends Component
{
    public $medias;
    public $with_contact;
    public function render()
    {
        return view('livewire.layout-components.footer');
    }
    public function mount($with_contact)
    {
        $this->with_contact = $with_contact;
        $this->medias = cache()->remember('media', 120, function () {
            return SocialMedia::where("active", true)->get();
        });
    }
    public function getPagesProperty()
    {
        return Page::all();
    }
}

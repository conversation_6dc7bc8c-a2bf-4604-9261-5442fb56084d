<?php

namespace App\Http\Livewire\LayoutComponents;

use App\Models\Service;
use Illuminate\Support\Facades\DB;
use Livewire\Component;

class Navbar extends Component
{
    public $services;
    public function mount()
    {
        $this->services = cache()->remember('services', 120, function () {
            return Service::all();
        });
    }
    public function render()
    {
        return view('livewire.layout-components.navbar');
    }
}

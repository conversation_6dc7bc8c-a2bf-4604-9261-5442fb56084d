<?php

namespace App\Http\Livewire\LayoutComponents;

use App\Http\Livewire\Traits\WithSearch;
use App\Models\Book;
use App\Models\TitleService;
use Illuminate\Support\Facades\Cache;
use Livewire\Component;

class Search extends Component
{
    use WithSearch;
    public $from;
    public $to;
    public function mount()
    {
        $this->from = cache()->get('from');
        $this->to = cache()->get('to');
        $this->services = request("services") ?? [1, 2, 3, 4, 5, 6, 7, 8];
    }
    public function render()
    {
        if ($this->is_advanced_search) {
            $this->emit('advancedSearchChanged');
        }
        return view('livewire.layout-components.search');
    }
    public function toggleAdvancedSearch()
    {
        // $this->readyToSearch = false;
        $this->is_advanced_search = !$this->is_advanced_search;
    }
    public function getBooksProperty()
    {
        return Cache::remember(\App\Facades\Panel::currentPanel() .".published_books", 60, function () {
            return Book::
            // where("type", ">=", $this->from)
            // ->where('type', '<=', $this->to)
            
            when(\App\Facades\Panel::currentPanel() == "kholasah", fn ($qr) => $qr->where("type", 0))->
            where("published", true)->orderBy('sort')->get();
        });
    }

    public function getBookSinceServicesProperty()
    {
        $services = TitleService::whereHas('title.book', function ($query) {
            $query->where('id', $this->book)->where('published', true);
        })
            ->where('service_type', 1)
            ->distinct('name')->pluck('name',);

        return $services;
    }


    public function getBookEducationServicesProperty()
    {
        $services = TitleService::whereHas('title.book', function ($query) {
            $query->where('id', $this->book)->where('published', true);
        })
            ->where('service_type', 2)
            ->distinct('name')->pluck('name',);

        return $services;
    }


    
    protected $queryString = [

        'is_advanced_search' => [
            'as' => 'is_advanced',
            'except' => false
        ]

    ];
}

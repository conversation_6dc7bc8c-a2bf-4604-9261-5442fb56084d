<?php

namespace App\Http\Livewire\Pages;

use App\Models\AboutKholasa;
use App\Models\Book;
use Livewire\Component;

class AboutKholasaPage extends Component
{
    public $selected_id;
    public $from;
    public $to;

    public function render()
    {
        $data = AboutKholasa::first();
        return view('livewire.pages.about-kholasa-page', ['title' => 'الخلاصة العلمية', 'data' => $data])
            ->layoutData([
                'title' => 'التعريف بالخلاصة العلمية',
                "with_contact" => true,
                'hasMeta' => true
            ]);
    }

    public function mount()
    {
        $this->selected_id = 0;
        $this->from = cache()->get('from');
        $this->to = cache()->get('to');
    }
    public function getBooksProperty()
    {
        return Book::
            // where("type", ">=", $this->from)->where('type', '<=', $this->to)->
            where("published", true)
            ->where('type', 0)
            ->select(["id", "name", 'kholasa_name', "overview"])->orderBy('sort', 'asc')->get();
    }
}

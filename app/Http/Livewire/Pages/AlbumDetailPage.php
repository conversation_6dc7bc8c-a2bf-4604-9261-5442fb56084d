<?php

namespace App\Http\Livewire\Pages;

use App\Models\Album;
use App\Models\Files;
use Livewire\Component;

class AlbumDetailPage extends Component
{
    public $title = "test";
    public $album_id;
    public function render()
    {
        $album = Album::find($this->album_id);
        $images = Files::where(["type_file" => "albums", "type_id" => $this->album_id])->get();
        $other_albums = Album::where("id", "!=", $this->album_id)->inRandomOrder()->limit(3)->get();

        return view('livewire.pages.album-detail-page', [
            "album" => $album,
            "images" => $images,
            "other_albums" => $other_albums,
        ])
            ->layoutData(['title' => $album->name, 'hasMeta' => true]);
    }
    public function mount($id)
    {
        $this->album_id = $id;
    }
    public function getPageUrlProperty()
    {
        return url('album/' . $this->album_id);
    }
}

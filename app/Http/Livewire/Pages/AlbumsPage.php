<?php

namespace App\Http\Livewire\Pages;

use App\Models\Album;
use Illuminate\Support\Carbon;
use Livewire\Component;
use  Livewire\WithPagination;

class AlbumsPage extends Component
{
    use WithPagination;
    protected $paginationTheme = 'bootstrap';
    public $title = 'البومات الصور';
    public function render()
    {
        Carbon::setLocale('ar');
        $this->title = __('frontend/albums.title');
        $albums = Album::where("is_active", true)->orderBy("created_at", "desc")->paginate(9);
        return view('livewire.pages.albums-page', [
            "albums" => $albums
        ])
            ->layoutData(
                [
                    'title' => $this->title,
                ]
            );
    }
    public function format($date)
    {
        return Carbon::parse($date)->translatedFormat('l j F Y g:i a');
    }
}

<?php

namespace App\Http\Livewire\Pages;

use App\Models\Book;
use Livewire\Component;

class BookDownloadsPage extends Component
{
    public $book;
    public function render()
    {
        return view('livewire.pages.book-downloads-page')
            ->layoutData([
                'title' => "تحميل كتاب " . $this->book->name,
                'with_contact' => false,
            ]);;
    }
    // mount
    public function mount(Book $book)
    {
        $this->book = $book;
    }
}

<?php

namespace App\Http\Livewire\Pages;

use App\Facades\Panel;
use App\Models\Book;
use App\Models\Title;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Route;
use Livewire\Component;

class BookPage extends Component
{
    public $title_id;
    public $from;
    public $to;
    public int $book_id;
    public $page_url;
    public $search = '';

    protected $listeners = [
        'changeTitleId' => 'changeTitleId',
        'refreshComponent' => '$refresh'
    ];

    public function render()
    {
        $title = Title::where('id', $this->title_id)->select(["name", "id", "parent_id"])->get()->first();
        // title name
        $titleName = "";
        if ($title != null)
            $titleName = $this->getTitlesName($title);
        // dump($titleName);
        $this->emit('pageChanged');
        $platform = Panel::currentPanel();
        $view = "livewire.pages.book-page";

        if ($platform == 'religions') {
            $view = "livewire.pages.religions.book-page";
        }

        return view($view, [
            'title' => $this->book->name ?? "الكتاب غير موجود",
        ])->layout($platform == "religions" ? "layouts.religion-app" : 'layouts.master')
            ->layoutData(
                [
                    "hasMeta" => true,
                    'title' => $this->title_id == 0 ? ($this->book->name ?? "الكتاب غير موجود") : "",
                    'subtitle' => $titleName,
                ]
            );
    }

    public function getTitlesName($title)
    {

        return  $title->name;
    }

    public function mount(string $id)
    {
        $this->book_id = str($id)->afterLast('-')->toInteger();

        $title = request()->get('t') ?? 0;
        $this->title_id = str($title)->afterLast("-")->toString() ?? 0;

        $this->page_url = Request::fullUrl();
        $this->from = cache()->get('from');
        $this->to = cache()->get('to');
    }
    public function getTitlesProperty()
    {

        if (!empty($this->search)) {
            return Title::where(
                [
                    ["name", 'like', "%$this->search%"],
                    ["book_id", $this->book_id],
                    ["parent_id", -1]
                ]
            )->orWhereHas('subtitles', function ($query) {
                $query->where(
                    [
                        ['name', 'like', '%' . $this->search . '%'],
                        ["book_id", $this->book_id],

                    ]
                );
            })->with(["subtitles" => function ($query) {
                $query->where('name', 'like', "%$this->search%")
                    ->orderBy('order');
            }])->without('parent')->orderBy('order')->get();
        } else {
            return Cache::remember(\App\Facades\Panel::currentPanel() . "_book." . $this->book_id . ".titles", 1 * 60, function () {
                $titles = Title::with([
                    "subtitles" => function ($query) {
                        $query->orderBy('order');
                    }
                ])->where("book_id", $this->book_id)->where("parent_id", -1)->orderBy('order')->get();
                return $titles;
            });
        }
    }
    public function getBooksProperty()
    {
        return Cache::remember(\App\Facades\Panel::currentPanel() . ".published_books", 60, function () {

            return Book::when(\App\Facades\Panel::currentPanel() == "kholasah", fn($qr) => $qr->where("type", 0))
                ->where("published", true)->orderBy('sort')->get();
        });
    }
    public function getBookProperty()
    {
        if (auth("web")->check()) {
            return Book::find($this->book_id);
        } else {
            return Book::where("published", true)->where("id", $this->book_id)->first();
        }
    }

    public function toggle_title(int $id)
    {
        $this->page_url = str_replace("t=" . $this->title_id, "t=" . $id, $this->page_url);
        $this->title_id = $id;
    }


    public function get_item_subtitles($item)
    {

        if (count($item->subtitles) > 0) {
            $titles = collect([]);
            foreach ($item->subtitles as $value) {
                $titles->add($this->get_item_subtitles($value));
            }
            return $titles;
        } else {
            return $item;
        }
    }

    public function changeTitleId(int $id)
    {
        $this->title_id = $id;
    }
}

<?php

namespace App\Http\Livewire\Pages;

use App\Facades\Panel;
use Livewire\Component;

class BooksPage extends Component
{
    public $title = "منصة الخلاصة";

    public function render()
    {
        $platform = Panel::currentPanel();
        $view = 'livewire.pages.books-page';
        if ($platform == 'curriculums') {
            $view = 'livewire.pages.curriculums.books-page';
            $this->title = "منصة المناهج التعليمية";
        } elseif ($platform == 'religions') {
            $view = 'livewire.pages.religions.books-page';
            $this->title = "منصة الحوار الحضاري";
        }


        return view($view)
            ->layout($platform == "religions" ? "layouts.religion-app" : 'layouts.master')
            ->layoutData(
                [
                    'title' => $this->title,
                ]
            );
    }
}

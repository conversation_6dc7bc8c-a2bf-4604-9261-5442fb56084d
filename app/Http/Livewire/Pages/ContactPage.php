<?php

namespace App\Http\Livewire\Pages;

use App\Facades\Panel;
use Livewire\Component;
use App\Models\MailListSubscription;

class ContactPage extends Component
{
    public $type = "1";
    public $name;
    public $email;
    public $country;
    public $city;
    public $organization_name;
    public $student_count;
    public $title = "تواصل معنا";
    public bool $isSent = false;
    public function render()
    {
        $platform = Panel::currentPanel();
        $view = "livewire.pages.contact-page";
        if ($platform == "religions") {
            $view = "livewire.pages.religion-contact-page";
        }
        return view($view)
            ->layout($platform == "religions" ? "layouts.religion-app" : 'layouts.master')
            ->layoutData([
                'title' => $this->title,
                'with_contact' => true,
            ]);
    }
    protected $rules = [
        'name' => 'required|min:6',
        'email' => 'required|email',
        // country
        'country' => 'required',
        // city
        'city' => 'required',
        // organization_name
        'organization_name' => 'requiredif:type,2',
        // student_count
        'student_count' => 'requiredif:type,2',

    ];

    public function submit()
    {
        $this->validate(null, [
            "name.required" => ":attribute مطلوب",
            "email.required" => ":attribute مطلوب",
            "country.required" => ":attribute مطلوب",
            "city.required" => ":attribute مطلوب",
            "organization_name.requiredif" => ":attribute مطلوب",
            "student_count.requiredif" => ":attribute مطلوب",
            "email.email" => ":attribute يجب ان يكون بريد الكتروني صالح",
            "name.min" => ":attribute يجب ان يكون على الاقل :min حروف",
        ], [
            "name" => "الاسم كامل",
            "email" => "البريدك الالكتروني",
            "country" => "الدولة",
            "city" => "المدينة",
            "organization_name" => "اسم الجهة",
            "student_count" => "عدد الطلاب",

        ],);


        // Execution doesn't reach here if validation fails.

        $result = MailListSubscription::create([
            'name' => $this->name,
            'email' => $this->email,
            'country' => $this->country,
            'city' => $this->city,
            'organization_name' => $this->organization_name,
            'student_count' => $this->student_count,
            'type' => $this->type,
        ]);
        if ($result != null) {
            $this->isSent = true;
        }
    }
}

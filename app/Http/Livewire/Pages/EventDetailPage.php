<?php

namespace App\Http\Livewire\Pages;

use App\Models\Event;
use Illuminate\Support\Carbon;
use Livewire\Component;

class EventDetailPage extends Component
{
    public $event_id;
    public function render()
    {
        Carbon::setLocale('ar');
        $event = Event::find($this->event_id);
        $other_events = Event::where("id", "!=", $this->event_id)->inRandomOrder()->limit(3)->get();

        return view(
            'livewire.pages.event-detail-page',
            [
                "event" => $event,
                'other_events' => $other_events,
            ]
        )
            ->layoutData(
                [
                    'title' => $event->title,
                    'hasMeta' => true
                ]
            );
    }
    public function mount($id)
    {
        $this->event_id = $id;
    }
    public function getPageUrlProperty()
    {
        return url('event/' . $this->event_id);
    }
    public function format($date)
    {
        return Carbon::parse($date)->translatedFormat('l j F Y g:i a');
    }
}

<?php

namespace App\Http\Livewire\Pages;

use App\Models\Event;
use Illuminate\Support\Carbon;
use Livewire\Component;
use Livewire\WithPagination;

class EventsPage extends Component
{
    public $title = 'الفعاليات';

    use WithPagination;
    protected $paginationTheme = 'bootstrap';
    public function render()
    {
        Carbon::setLocale('ar');
        $this->title = __('frontend/events.title');
        $events = Event::where("is_active", true)->orderBy("created_at", "desc")->paginate(9);
        return view('livewire.pages.events-page', [
            "events" => $events
        ])
            ->layoutData(
                [
                    'title' => $this->title,
                ]
            );
    }
    public function format($date)
    {
        return Carbon::parse($date)->translatedFormat('l j F Y g:i a');
    }
}

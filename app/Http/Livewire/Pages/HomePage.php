<?php

namespace App\Http\Livewire\Pages;

use App\Models\Album;
use App\Models\Event;
use App\Models\News;
use App\Models\Service;
use App\Models\Video;
use Illuminate\Support\Facades\DB;
use Livewire\Component;

class HomePage extends Component
{
    public $services;
    public $latest_news;
    public $latest_event;
    public $latest_video;
    public $latest_album;
    public function mount()
    {
        $this->services = cache()->remember('services', 120, function () {
            return DB::table('services')->get();
        });
        $this->latest_news = News::where('is_active', true)->orderBy('created_at', 'desc')->first();
        $this->latest_event = Event::where('is_active', true)->orderBy('created_at', 'desc')->first();
        $this->latest_video = Video::where('is_active', true)->orderBy('created_at', 'desc')->first();
        $this->latest_album = Album::where('is_active', true)->orderBy('created_at', 'desc')->first();
    }
    public function render()
    {
        return view('livewire.pages.home-page')
            ->layoutData([
                'title' => 'الرئيسية',
                "with_contact" => true,
            ]);
    }
}

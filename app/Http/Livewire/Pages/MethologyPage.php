<?php

namespace App\Http\Livewire\Pages;

use App\Models\Book;
use Livewire\Component;

class MethologyPage extends Component
{
    public $title = "منهج العمل";
    public $book_id;
    public function render()
    {
        return view('livewire.pages.methology-page', [
            'book' => Book::find($this->book_id),
        ])
            ->layoutData([
                'title' => $this->title,
            ]);
    }
    public function mount($id)
    {
        $this->book_id = $id;
    }
}

<?php

namespace App\Http\Livewire\Pages;

use App\Models\News;
use Illuminate\Support\Carbon;
use Livewire\Component;

class NewDetailPage extends Component
{
    public $new_id;
    public function render()
    {
        Carbon::setLocale('ar');
        $new = News::find($this->new_id);
        $other_news = News::where("id", "!=", $this->new_id)->inRandomOrder()->limit(3)->get();

        return view(
            'livewire.pages.new-detail-page',
            [
                "new" => $new,
                'other_news' => $other_news,
            ]
        )
            ->layoutData(
                [
                    'title' => $new->title,
                    'hasMeta' => true
                ]
            );
    }
    public function mount($id)
    {
        $this->new_id = $id;
    }
    public function getPageUrlProperty()
    {
        return url('new/' . $this->new_id);
    }
    public function format($date)
    {
        return Carbon::parse($date)->translatedFormat('l j F Y g:i a');
    }
}

<?php

namespace App\Http\Livewire\Pages;

use App\Models\News;
use Illuminate\Support\Carbon;
use Livewire\Component;
use  Livewire\WithPagination;

class NewsPage extends Component
{
    use WithPagination;
    protected $paginationTheme = 'bootstrap';
    public $title = "";
    public function render()
    {
        Carbon::setLocale('ar');
        $this->title =__('frontend/news.title');
        $news = News::where("is_active", true)->orderBy("created_at", "desc")->paginate(9);
        return view('livewire.pages.news-page', [
            "news" => $news
        ])
            ->layoutData(
                [
                    'title' => $this->title,
                ]
            );
    }
    public function format($date)
    {
        return Carbon::parse($date)->translatedFormat('l j F Y g:i a');
    }
}

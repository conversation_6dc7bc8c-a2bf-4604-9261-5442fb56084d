<?php

namespace App\Http\Livewire\Pages;

use App\Facades\Panel;
use Illuminate\Http\Request;
use Livewire\Component;
use App\Models\Page as CmsPage;

class Page extends Component
{
    public $page_id;
    public $title;
    public function mount($id = null)
    {
        $this->page_id = $id;
    }
    public function render()
    {
        $platform = Panel::currentPanel();

        $page = $this->page_id == null ?
            CmsPage::orderBy('id', 'desc')->first()
            : CmsPage::find($this->page_id);
        $this->title = $page->name ?? "الصفحة غير موجودة";

        $view = 'livewire.pages.page';
        if ($platform == "religions") {
            $view = 'livewire.pages.religion-page';
        }
        return view($view, ["page" => $page])
            ->layout($platform == "religions" ? "layouts.religion-app" : 'layouts.master')
            ->layoutData(['title' => $this->title, 'hasMeta' => true]);
    }
}

<?php

namespace App\Http\Livewire\Pages;

use App\Facades\Panel;
use App\Models\Contact;
use Livewire\Component;
use App\Models\MailListSubscription;

class ReligionContactPage extends Component
{
    public $name;
    public $email;
    public $message;

    public $title = "تواصل معنا";
    public bool $isSent = false;

    public function render()
    {

        return view("livewire.pages.religion-contact-page")
            ->layout("layouts.religion-app")
            ->layoutData([
                'title' => $this->title,
            ]);
    }
    protected $rules = [
        'name' => 'required|min:6',
        'email' => 'required|email',
        // country
        'message' => 'required',
        // city

    ];

    public function submit()
    {
        $this->validate(null, [
            "name.required" => ":attribute مطلوب",
            "email.required" => ":attribute مطلوب",
            "message.required" => ":attribute مطلوب",
            "email.email" => ":attribute يجب ان يكون بريد الكتروني صالح",
            "name.min" => ":attribute يجب ان يكون على الاقل :min حروف",
        ], [
            "name" => "الاسم كامل",
            "email" => "البريدك الالكتروني",
            "message" => "الرسالة",
        ],);


        $result = Contact::create([
            'name' => $this->name,
            'email' => $this->email,
            'message' => $this->message,
            // 'city' => $this->city,
        ]);
        if ($result != null) {
            $this->isSent = true;
        }
    }
}

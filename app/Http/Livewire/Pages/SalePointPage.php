<?php

namespace App\Http\Livewire\Pages;

use App\Http\Controllers\Admin\Cities;
use App\Http\Controllers\Admin\SalePoints;
use App\Models\City;
use App\Models\Country;
use App\Models\SalePoint;
use Illuminate\Support\Facades\DB;
use Livewire\Component;

class SalePointPage extends Component
{
    public $title = 'نقاط البيع';
    public $city_id;
    public $country_id;
    public $sale_points;
    public $cities;
    public $countries;

    public function mount()
    {
        $this->city_id = 0;
        $this->country_id = 0;
        $this->sale_points = null;
        $this->countries = cache()->remember('countries', 10 * 60, function () {
            return DB::table('countries')->select("id", "name")->get();
        });

        $this->sale_points = SalePoint::all();
    }
    public function updated($propertyName)
    {
        if ($propertyName == "country_id") {
            $this->city_id = 0;
            error_log($this->city_id);
        }
    }
    public function render()
    {
        $this->countries = cache()->remember('countries', 120, function () {
            return DB::table('countries')->select("id", "name")->get();
        });
        $this->cities = City::where(
            "country_id",
            $this->country_id,
        )->get();
        return view(
            'livewire.pages.sale-point-page',
            //["sale_points" => $this->sale_points]
        )
            ->layoutData(['title' => $this->title]);
    }
    public function filter()
    {
        // $this->validate(
        //     [
        //         // "country_id" => "not_in:0",
        //         // "city_id" => "not_in:0",
        //     ],
        //     [
        //         "*.not_in" => "يرجى تحديد :attribute أولاً",
        //     ],
        //     [
        //         "country_id" => "الدولة",
        //         "city_id" => "المدينة",
        //     ],
        // );
        $this->sale_points = SalePoint::when(
            $this->country_id,
            fn ($query) => $query->where("country_id", $this->country_id)
        )->when(
            $this->city_id,
            fn ($query) => $query->where("city_id", $this->city_id)
        )->get();
    }
}

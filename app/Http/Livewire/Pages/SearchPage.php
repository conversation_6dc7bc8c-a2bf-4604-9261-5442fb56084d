<?php

namespace App\Http\Livewire\Pages;

use App\Facades\Panel;
use App\Http\Controllers\Admin\Contents;
use App\Http\Livewire\Traits\WithSearch;
use App\Models\Book;
use App\Models\Content;
use App\Models\TitleService;
use App\Models\VideoContent;
use Illuminate\Support\Facades\Cache;
use Livewire\Component;
use ProtoneMedia\LaravelCrossEloquentSearch\Search;

class SearchPage extends Component
{
    use WithSearch;
    public $readyToSearch = false;
    public $from;
    public $to;


    public function render()
    {
        $platform = Panel::currentPanel();

        if ($this->is_advanced_search) {
            $this->emit('advancedSearchChanged');
        }

        $view = "livewire.pages.search-page";
        if ($platform == "religions") {
            $view = "livewire.pages.religion-search-page";
        }
        return view(
            $view,
            [
                "result" =>  $this->readyToSearch == false ? null : $this->getSearchResult()
            ]
        )
            ->layout($platform == "religions" ? "layouts.religion-app" : 'layouts.master')
            ->layoutData(
                [
                    'title' => "نتائج البحث عن $this->search",
                ]
            );
    }
    public function mount()
    {

        $this->search = request("k") ?? "";
        $this->is_advanced_search = request("is_advanced") ?? 0;
        $this->services = request("services") ?? [];
        $this->book = request("book") ?? null;
        $this->type = request("type") ?? 0;
        $this->from = cache()->get('from');
        $this->to = cache()->get('to');
    }

    public function simpleSearch()
    {
        $this->readyToSearch = true;
    }

    public function toggleAdvancedSearch()
    {
        $this->readyToSearch = false;
        $this->is_advanced_search = !$this->is_advanced_search;
    }
    public function getBooksProperty()
    {
        return Cache::remember(\App\Facades\Panel::currentPanel() . ".published_books", 60, function () {
            return Book::
                // where("type", ">=", $this->from)->where('type', '<=', $this->to)

                when(\App\Facades\Panel::currentPanel() == "kholasah", fn($qr) => $qr->where("type", 0))->where("published", true)->get();
        });
    }
    public function getBookSinceServicesProperty()
    {
        $services = TitleService::whereHas('title.book', function ($query) {
            $query->where('id', $this->book)->where('published', true);
        })
            ->where('service_type', 1)
            ->distinct('name')->pluck('name',);

        return $services;
    }


    public function getBookEducationServicesProperty()
    {
        $services = TitleService::whereHas('title.book', function ($query) {
            $query->where('id', $this->book)->where('published', true);
        })
            ->where('service_type', 2)
            ->distinct('name')->pluck('name',);

        return $services;
    }


    protected $queryString = [
        'search' => [
            "as" => "k",
            'except' => ''
        ],
        'book' => [
            "as" => "book",
            'except' => 0

        ],
        'services' => [
            'as' => 'services',
            'except' => [1, 2]
        ],
        'is_advanced_search' => [
            'as' => 'is_advanced',
            'except' => false
        ]

    ];
}

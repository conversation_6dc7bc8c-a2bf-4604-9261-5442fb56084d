<?php

namespace App\Http\Livewire\Pages;

use App\Models\Service;
use App\Models\ServiceOrder;
use Illuminate\Support\Facades\Mail;
use Livewire\Component;

class ServicePage extends Component
{
    public $title;
    public $service;
    public $order_sent;
    public ServiceOrder $order;
    protected $rules = [
        'order.name' => 'required|min:6',
        'order.email' => 'required|email',
        'order.phone_number' => 'required|integer',
        'order.city' => 'required',
        'order.country' => 'required',
        'order.organization_name' => 'required',
        'order.student_count' => 'required|integer|gt:0',
        'order.detail' => 'required',
    ];
    public function mount($id)
    {
        $this->service = Service::find($id);
        $this->order = new ServiceOrder();
        $this->title = $this->service->name;
        $this->order_sent = false;
    }

    public function render()
    {
        return view('livewire.pages.service-page')
            ->layoutData(['title' => $this->title]);
    }
    public function saveOrder()
    {
        $this->validate(null, [
            "order.*.required" => ":attribute مطلوب",
            "order.*.email" => ":attribute يجب ان يكون بريد الكتروني صالح",
            "order.*.min" => ":attribute يجب ان يكون على الاقل :min حروف",
            "order.*.integer" => ":attribute يجب ان يكون رقم صحيح",
            "order.*.gt" => ":attribute يجب ان يكون اكبر من :value",
        ], [
            "order.name" => "الاسم كامل",
            "order.email" => "البريد الإلكتروني",
            "order.phone_number" => "رقم الجوال",
            "order.country" => "الدولة",
            "order.city" => "المدينة",
            "order.organization_name" => "اسم الجهة",
            "order.student_count" => "عدد الطلاب",
            "order.detail" => "تفاصيل الخدمة",
        ],);
        $this->order->service_id = $this->service->id;
        $result = $this->order->save();
        if ($result) {
            $this->order_sent = true;
            Mail::to('<EMAIL>')->send(new \App\Mail\OrderServiceMail($this->order));
        }
    }
}

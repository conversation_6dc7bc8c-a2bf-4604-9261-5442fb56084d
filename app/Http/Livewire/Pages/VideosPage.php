<?php

namespace App\Http\Livewire\Pages;

use App\Models\Video;
use Illuminate\Support\Facades\DB;
use Livewire\Component;
use Livewire\WithPagination;

class VideosPage extends Component
{
    use WithPagination;
    protected $paginationTheme = 'bootstrap';

    public $title = 'الفيديوهات';
    public function render()
    {
        $this->emit('rendered');
        $page = request()->get('page', 1);
        $this->title = __('frontend/videos.title');
        return view('livewire.pages.videos-page', [
            "videos" => Video::where(["is_active" => 1])->orderBy("created_at", "desc")->paginate(9)
        ])
            ->layoutData(['title' => $this->title]);
    }
}

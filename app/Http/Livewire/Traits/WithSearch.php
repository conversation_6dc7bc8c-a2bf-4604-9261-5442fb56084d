<?php

namespace App\Http\Livewire\Traits;

use App\Facades\Panel;
use App\Models\Activity;
use App\Models\Calender;
use App\Models\Content;

use App\Models\Tadabor;
use App\Models\TitleService;
use App\Models\TitleServiceActivity;
use App\Models\TitleServiceCalender;
use App\Models\TitleServiceQuotation;
use App\Models\TitleServiceReference;
use App\Models\TitleServiceText;
use Illuminate\Support\Facades\DB;
use ProtoneMedia\LaravelCrossEloquentSearch\Search;

/**
 *  Search in kholasah contents
 */
trait WithSearch
{
    public bool $is_advanced_search = false;
    public $search;
    public $book = null;
    public $type = 0;
    public $services = [];

    public function getSearchResult($page = 1)
    {

        $tafseers = ["ibn_katheer", "baghawy", "saady", "qortoby", "tabary", "tanweer", "zad_almaseer", "ibn_alqayyem", 'g<PERSON><PERSON>_ibn_qutaybah', 'aljadwal', 'ira<PERSON>_aldarweesh', 'iraab_alnahas', 'nathm_aldurar', 'qiraat_almawsoah', 'siraaj_ghareeb', 'tahbeer_altayseer', 'tadabor'];

        $tafseers_name = [
            'ibn_katheer' => "إبن كثير",
            'baghawy' => "البغوي",
            'saady' => "السعدي",
            'qortoby' => "القرطبي",
            'tabary' => "الطبري",
            'tanweer' => "ابن عاشور",
            'zad_almaseer' => "ابن الجوزي",
            'ibn_alqayyem' => "ابن القيم",
            'ghareeb_ibn_qutaybah' => "غريب القرآن لابن قتيبة",
            'aljadwal' => "إعراب القرآن وبيانه",
            'iraab_aldarweesh' => "إعراب القرآن وبيانه",
            'iraab_alnahas' => "إعراب القران للنحاس",
            'nathm_aldurar' => "البقاعي",
            'qiraat_almawsoah' => "الموسوعة القرآنية",
            'siraaj_ghareeb' => "السراج في بيان غريب الكلمات",
            'tahbeer_altayseer' => "تحبير التيسير",
            'tadabor' => "تدبر"
        ];

        $search = get_searchable_text($this->search);
        if (!empty(trim($search))) {
            $arr = [];
            $searchQuery = Search::new();
            $searchQuery
                ->addFullText(
                    // ->addWhen((!$this->is_advanced_search) || in_array('المحتوى', $this->services), 
                    Content::whereHas("title.book", function ($query) {
                        $query->where('published',  true);
                        if ((!is_null($this->book)) && $this->book != 0) {
                            $query->where('id',  $this->book);
                        }
                    })->with(["title:id,name,book_id", "title.book"]),
                    'searchable_text',
                   orderByColumn: "title_id"
                )

                // ->addFullText(
                //     TitleServiceText::with([
                //         "service.title:id,name,book_id as title",
                //         "service.title.book:id,name",
                //         // 'title'
                //     ]),
                //     'searchable_text',
                //   //  orderByColumn: 'title.id'
                // )

                // ->addFullText(
                //     TitleServiceQuotation::with([
                //         "service.title:id,name,book_id as title",
                //         "service.title.book:id,name",
                //         // 'title'
                //     ]),
                //     'searchable_text',
                //   //  orderByColumn: 'title.id'
                // )

                // ->addFullText(
                //     TitleServiceReference::with([
                //         "service.title:id,name,book_id as title",
                //         "service.title.book:id,name",
                //         // 'title'
                //     ]),
                //     'searchable_text',
                //   // orderByColumn: 'title.id'
                // )
                // ->addFullText(
                //     TitleServiceActivity::with([
                //         "service.title:id,name,book_id as title",
                //         "service.title.book:id,name",
                //         // "title"
                //     ]),
                //     'searchable_text',
                //  //   orderByColumn: 'title.id'
                // )
                // ->addFullText(
                //     TitleServiceCalender::with([
                //         "service.title:id,name,book_id as title",
                //         "service.title.book:id,name",
                //         // "title"
                //     ]),
                //     'searchable_text',
                //  //   orderByColumn: 'title.id'
                // );
            ->addFullText(
                // ->addWhen((!$this->is_advanced_search) || (count($this->services) > 1 || (!in_array('المحتوى', $this->services))),
                TitleService::whereHas("title.book", function ($query) {
                    $query->where('published',  true);
                    if (!is_null($this->book) && $this->book != 0) {
                        $query->where('id',  $this->book);
                    }
                })
                    // ->whereIn('name', $this->services)
                    ->with([
                        "title:id,name,book_id",
                        "title.book:id,name",
                        "text",
                        "texts",
                        "qoutations",
                        "refrences",
                        "videos",
                        "activities",
                        "calenders"

                    ]),
                [
                    // "videos" => ["searchable_text"],
                    "text" => ["searchable_text"],
                    "texts" => ["searchable_text"],
                    "qoutations" => ["searchable_text"],
                    "refrences" => ["searchable_text"],
                    "activities" => ["searchable_text"],
                    "calenders" => ["searchable_text"],
                ],
                orderByColumn: "title_id"
            );

            //add tafsser and tadbor
            if (Panel::currentPanel() == "kholasah") {
                foreach ($tafseers as $tafseer) {


                    if ($tafseer == "tadabor") {

                        $searchQuery
                            ->addFullText(
                                // ->addWhen(
                                // !$this->is_advanced_search,
                                Tadabor::query()->with('verses.verse'),
                                "searchable_text",
                                orderByColumn: 'id'
                            );
                    } else {
                        $searchQuery
                            ->addFullText(
                                // ->addWhen(
                                // !$this->is_advanced_search,
                                (createDynamicModel($tafseer, $tafseer, $tafseers_name[$tafseer]))::query()->with('quran'),
                                "searchable_text",
                                orderByColumn: 'id'
                            );
                    }
                }
            }
            


            $searchQuery->beginWithWildcard()
                ->endWithWildcard()
                ->paginate(100, "page", $page);

            $searchQuery = $searchQuery->dontParseTerm()->search('"'.$search.'"');

            $arr["count"] = $searchQuery->count();
            $arr["total"] = $searchQuery->total();
            $arr["perPage"] = $searchQuery->perPage();
            $arr["lastPage"] = $searchQuery->lastPage();
            $arr["currentPage"] = $searchQuery->currentPage();
            $arr["hasMorePages"] = $searchQuery->hasMorePages();

            $arr["items"] = $searchQuery->groupBy('title.book.name', true)->toArray();

            return $arr;
        }
        return null;
    }
}

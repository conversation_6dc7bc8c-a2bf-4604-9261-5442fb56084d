<?php

namespace App\Http\Middleware;

use App\Facades\Panel;
use Closure;
use Illuminate\Http\Request;

class ApplyPanel
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next,String $panel)
    {
        Panel::setPanel($panel);
        return $next($request);
    }
}

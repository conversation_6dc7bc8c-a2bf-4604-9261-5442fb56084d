<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class BooksType
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next, $from, $to)
    {
        cache()->put('from', $from);
        cache()->put('to', $to);
        $p_type = 0;
        if ($from == 1) {
            $p_type = 1;
        } else if ($from == 6) {
            $p_type = 2;
        }
        cache()->put('platform_type', $p_type);
        return $next($request);
    }
}

<?php



namespace App\Http\Responses;

use App\Facades\Panel;
use Filament\Http\Responses\Auth\Contracts\LogoutResponse as Responsable;

use Illuminate\Http\RedirectResponse;
use Filament\Http\Responses\Auth\LoginResponse as BaseLoginResponse;



class LogoutResponse extends BaseLoginResponse

{

    public function toResponse($request): RedirectResponse

    {
        return redirect()->to(

            config('panels.panels.'.Panel::currentPanel().'.auth.pages.login') ? route(Panel::currentPanel().'.auth.login') : config('panels.'.Panel::currentPanel().'.path'),

        );

    }

}

<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class OrderServiceMail extends Mailable
{
    use Queueable, SerializesModels;

    public $serviceOrder;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($serviceOrder)
    {
        $this->serviceOrder = $serviceOrder;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->subject("طلب خدمة جديد")
            ->view('emails.service-order');
    }
}

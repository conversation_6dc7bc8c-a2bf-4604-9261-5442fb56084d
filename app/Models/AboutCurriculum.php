<?php

namespace App\Models;

use App\Traits\HasSEO;
use Illuminate\Database\Eloquent\Factories\HasFactory;


class AboutCurriculum extends Model
{
    use HasFactory;
    use HasSEO;
    protected $table = 'about_curriculum';
    // fillable
    protected $fillable = [
        "id",
        'title',
        'description',
        'image',
        'file',
        "methods",
        'main_goal',
        'goals',
        'foundations',
        'curriculum',
    ];
    // cast
    protected $casts = [
        'methods' => 'array',
        'foundations' => 'array',
        'curriculum' => 'array',
    ];

    public function getConnectionName()
    {
        return config("panel.panels.kholasah.database_connection");
    } 
}

<?php

namespace App\Models;

use App\Traits\HasSEO;
use Illuminate\Database\Eloquent\Factories\HasFactory;


class AboutKholasa extends Model
{
    use HasFactory;
    use HasSEO;

    protected $table = 'about_kholasa';
    // fillable
    protected $fillable = [
        "id",
        'title',
        'description',
        'image',
        'file',
        "text",
        'methods',
        'goals_achievement',
        'targets',
        'outcomes',
        'comments',
    ];
    // cast
    protected $casts = [
        'methods' => 'array',
        'targets' => 'array',
        'comments' => 'array',
    ];

    public function getConnectionName()
    {
        
        return config("panel.panels.kholasah.database_connection");
    }
}

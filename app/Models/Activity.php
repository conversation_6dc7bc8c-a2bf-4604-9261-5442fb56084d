<?php

namespace App\Models;




class Activity extends Model
{

    protected $table    = 'activities';
    protected $fillable = [
        'id',
        'admin_id',
        'book_id',

        'title_id',

        'active',

        'style',
        'imp_type',
        'type',
        'goal',
        'needed',
        'answer',
        'created_at',
        'updated_at',
    ];

    /**
     * book_id relation method
     * @param void
     * @return object data
     */
    public function book()
    {
        return $this->belongsTo(\App\Models\Book::class);
    }

    /**
     * title_id relation method
     * @param void
     * @return object data
     */
    public function title()
    {
        return $this->belongsTo(\App\Models\Title::class);
    }

    /**
     * Static Boot method to delete or update or sort Data
     * @param void
     * @return void
     */
    protected static function boot()
    {
        parent::boot();
        // if you disable constraints should by run this static method to Delete children data
        static::deleting(function ($activity) {
            //$activity->book_id()->delete();
            //$activity->book_id()->delete();
        });
    }
    public function getModelTypeAttribute()
    {
        return "Activity";
    }
    protected $appends = ['model_type'];
}

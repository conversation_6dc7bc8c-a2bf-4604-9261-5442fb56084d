<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

/**
 * @property int id
 * @property string name
 * @property string email
 * @property string|null photo_profile
 * @property string password
 * @property int group_id
 * @property string|null remember_token
 * @property Carbon|null created_at
 * @property Carbon|null updated_at
 *
 * //Dynamic Properties
 * @property AdminGroup|null group
 */
class Admin extends Authenticatable
{
    use Notifiable;

    protected $table   = 'admins';
    protected $guarded = ['*'];

    protected $hidden = ['password'];

    public function group(): HasOne
    {
        return $this->hasOne(AdminGroup::class);
    }

    public function role($name): bool
    {
        $name = explode('_', $name);
        if (!$this->group_id || !$this->group || count($name) != 2) {
            return false;
        }
        return $this->group->role->where('name', $name[0])->where($name[1], 'yes')->count() >= 1;
    }

    /**
     * check multi roles
     * @param string ...$roles
     *
     * @return bool
     */
    public function hasRole(...$roles): bool
    {
        foreach ($roles as $role) {
            if ($this->role($role)) {
                return true;
            }
        }
        return false;
    }
}

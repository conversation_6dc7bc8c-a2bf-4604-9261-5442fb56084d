<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;

use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

// Auto Models By Baboon Script
// Baboon Maker has been Created And Developed By  [It V 1.5.0 | https://it.phpanonymous.com]
// Copyright Reserved  [It V 1.5.0 | https://it.phpanonymous.com]

/**
 * @property int id
 * @property int|null admin_id
 * @property string group_name
 * @property Carbon|null created_at
 * @property Carbon|null updated_at
 *
 * //Dynamic Properties
 * @property Admin|null admin
 * @property AdminGroupRole[]|Collection|null role
 */
class AdminGroup extends Model
{

    protected $table   = 'admin_groups';
    protected $guarded = [];

    public function admin(): HasOne
    {
        return $this->hasOne(Admin::class);
    }

    public function role(): HasMany
    {
        return $this->hasMany(AdminGroupRole::class, 'admin_groups_id');
    }

}

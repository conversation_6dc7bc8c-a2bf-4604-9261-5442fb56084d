<?php

namespace App\Models;

use App\Traits\HasSEO;
use Illuminate\Database\Eloquent\Model;

class Album extends Model
{
    use HasSEO;

    protected $table    = 'albums';
    protected $fillable = [
        'id',
        'admin_id',
        'title',
        'is_active',
        "images",
        'description',
    ];

    /**
     * admin id relation method to get how add this data
     * @type hasOne
     * @param void
     * @return object data
     */
    public function admin_id()
    {
        return $this->hasOne(\App\Models\Admin::class, 'id', 'admin_id');
    }
    public function getImageAttribute()
    {
        return Files::where(["type_file" => "albums", "type_id" => $this->id])->first();
    }

    /**
     * Static Boot method to delete or update or sort Data
     * @param void
     * @return void
     */
    protected static function boot()
    {
        parent::boot();
        // if you disable constraints should by run this static method to Delete children data
        static::deleting(function ($album) {
        });
    }

    // cast
    protected $casts = [
        "images" => "array",
    ];
}

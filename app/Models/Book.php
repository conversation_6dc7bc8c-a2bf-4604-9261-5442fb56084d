<?php

namespace App\Models;

use App\Traits\HasSEO;

class Book extends Model
{
    use HasSEO;

    protected $table = 'books';


    protected $fillable = [
        '   ',
        'admin_id',
        'name',
        'kholasa_name',
        'overview',
        'methology',
        'type',
        'published',
        'sort',
        'created_at',
        'updated_at',
        "files",
        "cover",
    ];

    /**
     * admin id relation method to get how add this data
     * @type hasOne
     * @param void
     * @return object data
     */
    public function admin_id()
    {
        return $this->hasOne(\App\Models\Admin::class, 'id', 'admin_id');
    }

    // titles
    public function titles()
    {
        return $this->hasMany(\App\Models\Title::class, 'book_id', 'id');
    }

    /**
     * Static Boot method to delete or update or sort Data
     * @param void
     * @return void
     */
    protected static function boot()
    {
        parent::boot();
        // if you disable constraints should by run this static method to Delete children data
        static::deleting(function ($book) {
        });
    }

    // cast
    protected $casts = [
        "files" => "array",
    ];
}

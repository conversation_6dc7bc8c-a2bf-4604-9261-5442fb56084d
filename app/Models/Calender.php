<?php

namespace App\Models;




class Calender extends Model
{

    protected $table    = 'calenders';
    protected $fillable = [
        'id',
        'admin_id',
        'book_id',
        'title_id',
        'active',
        'style',
        'duration',
        'imp_style',
        'goal',
        'tools_questions',
        'feedback_answers',
        'created_at',
        'updated_at',
    ];

    /**
     * book_id relation method
     * @param void
     * @return object data
     */
    public function book()
    {
        return $this->belongsTo(\App\Models\Book::class);
    }

    /**
     * title relation method
     * @param void
     * @return object data
     */
    public function title()
    {
        return $this->belongsTo(\App\Models\Title::class);
    }

    /**
     * Static Boot method to delete or update or sort Data
     * @param void
     * @return void
     */
    protected static function boot()
    {
        parent::boot();
        // if you disable constraints should by run this static method to Delete children data
        static::deleting(function ($calender) {
            //$calender->book()->delete();
            //$calender->book()->delete();
        });
    }
    public function getModelTypeAttribute()
    {
        return "Calender";
    }
    protected $appends = ['model_type'];
}

<?php

namespace App\Models;

use App\Facades\Panel;
use Illuminate\Database\Eloquent\Model;

class Contact extends Model
{

   protected $table    = 'contacts';
   protected $fillable = [
      'id',
      'admin_id',
      'name',
      'email',
      'message',
      'created_at',
      'updated_at',
   ];

   /**
    * Static Boot method to delete or update or sort Data
    * @param void
    * @return void
    */
   protected static function boot()
   {
      parent::boot();
      // if you disable constraints should by run this static method to Delete children data
      static::deleting(function ($contact) {});
   }


   public function getConnectionName()
   {
      if (Panel::currentPanel() == "religions") {
         return config('panels.panels.' . Panel::currentPanel() . '.database_connection');
      }
      return parent::getConnectionName();
   }
}

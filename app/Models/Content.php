<?php

namespace App\Models;

use App\Models\Traits\GenerateSearch;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;


class Content extends Model
{

    use GenerateSearch;
    use SoftDeletes;

    protected $table    = 'contents';
    protected $fillable = [
        'id',
        'admin_id',
        'book_id',
        'title_id',
        'text',
        'created_at',
        'updated_at',
        'deleted_at',
        "searchable_text"
    ];

    /**
     * admin id relation method to get how add this data
     * @type hasOne
     * @param void
     * @return object data
     */
    public function admin_id()
    {
        return $this->hasOne(\App\Models\Admin::class, 'id', 'admin_id');
    }




    /**
     * title_id relation method
     * @param void
     * @return object data
     */
    public function title()
    {
        return $this->belongsTo(\App\Models\Title::class, 'title_id');
    }



    /**
     * content_id relation method
     * @param void
     * @return object data
     */
    public function ratings()
    {
        return $this->hasMany(\App\Models\ContentRate::class);
    }


    /**
     * Static Boot method to delete or update or sort Data
     * @param void
     * @return void
     */
    protected static function boot()
    {
        parent::boot();
        // if you disable constraints should by run this static method to Delete children data
        static::deleting(function ($content) {
            //$content->book_id()->delete();
            //$content->book_id()->delete();
        });
    }

    public function getModelTypeAttribute()
    {
        return "Content";
    }

    public function comments(): MorphMany
    {
        return $this->morphMany(ContentComment::class, 'commentable');
    }

    public function rates(): MorphMany
    {
        return $this->morphMany(ContentRate::class, 'ratable');
    }

    protected $appends = ['model_type'];
}

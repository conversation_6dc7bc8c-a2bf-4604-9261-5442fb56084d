<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class ContentComment extends Model
{
    use HasFactory;

    protected $fillable = [
        "comment"
    ];
    // public function content()
    // {
    //     return $this->belongsTo(Content::class);
    // }

    public function commentable(): MorphTo
    {
        return $this->morphTo(__FUNCTION__, 'commentable_type', 'commentable_id');
    }
}

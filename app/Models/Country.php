<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;



class Country extends Model
{

    protected $table    = 'countries';
    protected $fillable = [
        'id',
        'admin_id',
        'name',
        'created_at',
        'updated_at',
    ];

    /**
     * admin id relation method to get how add this data
     * @type hasOne
     * @param void
     * @return object data
     */
    public function admin_id()
    {
        return $this->hasOne(\App\Models\Admin::class, 'id', 'admin_id');
    }

    // cities
    public function cities()
    {
        return $this->hasMany(\App\Models\City::class, 'country_id', 'id');
    }

    /**
     * Static Boot method to delete or update or sort Data
     * @param void
     * @return void
     */
    protected static function boot()
    {
        parent::boot();
        // if you disable constraints should by run this static method to Delete children data
        static::deleting(function ($country) {
        });
    }
}

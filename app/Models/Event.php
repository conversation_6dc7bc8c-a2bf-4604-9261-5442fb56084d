<?php

namespace App\Models;

use App\Traits\HasSEO;
use Illuminate\Database\Eloquent\Model;

class Event extends Model
{

   use HasSEO;
   protected $table    = 'events';
   protected $fillable = [
      'id',
      'admin_id',
      'title',
      'is_active',
      'overview',
      'image',
      'detail',
      'created_at',
      'updated_at',
   ];

   /**
    * admin id relation method to get how add this data
    * @type hasOne
    * @param void
    * @return object data
    */
   public function admin_id()
   {
      return $this->hasOne(\App\Models\Admin::class, 'id', 'admin_id');
   }


   /**
    * Static Boot method to delete or update or sort Data
    * @param void
    * @return void
    */
   protected static function boot()
   {
      parent::boot();
      // if you disable constraints should by run this static method to Delete children data
      static::deleting(function ($event) {
      });
   }
}

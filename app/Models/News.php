<?php

namespace App\Models;

use App\Traits\HasSEO;
use Illuminate\Database\Eloquent\Casts\Attribute;

use Illuminate\Database\Eloquent\Model;


class News extends Model
{
    use HasSEO;

    protected $table    = 'news';
    protected $fillable = [
        'id',
        'admin_id',
        'title',
        'overview',
        'detail',
        'is_active',
        'image',
        'created_at',
        'updated_at',
    ];
    // // cast
    // protected $casts = [
    //     'is_active' => 'string',
    // ];
    // setter
    protected function isActive(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => intval($value),
            set: fn ($value) => strval($value),
        );
    }

    /**
     * admin id relation method to get how add this data
     * @type hasOne
     * @param void
     * @return object data
     */
    public function admin_id()
    {
        return $this->hasOne(\App\Models\Admin::class, 'id', 'admin_id');
    }


    /**
     * Static Boot method to delete or update or sort Data
     * @param void
     * @return void
     */
    protected static function boot()
    {
        parent::boot();
        // if you disable constraints should by run this static method to Delete children data
        static::deleting(function ($news) {
        });
    }
}

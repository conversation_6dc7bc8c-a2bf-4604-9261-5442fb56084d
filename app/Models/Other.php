<?php

namespace App\Models;




class Other extends Model
{

    protected $table    = 'others';
    protected $fillable = [
        'id',
        'admin_id',
        'book_id',
        'title_id',
        'content',
        'created_at',
        'updated_at',
    ];

    /**
     * book_id relation method
     * @param void
     * @return object data
     */
    public function book()
    {
        return $this->belongsTo(\App\Models\Book::class);
    }

    /**
     * title_id relation method
     * @param void
     * @return object data
     */
    public function title()
    {
        return $this->belongsTo(\App\Models\Title::class);
    }

    /**
     * Static Boot method to delete or update or sort Data
     * @param void
     * @return void
     */
    protected static function boot()
    {
        parent::boot();
        // if you disable constraints should by run this static method to Delete children data
        static::deleting(function ($other) {
            //$other->book_id()->delete();
            //$other->book_id()->delete();
        });
    }
    public function getModelTypeAttribute()
    {
        return "Other";
    }
    protected $appends = ['model_type'];
}

<?php

namespace App\Models;

use App\Enums\ParsingStatus;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * @property int id
 * @property string name
 * @property int admin_id
 * @property int book_id
 * @property boolean|int indexes_extracted
 * @property boolean|int indexes_processed
 * @property int|ParsingStatus status
 * @property Carbon|null created_at
 * @property Carbon|null updated_at
 *
 * //Dynamic properties
 * @property string newName
 * @property string path
 * @property string fullPath
 * @property string indexesFilePath
 *
 * //Dynamic relations
 * @property Admin admin
 * @property Title[] indexes
 * @property TitlesHref[]|Collection hrefs
 * @property ParsingFileError[]|Collection errors
 */
class ParsingFile extends Model
{
    protected $table   = 'parsing_files';
    protected $guarded = [];

    // cast
    protected $casts = [
        'images' => 'array',
    ];

    public function admin(): HasOne
    {
        return $this->hasOne(Admin::class);
    }
    public function book(): BelongsTo
    {
        return $this->belongsTo(Book::class);
    }

    public function indexes(): HasManyThrough
    {
        return $this->hasManyThrough(
            Title::class,
            TitlesHref::class,
            'parsing_file_id',
            'id',
            'id',
            'title_id'
        );
    }


    public function hrefs(): HasMany
    {
        return $this->hasMany(TitlesHref::class, 'parsing_file_id');
    }

    public function errors(): HasMany
    {
        return $this->hasMany(ParsingFileError::class, 'file_id');
    }

    public function getPathAttribute(): string
    {
        return storage_path("parsing");
    }

    public function getNewNameAttribute(): string
    {
        return $this->file_name;
    }

    public function getFullPathAttribute($value): string
    {
        return storage_path("parsing/{$value}{$this->newName}");
    }

    public function getIndexesFilePathAttribute(): string
    {
        return $this->getFullPathAttribute('indexes-');
    }

    public function getIndexesPath($isExtractedBefore = false): string
    {
        return $this->getFullPathAttribute($isExtractedBefore ? 'indexes-' : '');
    }

    /********** Queries  **********/
    public function setIsParsing(): bool
    {
        if ($this->status == ParsingStatus::PARSING) {
            return true;
        }
        return $this->update(['status' => ParsingStatus::PARSING]);
    }

    public function setIndexesExtracted(): bool
    {
        return $this->update(['indexes_extracted' => true]);
    }
}

<?php

namespace App\Models;

use App\Enums\ParsingStatus;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;

use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;

/**
 * @property int id
 * @property int file_id
 * @property string error
 * @property string source
 * @property string|null other
 * @property Carbon|null created_at
 * @property Carbon|null updated_at
 *
 * @property ParsingFile file
 */
class ParsingFileError extends Model
{
    protected $table   = 'parsing_files_errors';
    protected $guarded = [];

    public function file(): HasOne
    {
        return $this->hasOne(ParsingFile::class, 'file_id');
    }

    public function book(): HasOneThrough
    {
        return $this->hasOneThrough(Book::class, ParsingFile::class, 'book_id', 'id', 'file_id', 'id');
    }
}

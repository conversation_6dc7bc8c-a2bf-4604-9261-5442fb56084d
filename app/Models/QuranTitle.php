<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;


class QuranTitle extends Model
{
    use HasFactory;
    public $timestamps = false;

    // protected $primaryKey = 'verse_id';

    protected $fillable = [
        'from_verse',
        'to_verse',
        'title_id',
        'sora_num',

    ];


    public  function Sura()
    {
        return $this->belongsTo(Quran::class, "verse_id");
    }

    public  function Verse()
    {
        return $this->belongsTo(Quran::class, "verse_id");
    }
}

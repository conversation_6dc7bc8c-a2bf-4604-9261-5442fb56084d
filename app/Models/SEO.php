<?php

namespace App\Models;

use App\Facades\Panel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class SEO extends Model
{
    use HasFactory;

    public $table = 'seo';

    protected $fillable = [
        "keywords", "description"
    ];
    protected $casts = [
        "keywords" => "array"
    ];

    public function model(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Static Boot method to delete or update or sort Data
     * @param void
     * @return void
     */
    protected static function boot()
    {
        parent::boot();
        // if you disable constraints should by run this static method to Delete children data
        static::deleting(function ($seo) {
            Cache::forget("seo." . Panel::currentPanel() . '.' . Str::afterLast($seo->model_type, '\\') . "." . $seo->model_id);
        });
        static::updated(function ($seo) {
            Cache::forget("seo." . Panel::currentPanel() . '.' . Str::afterLast($seo->model_type, '\\') . "." . $seo->model_id);
        });
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SalePoint extends Model
{

    protected $table    = 'sale_points';
    protected $fillable = [
        'id',
        'admin_id',
        'place',
        'phone_number',
        'address',
        'country_id',

        'city_id',

        'map_url',
        'created_at',
        'updated_at',
    ];

    /**
     * admin id relation method to get how add this data
     * @type hasOne
     * @param void
     * @return object data
     */
    public function admin_id()
    {
        return $this->hasOne(\App\Models\Admin::class, 'id', 'admin_id');
    }


    /**
     * country_id relation method
     * @param void
     * @return object data
     */
    public function country()
    {
        return $this->belongsTo(\App\Models\Country::class);
    }

    /**
     * city_id relation method
     * @param void
     * @return object data
     */
    public function city()
    {
        return $this->belongsTo(\App\Models\City::class);
    }

    /**
     * Static Boot method to delete or update or sort Data
     * @param void
     * @return void
     */
    protected static function boot()
    {
        parent::boot();
        // if you disable constraints should by run this static method to Delete children data
        static::deleting(function ($salepoint) {
            //$salepoint->country_id()->delete();
            //$salepoint->country_id()->delete();
        });
    }
}

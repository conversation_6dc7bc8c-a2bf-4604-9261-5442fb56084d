<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;


class ServiceOrder extends Model
{

    protected $table    = 'service_orders';
    protected $fillable = [
        'id',
        'admin_id',
        'name',
        'country',
        'city',
        'email',
        'phone_number',
        'organization_name',
        'student_count',
        'detail',
        'service_id',

        'created_at',
        'updated_at',
    ];

    /**
     * service_id relation method
     * @param void
     * @return object data
     */
    public function service()
    {
        return $this->belongsTo(\App\Models\Service::class);
    }

    /**
     * Static Boot method to delete or update or sort Data
     * @param void
     * @return void
     */
    protected static function boot()
    {
        parent::boot();
        // if you disable constraints should by run this static method to Delete children data
        static::deleting(function ($serviceorder) {
            //$serviceorder->service_id()->delete();
        });
    }
}

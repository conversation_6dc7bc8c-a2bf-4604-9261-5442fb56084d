<?php
namespace App\Models;
use Illuminate\Database\Eloquent\Model;

class SocialMedia extends Model {

protected $table    = 'social_medias';
protected $fillable = [
		'id',
		'admin_id',
        'icon',
        'active',

        'url',
		'created_at',
		'updated_at',
	];

 	/**
    * Static Boot method to delete or update or sort Data
    * @param void
    * @return void
    */
   protected static function boot() {
      parent::boot();
      // if you disable constraints should by run this static method to Delete children data
         static::deleting(function($socialmedia) {
         });
   }

}

<?php

namespace App\Models;



class <PERSON><PERSON><PERSON> extends Model
{

    public $table = "tadabor";

    public $timestamps = false;

    function verses()
    {
        return $this->hasMany(TadaborVerse::class, "tadabor_id");
    }

    public function getModelTypeAttribute()
    {
        return 'tadabor';
    }

    protected $appends = ['model_type', 'title'];

    public function gettitleAttribute()
    {
        return [
            'book' =>
            ['name' => 'تدبر']
        ];
    }
}

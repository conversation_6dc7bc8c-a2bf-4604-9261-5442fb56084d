<?php

namespace App\Models;

use App\Traits\HasSEO;
use Illuminate\Database\Eloquent\Builder;

use Illuminate\Support\Facades\Cache;
use SolutionForest\FilamentTree\Concern\ModelTree;

class Title extends Model
{
    use HasSEO;
    use ModelTree;

    protected $table    = 'titles';
    protected $fillable = ['id', 'title', 'parent_id', 'order', 'book_id'];
    protected $with = ['parent'];


    /**
     * admin id relation method to get how add this data
     * @type hasOne
     * @param void
     * @return object data
     */
    public function admin_id()
    {
        return $this->hasOne(\App\Models\Admin::class, 'id', 'admin_id');
    }

    public function getNextTitle()
    {
        $title = Title::where('order', '>', $this->order ?? 0)->where('parent_id', $this->parent_id)->where('book_id', $this->book_id)->withCount('subtitles')->orderBy('order')->first();
        if ($title) {
            // dump('id');
            // dump($this->id);
            if ($title?->subtitles_count > 0) {

                $title = Title::where('parent_id', $title->id)->where('book_id', $this->book_id)->withCount('subtitles')->orderBy('order')->first();
                // dump($title->id);
                return $title;
            }
            // dump('count 0');
            // dump($title->id);
            return $title;
        } else {
            // dump('parent');
            // dump($this->parent->id);
            $parent = $this->parent?->getNextTitle();
            // dump("sub_count");
            // dump($parent?->subtitles_count);
            if ($parent && ($parent?->subtitles_count ?? 0) > 0) {
                // dump("inside parent && (parent?->subtitles_count ?? 0) > 0");
                $title = Title::where('parent_id', $parent->id)->where('book_id', $this->book_id)->withCount('subtitles')->orderBy('order')->first();
                if ($title) {
                    return $title;
                }
            } else if ($parent) {
                return $parent;
            }
        }
        return null;
    }

    public function getPreviousTitle()
    {
        $title = Title::where('order', '<', $this->order ?? 0)->where('book_id', $this->book_id)->where('parent_id', $this->parent_id)->withCount('subtitles')->orderBy('order', 'desc')->first();
        if ($title) {
            return $title;
        } else {
            $parent = $this->parent?->getPreviousTitle();
            if ($parent && ($parent?->subtitles_count ?? 0) > 0) {
                $title = Title::where('parent_id', $parent->id)->where('book_id', $this->book_id)->orderBy('order', 'desc')->first();
                if ($title) {
                    return $title;
                }
            } else {
                return $parent;
            }
        }
        return null;
    }


    /**
     * book_id relation method
     * @param void
     * @return object data
     */
    public function book()
    {
        return $this->belongsTo(\App\Models\Book::class);
    }

    /**
     * parent_id relation method
     * @param void
     * @return object data
     */


    public function parent()
    {
        return $this->belongsTo(\App\Models\Title::class, 'parent_id')->withCount('subtitles');
    }
    public function subtitles()
    {
        return $this->hasMany(\App\Models\Title::class, 'parent_id')->with("subtitles", fn($query) => $query->orderBy('order'));
    }
    public function content()
    {
        return $this->hasOne(\App\Models\Content::class);
    }

    public function videoContents()
    {
        return $this->hasMany(\App\Models\VideoContent::class);
    }

    public function quotations()
    {
        return $this->hasMany(\App\Models\Quotation::class);
    }

    public function references()
    {
        return $this->hasMany(\App\Models\Reference::class);
    }

    public function goal()
    {
        return $this->hasOne(\App\Models\Goal::class);
    }

    public function activities()
    {
        return $this->hasMany(\App\Models\Activity::class);
    }

    public function calenders()
    {
        return $this->hasMany(\App\Models\Calender::class);
    }

    public function other()
    {
        return $this->hasOne(\App\Models\Other::class);
    }

    // services relation method
    public function services()
    {
        return $this->hasMany(\App\Models\TitleService::class, 'title_id', 'id');
    }


    // verses relation method
    public function verses()
    {
        return $this->with(\App\Models\QuranTitle::class, "title_id");
    }

    // Default if you need to override

    public function determineOrderColumnName(): string
    {
        return "order";
    }

    public function determineParentColumnName(): string
    {
        return "parent_id";
    }

    public function determineTitleColumnName(): string
    {
        return 'name';
    }

    public static function defaultParentKey()
    {
        return -1;
    }

    public static function defaultChildrenKeyName(): string
    {
        return "children";
    }

    public function scopeOrdered(Builder $query, string $direction = 'asc')
    {
        return $query->orderBy($this->determineOrderColumnName(), $direction)
            ->orderBy('id', 'asc');
    }


    /**
     * Static Boot method to delete or update or sort Data
     * @param void
     * @return void
     */
    protected static function boot()
    {
        parent::boot();
        // if you disable constraints should by run this static method to Delete children data
        static::deleting(function ($title) {
            Cache::forget("book." . $title->book_id . ".titles");
        });

        static::updated(function ($title) {
            Cache::forget("book." . $title->book_id . ".titles");
        });
    }

    public function getServicesStatistics()
    {
        $statistic  = "";
        foreach ($this->services as $service) {
            $statistic .= $service->name . ": " . $service->getServicesCount() . "|";
        }

        $statistic = str($statistic)->replaceLast("|", "");
        return $statistic;
    }
}

<?php

namespace App\Models;

use App\Traits\HasSEO;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class TitleService extends Model
{
    use HasSEO;
    use HasFactory;
    // fillable
    protected $fillable = [
        "id",
        'title_id',
        'view_type',
        'service_type',
        'service_sub_type_id',
        'name',
        'data',
        'sort',
    ];
    // cast
    protected $casts = [
        'data' => 'array',
    ];
    // relations
    public function title()
    {
        return $this->belongsTo(Title::class);
    }
    public function service_sub_type()
    {
        return $this->belongsTo(ServiceSubType::class);
    }

    public function getServicesCount()
    {
        switch ($this->view_type) {
            case 1:
                return 1;
                break;
            case 3:
                return $this->qoutations()->count();
                break;
            case 4:
                return $this->refrences()->count();
                break;
            case 5:
                return $this->activities()->count();
                break;
            case 6:
                return $this->calenders()->count();
                break;
            case 2:
                return $this->videos()->count();
                break;
            case 7:
                return $this->texts()->count();
                break;
            default:
        }
    }

    public function videos()
    {
        return $this->hasMany(TitleServiceVideo::class, 'service_id');
    }

    public function texts()
    {
        return $this->hasMany(TitleServiceText::class, 'service_id')->orderBy('sort', 'asc');
    }
    public function text()
    {
        return $this->hasOne(TitleServiceText::class, 'service_id');
    }

    public function qoutations()
    {
        return $this->hasMany(TitleServiceQuotation::class, 'service_id')->orderBy('sort', 'asc');
    }

    public function refrences()
    {
        return $this->hasMany(TitleServiceReference::class, 'service_id')->orderBy('sort', 'asc');
    }
    public function activities()
    {
        return $this->hasMany(TitleServiceActivity::class, 'service_id')->orderBy('sort', 'asc');
    }
    public function calenders()
    {
        return $this->hasMany(TitleServiceCalender::class, 'service_id')->orderBy('sort', 'asc');
    }

    public function getModelTypeAttribute()
    {
        return "TitleService";
    }

    public function comments(): MorphMany
    {
        return $this->morphMany(ContentComment::class, 'commentable');
    }

    public function rates(): MorphMany
    {
        return $this->morphMany(ContentRate::class, 'ratable');
    }

    protected $appends = ['model_type'];
}

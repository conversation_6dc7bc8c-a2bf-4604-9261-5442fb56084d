<?php

namespace App\Models;

use App\Models\Traits\GenerateSearch;
use Illuminate\Database\Eloquent\Factories\HasFactory;


class TitleServiceActivity extends Model
{
    use GenerateSearch;
    use HasFactory;

    // fillable
    protected $fillable = [
        'id',
        'service_id',
        'goal',
        'style',
        'imp_type',
        'sort',
        'answer',
        'needed',
        'type',
    ];

    protected $casts = [
        "searchable_text"
    ];

    public function service()
    {
        return $this->belongsTo(TitleService::class, 'service_id');
    }

    public function title()
    {
        return Title::whereHas('services', fn($qy) => $qy->where('id', $this->service_id))->first();
    }

    public function getModelTypeAttribute()
    {
        return "TitleService";
    }
}

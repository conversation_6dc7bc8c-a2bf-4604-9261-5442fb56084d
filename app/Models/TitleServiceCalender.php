<?php

namespace App\Models;

use App\Models\Traits\GenerateSearch;
use Illuminate\Database\Eloquent\Factories\HasFactory;


class TitleServiceCalender extends Model
{
    use GenerateSearch;
    use HasFactory;
    protected $fillable = [
        'id',
        'service_id',
        'goal',
        'style',
        'imp_type',
        'sort',
        'feedback_answers',
        'tools_questions',
        'duration',
    ];

    public function service()
    {
        return $this->belongsTo(TitleService::class, 'service_id');
    }

    public function title()
    {
        return Title::whereHas('services', fn($qy) => $qy->where('id', $this->service_id))->first();
    }

    public function getModelTypeAttribute()
    {
        return "TitleService";
    }


    
}

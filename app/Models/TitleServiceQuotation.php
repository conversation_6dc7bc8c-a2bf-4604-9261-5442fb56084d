<?php

namespace App\Models;

use App\Models\Traits\GenerateSearch;
use Illuminate\Database\Eloquent\Factories\HasFactory;


class TitleServiceQuotation extends Model
{
    use GenerateSearch;
    use HasFactory;

    // fillable
    protected $fillable = [
        'id',
        'service_id',
        'name',
        'text',
        'sort',
    ];

    protected $casts = [
        "searchable_text"
    ];

    public function title()
    {
        return Title::whereHas('services', fn($qy) => $qy->where('id', $this->service_id))->first();
    }

    public function service()
    {
        return $this->belongsTo(TitleService::class, 'service_id');
    }

    
    public function getModelTypeAttribute()
    {
        return "TitleService";
    }
}

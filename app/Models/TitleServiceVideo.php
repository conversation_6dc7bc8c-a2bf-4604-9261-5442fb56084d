<?php

namespace App\Models;

use App\Console\Commands\GenerateSearchableText;
use App\Models\Traits\GenerateSearch;
use Illuminate\Database\Eloquent\Factories\HasFactory;


class TitleServiceVideo extends Model
{
    use GenerateSearch;

    use HasFactory;

    // fillable
    protected $fillable = [
        'id',
        'service_id',
        'video_id',
        'title',
        'detail',
        'video_type',
        'video_file',
        'thumbnail',
    ];

    protected $casts = [
        "searchable_text"
    ];



    public function  generateSearchableText()
    {

        $data = $this->toArray();
        unset($data['model_type']);
        unset($data['created_at']);
        unset($data['updated_at']);


        $this->searchable_text = $data;
        $this->saveQuietly();
    }

    public function service()
    {
        return $this->belongsTo(TitleService::class);
    }

    public function title()
    {
        return $this->belongsTo(Title::class);
    }
}

<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * @property int id
 * @property int title_id
 * @property string href
 * @property int parsing_file_id
 *
 * //Dynamic relations
 * @property ParsingFile parsingFile
 */
class TitlesHref extends Model
{
    protected $table      = 'titles_hrefs';
    protected $guarded    = [];
    public    $timestamps = false;

    public function parsingFile(): HasOne
    {
        return $this->hasOne(ParsingFile::class);
    }
}

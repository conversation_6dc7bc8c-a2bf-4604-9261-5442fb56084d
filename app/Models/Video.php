<?php

namespace App\Models;

use App\Traits\HasSEO;
use Illuminate\Database\Eloquent\Casts\Attribute;

use Illuminate\Database\Eloquent\Model;


class Video extends Model
{
    // use HasSEO;

    protected $table    = 'videos';
    protected $fillable = [
        'id',
        'admin_id',
        'title',
        'is_active',
        'url',
        "video_type",
        "file",
        "thumb",
        'description',
        'created_at',
        'updated_at',
    ];


    protected function thumbUrl(): Attribute
    {
        return Attribute::make(
            get: fn ($value) =>  $this->video_type == 0 ? $this->thumb : url('uploads/' . $this->thumb),
        );
    }
    protected function videoUrl(): Attribute
    {
        return Attribute::make(
            get: fn ($value) =>  $this->video_type == 0 ? $this->url : url('uploads/' . $this->file),
        );
    }
    protected function videoUrlAttributes(): Attribute
    {
        return Attribute::make(
            get: fn ($value) =>  $this->video_type == 0 ? "
            data-src=\"https://www.youtube.com/watch?v=$this->url\"
            ":
            "
            data-lg-size=\"1280-720\" data-video='{\"source\": [{\"src\":\"$this->video_url \", \"type\" :\"video/mp4\"}], \"attributes\" : {\"preload\": false, \"playsinline\" :
                true, \"controls\" : true}}'
            ",
        );
    }

    /**
     * admin id relation method to get how add this data
     * @type hasOne
     * @param void
     * @return object data
     */
    public function admin_id()
    {
        return $this->hasOne(\App\Models\Admin::class, 'id', 'admin_id');
    }


    /**
     * Static Boot method to delete or update or sort Data
     * @param void
     * @return void
     */
    protected static function boot()
    {
        parent::boot();
        // if you disable constraints should by run this static method to Delete children data
        static::deleting(function ($video) {
        });
    }
}

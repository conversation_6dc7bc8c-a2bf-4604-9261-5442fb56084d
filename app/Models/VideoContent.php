<?php

namespace App\Models;


use Illuminate\Database\Eloquent\SoftDeletes;


class VideoContent extends Model
{

    use SoftDeletes;
    protected $dates = ['deleted_at'];

    protected $table    = 'video_contents';
    protected $fillable = [
        'id',
        'admin_id',
        'book_id',

        'title_id',
        'name',
        'description',

        'url',
        'active',

        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * book_id relation method
     * @param void
     * @return object data
     */
    public function book()
    {
        return $this->belongsTo(\App\Models\Book::class);
    }

    /**
     * title_id relation method
     * @param void
     * @return object data
     */
    public function title()
    {
        return $this->belongsTo(\App\Models\Title::class);
    }

    /**
     * Static Boot method to delete or update or sort Data
     * @param void
     * @return void
     */
    protected static function boot()
    {
        parent::boot();
        // if you disable constraints should by run this static method to Delete children data
        static::deleting(function ($videocontent) {
            //$videocontent->book_id()->delete();
            //$videocontent->book_id()->delete();
        });
    }
    public function getModelTypeAttribute()
    {
        return "VideoContent";
    }
    protected $appends = ['model_type'];
}

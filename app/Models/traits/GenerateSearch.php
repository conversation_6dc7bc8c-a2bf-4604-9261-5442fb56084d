<?php

namespace App\Models\Traits;

trait GenerateSearch
{
    /**
     * Boot the trait.
     *
     * @return void
     */
    public static  function  bootGenerateSearch()
    {
        static::updating(function ($model) {
            // Some logic you want to skip sometimes
            $data = $model->toArray();
            unset($data['model_type']);
            unset($data['created_at']);
            unset($data['updated_at']);
            $model->searchable_text = $data;
        });
    }
}

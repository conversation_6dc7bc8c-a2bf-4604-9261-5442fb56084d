<?php

namespace App;

class MultiPanelManager
{
    public ?string $currentPanel = null;
    public static function getPanels()
    {
        return config('panels.panels');
    }

    public  function currentPanel()
    {
        return $this->currentPanel?? config('panels.default');
    }

    public function setPanel(string $currentPanel)
    {
        $this->currentPanel = $currentPanel;
    }

    public function getPanelIdByDomain($domain): string {

        foreach (config('panels.panels') as $id => $panel) {
            if ($panel['domain'] == $domain) return $id;
        }

        return config('panels.default');
    }
}

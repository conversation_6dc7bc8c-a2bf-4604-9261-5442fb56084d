<?php


namespace App\Presenters;


class ParsingActionPresenter
{
    protected bool    $isFinished = true;
    protected ?string $data       = '';

    public function setIsFinished(bool $isFinished): ParsingActionPresenter
    {
        $this->isFinished = $isFinished;
        return $this;
    }

    public function setData(string $data): ParsingActionPresenter
    {
        $this->data = $data;
        return $this;
    }

    public function toArray(): array
    {
        return [
            'next' => $this->isFinished,
            'data' => $this->data,
        ];
    }
}

<?php


namespace App\Presenters;


use Illuminate\Support\Arr;

class ParsingIndex
{
    protected array $data = [];

    public function __construct(string $title, array $href)
    {
        $this->data['title'] = $title;
        $this->data['href']  = $href;
    }

    public static function make(string $title, array $href): ParsingIndex
    {
        return (new static($title, $href));
    }

    public function addChild(string $title, array $href, int $level): ParsingIndex
    {
        $key = $this->getChildKeyForLevel($level);

        Arr::set($this->data, $key, ['title' => $title, 'href' => $href]);
        return $this;
    }

    protected function getChildKeyForLevel(int $level): string
    {
        $key = 'children';
        for ($i = 1; $i < $level; $i++) {
            $parentKey = count(data_get($this->data, $key, []));
            if ($i < $level - 1) {
                $parentKey--;
            }
            $key .= '.' . $parentKey . '.children';
        }
        return rtrim($key, '.children');
    }

    public function getData(): array
    {
        return $this->data;
    }
}

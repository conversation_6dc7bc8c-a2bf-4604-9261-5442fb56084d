<?php

namespace App\Providers;

use App\Facades\Panel;
use App\Filament\Pages\AboutCurriculumAdminPage;
use App\Filament\Pages\AboutCurriculumPage;
use App\Filament\Pages\AboutKholasaPage;
use App\Helpers\TranslatorWithFallback;
use App\Models\AboutCurriculum;
use App\Models\AboutKholasa;
use App\Models\DatabaseNotification;
use App\MultiPanelManager;
use App\Services\FootnoteService;
use App\Services\ParsingService;
use App\Services\QuranService;
use Filament\Facades\Filament;
use Filament\Forms\Components\BaseFileUpload;
use Filament\Forms\Components\FileUpload;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Support\ServiceProvider;
use Illuminate\Foundation\Vite;
use Illuminate\Support\Facades\App;
use Spatie\Activitylog\Models\Activity;
use Spatie\Activitylog\Policies\ActivityPolicy;
use Illuminate\Support\Str;
use Illuminate\Translation\FileLoader;
use Livewire\TemporaryUploadedFile;
use Illuminate\Notifications\DatabaseNotification as BaseNotification;
use Illuminate\Support\Facades\URL;
use Mohamedsabil83\FilamentFormsTinyeditor\Components\TinyEditor;

class AppServiceProvider extends ServiceProvider
{


    /**
     * The policy mappings for the application.
     *
     * @var array
     */
    protected $policies = [
        // Activity::class => ActivityPolicy::class,
    ];

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {


        // if (!(App::runningInConsole() && App::runningConsoleCommand('scribe:generate'))) {
        //     $this->registerTranslation();
        // }
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {

        TextColumn::macro('limitHtml', function ($limit = 100, $end = '...') {
            return $this->formatStateUsing(function ($state) use ($limit, $end) {
                return Str::limit(strip_tags($state), $limit, $end);
            });
        });

        // TinyEditor::configureUsing(function (TinyEditor $tinyEditor) {
        //     $tinyEditor->saveUploadedFileAttachmentsUsing(
        //         fn(\Livewire\TemporaryUploadedFile $file): string => (string) $file->getFilename() . '-' . rand(1000000, 9999999) . '.' . $file->getClientOriginalExtension()
        //     );
        // });

        FileUpload::configureUsing(function (FileUpload $fileUpload) {
            $fileUpload->getUploadedFileNameForStorageUsing(
                fn(TemporaryUploadedFile $file): string => str($file->getClientOriginalName())->beforeLast('.') . '-' . rand(1000000, 9999999) . '.' . $file->getClientOriginalExtension(),
            );
            $fileUpload->canPreview();
            $fileUpload->canDownload();
            $fileUpload->getUploadedFileUrlUsing(static function (BaseFileUpload $component, string $file): ?string {
                /** @var FilesystemAdapter $storage */
                $storage = $component->getDisk();
                $diskName = $component->getDiskName();

                try {
                    if (!$storage->exists($file)) {
                        return null;
                    }
                } catch (UnableToCheckFileExistence $exception) {
                    return null;
                }

                if ($component->getVisibility() === 'private') {
                    try {
                        return $storage->temporaryUrl(
                            $file,
                            now()->addMinutes(5),
                        );
                    } catch (Throwable $exception) {
                        // This driver does not support creating temporary URLs.
                    }
                }
                if ($diskName === 'uploads') {
                    return "https://" . request()->host() . "/uploads/$file";
                }

                return $storage->url($file);
            });
        }, null, true);
        $this->app->singleton('quran', fn() => new QuranService());
        $this->app->singleton('parsing', fn() => new ParsingService());
        $this->app->singleton('footnote', fn() => new FootnoteService());
        $this->app->singleton('panel', fn() => new MultiPanelManager());
        $panel =  Panel::getPanelIdByDomain(request()->host());
        Panel::setPanel($panel);
        // dd(config('app.url'));
        Str::macro('highlightSearch', function ($input, $searchString) {
            return str_replace($searchString, "<span class='highlight'>$searchString</span>", $input);
        });
        Filament::serving(function () {
            // Using Vite
            Filament::registerTheme(
                app(Vite::class)('resources/css/kholasah-theme.css'),
            );
            Filament::registerNavigationGroups(array_values(__('common.navigation_groups')));
            Filament::registerStyles([
                url("css/all.min.css"),
            ]);
            Filament::registerPages([
                //    AboutKholasaFPage::class,
                //    AboutCurriculumAdminPage::class,
            ]);
        });

        //cutomisze notification model
        $this->app->instance(BaseNotification::class, new DatabaseNotification());
    }
    private function registerTranslation()
    {
        $this->app->extend('translator', function ($translator, $app) {

            $trans = new TranslatorWithFallback(new FileLoader($app['files'], [__DIR__ . '/lang', $app['path.lang']]), $app['config']['app.locale']);

            $trans->setFallback($app['config']['app.fallback_locale']);

            return $trans;
        });
    }
}

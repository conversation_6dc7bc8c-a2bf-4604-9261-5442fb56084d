<?php

namespace App\Services;

use voku\helper\HtmlDomParser;
// use PHPHtmlParser\Dom;
// use PHPHtmlParser\Dom\Node\HtmlNode;
// use PHPHtmlParser\Dom\Node\TextNode;

class FootnoteService
{
    // public function injectFoots(string $body)
    // {
    //     $body  = (new Dom)->loadStr($body);
    //     $index = 1;
    //     foreach ($body->find('[href^=#_ftn]') as $foot) {
    //         /** @var HtmlNode $foot */
    //         $footNo = str_replace('#', '', $foot->getAttribute('href'));
    //         $foot->removeAllAttributes();
    //         $foot->setAttribute('role', 'button')
    //             ->setAttribute('data-toggle', 'popover')
    //             ->setAttribute('data-placement', 'top')
    //             ->setAttribute('data-trigger', 'focus')
    //             ->setAttribute('tabindex', '0')
    //             ->setAttribute('data-content', $this->foots[$footNo]);
    //         foreach ($foot->getChildren() as $child) {
    //             /** @var HtmlNode $child */
    //             $child->delete();
    //         }
    //         $foot->addChild(new TextNode("[$index]"));
    //         $index++;
    //     }
    //     return (string) $body;
    // }
    public function extract(String $htmlString)
    {
        $dom = HtmlDomParser::str_get_html($htmlString);
        foreach ($dom->find('[name^="_ftnref"]') as $key =>  $ref) {
            $index = $key + 1;
            $refSouce = $dom->find('#ftn' . $index);
            $str = "";
            $str = preg_replace("/\[[^\]]*\]/", "", $refSouce->text);
            $str = implode("", str_replace("()", "", $str));
            // dump($str);
            $ref->parent()->outerHtml = '<a data-trigger="focus" tabindex="0" class="btn-ftn" role="button" data-toggle="popover" data-placement="top" data-content= "' . $str . '">([' . $index . '])</a>';
        }
        $dom->findOne('#ftn1')->parent()->delete();
        // dump($dom->outerHtml);
        return $dom->outerHtml;
    }

    public function inject(String $htmlString)
    {
        $fnts = [];
        $dom = HtmlDomParser::str_get_html($htmlString);
        foreach ($dom->find('[data-toggle="popover"]') as $key =>  $ref) {
            $index = $key + 1;
            $fnts[]=$ref->getAttribute('data-content');
        }
        return $fnts;
    }
}

<?php


namespace App\Services\ParsingActions;

use App\Facades\Panel;
use App\Models\Content;
use App\Models\Title;
use App\Models\TitlesHref;
use App\Presenters\ParsingActionPresenter;
use Illuminate\Support\Facades\Log;
use PHPHtmlParser\Dom;
use PHPHtmlParser\Dom\Node\HtmlNode;
use PHPHtmlParser\Dom\Node\TextNode;

class ProcessIndexesAction implements ParsingActionInterface
{
    /** @var TitlesHref[] - use it with array access */
    protected      $indexes = [];
    protected      $foots   = [];
    protected int  $bookId;
    protected ?int $adminId;

    public function run(): ParsingActionPresenter
    {
        $this->bookId  = parsing()->getFile()->book_id;
        $this->adminId = null;
        $this->saveContentOfTheIndexes();
        return new ParsingActionPresenter;
    }

    protected function loadIndexesAndFoots()
    {
        Log::error('loadIndexesAndFoots');
        $this->foots   = require parsing()->getFile()->getFullPathAttribute('footnotes-');
        $this->indexes = parsing()->getFile()->hrefs->mapWithKeys(fn(TitlesHref $href) => [$href->href => $href]);
        Log::error(parsing()->getFile());
        Log::error($this->indexes);
    }

    protected function saveContentOfTheIndexes()
    {
        $this->loadIndexesAndFoots();
        $savedTitles = [];

        parsing()->loadHTMLFile(parsing()->getFile()->fullPath);

        foreach (parsing()->getDom()->getChildren() as $section) {
            // Log::info(parsing()->stripText($section));
            // there is text between section, ignore it
            if ($section->isTextNode()) {
                continue;
            }
            /** @var HtmlNode $section */
            //here we are sure that we finished the content
            if (parsing()->isIndexesSection($section)) {
                break;
            }
            $body    = '';
            $titleId = null;
            foreach ($section->getChildren() as $paragraph) {
                // foreach ($paragraph->find('[name^=_Toc]') as $item) {
                //     /** @var HtmlNode $item */
                //     $index = $item->getAttribute('name');
                //     Log::error($this->indexes);
                //     if ($this->indexes[$index] ?? null) {
                //         if ($titleId) {
                //             $this->storeContent($titleId, $body);
                //             $body = '';
                //         }
                //         $titleId       = $this->indexes[$index]->title_id;
                //         $savedTitles[] = $titleId;
                //     }
                // }


                foreach ($paragraph->find('[name^=_Toc]') as $item) {
                 
                    $index = $item->getAttribute('name');
                    if (!empty($this->indexes[$index])) {
                        if ($titleId && trim($body)) {
                            $this->storeContent($titleId, $body);
                            $body = '';
                        }
                        $titleId = $this->indexes[$index]->title_id;
                        $savedTitles[] = $titleId;
                    }
                }

                $body .= $paragraph;
            }
            $body && $titleId && $this->storeContent($titleId, $body);
            if ($body && !$titleId) {
                parsing()->getFile()
                    ->errors()
                    ->create([
                        'error'  => 'Found section body without title id!',
                        'source' => __METHOD__,
                        'other'  => parsing()->stripText($body, 500),
                    ]);
            }
        }
        $notFond = array_values(array_diff($this->indexes->pluck('title_id')->toArray(), $savedTitles));
        if ($notFond) {
            parsing()->getFile()
                ->errors()
                ->create([
                    'error'  => 'Failed to get content for titles!',
                    'source' => __METHOD__,
                    'other'  => Title::query()->find($notFond)->pluck('name')->toJson(),
                ]);
        }
    }

    protected function storeContent(int $titleId, string $body)
    {
        $body = quran()->replaceTextAyat($body);
        $body = stripContentText($body);
        $body = $this->replaceImageSrc($body);
        Log::info('storeContent: ' . $titleId . ' ' . $body);

       
        Content::insert([
            'title_id' => $titleId,
            'book_id'  => $this->bookId,
            'admin_id' => $this->adminId,
            'text'     => $this->injectFoots($body),
        ]);
    }

    protected function injectFoots(string $body)
    {
        $body  = (new Dom)->loadStr($body);
        $index = 1;
        foreach ($body->find('[href^=#_ftn]') as $foot) {
            /** @var HtmlNode $foot */
            $footNo = str_replace('#', '', $foot->getAttribute('href'));
            $foot->removeAllAttributes();
            $foot->setAttribute('role', 'button')
                ->setAttribute('data-toggle', 'popover')
                ->setAttribute('data-placement', 'top')
                ->setAttribute('data-trigger', 'focus')
                ->setAttribute('tabindex', '0')
                ->setAttribute('data-content', $this->foots[$footNo]);
            foreach ($foot->getChildren() as $child) {
                /** @var HtmlNode $child */
                $child->delete();
            }
            $foot->addChild(new TextNode("[$index]"));
            $index++;
        }
        return (string) $body;
    }
    protected function replaceImageSrc(string $body)
    {
        $body  = (new Dom)->loadStr($body);
        foreach ($body->find('img') as $image) {
            /** @var HtmlNode $foot */
            $src = $image->getAttribute('src');
            $filename = basename($src);
            $src = url('uploads/' . Panel::currentPanel() . "/books/contents/images/" . parsing()->getFile()->id . '/' . $filename);
            $image->setAttribute('src', $src);
        }
        return (string) $body;
    }
}

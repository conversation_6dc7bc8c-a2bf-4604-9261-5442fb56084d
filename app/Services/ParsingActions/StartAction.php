<?php

namespace App\Services\ParsingActions;

use App\Models\Title;
use App\Models\TitlesHref;
use App\Presenters\ParsingActionPresenter;
use App\Presenters\ParsingIndex;
use Illuminate\Support\Facades\Log;
use PHPHtmlParser\Dom;
use PHPHtmlParser\Dom\Node\HtmlNode;
use PHPHtmlParser\Dom\Node\TextNode;
use stringEncode\Exception;

class StartAction implements ParsingActionInterface
{
    protected bool  $indexesSavedBefore;
    protected array $indexes = [];
    protected array $hrefs   = [];

    public function run(): ParsingActionPresenter
    {
        parsing()->getFile()->setIsParsing();
        $this->indexesSavedBefore = parsing()->getFile()->indexes_extracted;

        if (!parsing()->getFile()->indexes_processed) {
            $this->parseIndexes();
        }
        return (new ParsingActionPresenter)
            ->setIsFinished($this->indexesSavedBefore)
            ->setData($this->indexesSavedBefore ? '' : 'تم معالجة الملف معالجة أولية، وجاري حفظ الفهارس.');
    }

    /**
     * get indexes using (MsoToc1,MsoToc2,MsoToc3, ...)
     */
    protected function parseIndexes()
    {
        /** @var ParsingIndex|null $currentIndex */
        $currentIndex = null;
        foreach ($this->getIndexesDom() as $paragraph) {
            /** @var HtmlNode|TextNode $paragraph */
            if ($paragraph->isTextNode() || !$paragraph->tag->hasAttribute('class')) {
                continue;
            }
            $level = $paragraph->tag->getAttribute('class')->getValue();
            $level = str_replace('msotoc', '', strtolower($level));
            $level = intval($level);
            if (!($level >= 1)) {
                continue;
            }
            $title = parsing()->stripText($paragraph->innerText());
            $title = trim(mb_ereg_replace("\d+$", "", $title));         //remove numbers at the end
            $title = trim(mb_ereg_replace("\.\.$|\.$|\:$", "", $title)); //remove dot's at the end
            $href  = $this->getIndexHref($paragraph);
            //TODO:: register empty
            if ($level == 1 && $currentIndex) {
                $this->indexes[] = $currentIndex->getData();
            }

            if ($level == 1) {
                $currentIndex = ParsingIndex::make($title, $href);
            } elseif (!$currentIndex) {
                parsing()
                    ->getFile()
                    ->errors()
                    ->create([
                        'error'  => 'Index without parent!',
                        'source' => __METHOD__,
                        'other'  => json_encode(['title' => $title, 'href' => $href], JSON_UNESCAPED_UNICODE),
                    ]);
            } else {
                $currentIndex->addChild($title, $href, $level);
            }
        }
        if ($currentIndex) {
            $this->indexes[] = $currentIndex->getData();
        }
        $this->storeIndexesToDB($this->indexes, parsing()->getFile()->title_id, firstCall: true);
    }

    protected function getIndexHref(HtmlNode $indexDom): array
    {
        $indexes = [];
        foreach ($indexDom->find('a[href^="#_"]') as $index) {
            $indexes[] = str_replace('#', '', $index->tag->getAttribute('href')->getValue());
        }

        if (!$indexes) {
            parsing()
                ->getFile()
                ->errors()
                ->create([
                    'error'  => 'Index without Link (href)!',
                    'source' => __METHOD__,
                    'other'  => $indexDom->innerText(),
                ]);
            //throw new \Exception("Can't find link to the content of Index:" . $indexDom->innerText());
        }

        return $indexes;
    }

    protected function storeIndexesToDB(array $indexes, ?int $parentId = null, $firstCall = false)
    {

       
        parsing()->getFile()->update(['status' => 0]);
        $fileId = parsing()->getFile()->id;
        $loop = 0;
        foreach ($indexes as $index) {
            $loop++;
            $data  = [
                'admin_id'  => null,
                'name'      => $index['title'] ?? '',
                'book_id'   => parsing()->getFile()->book_id,
                'parent_id' => $parentId ?? -1,
                "order" => $loop,
            ];
            $title = Title::updateOrCreate($data);
            foreach ($index['href'] ?? [] as $href) {
                $this->hrefs[] = [
                    'title_id'        => $title->id,
                    'parsing_file_id' => $fileId,
                    'href'            => $href,
                ];
            }
            $this->storeIndexesToDB($index['children'] ?? [], $title->id);
        }
        Log::error('parentId: ' . $parentId);
        if (!$parentId  || ($firstCall && $parentId)) {
            foreach (array_chunk($this->hrefs, 500) as  $hrefs) {
                TitlesHref::query()->insert($hrefs);
            }
            $this->hrefs = [];
        }
        // parsing()->getFile()->update(['indexes_processed' => 1]);
    }

    /**
     * When MS Word Saves Document as html it will be like:
     *  <html>
     *      <head>...</head>
     *      <body>
     *          <div class="WordSection1"></div>
     *          ...
     *          <div class="WordSection{n}">
     *              <p ...>...</p>  <==== This is what we will search in
     *          </div>
     *      </body>
     *  </html>
     * We need to search for the indexes Section by searching in each paragraph tag.
     */
    protected function getIndexesDom(): array
    {
        parsing()->loadHTMLFile(parsing()->getFile()->getIndexesPath($this->indexesSavedBefore));
        if ($this->indexesSavedBefore) {
            return parsing()->getDom()->getChildren();
        }
        $indexes        = null;
        $indexExtracted = false;
        $footnotes      = null;
        // $footnotes      = new HtmlNode('div'); // TODO
        foreach (parsing()->getDom()->getChildren() as $section) {
            // there is text between section, ignore it
            if ($section->isTextNode()) {
                continue;
            }

            if (!$indexExtracted && parsing()->isIndexesSection($section)) {
                $indexes        = $section;
                $indexExtracted = true;
                continue;
            }
            if ($indexExtracted && $section->find('#ftn1,[name=_ftn1]', 0)) {
                $footnotes = $section;
                break;
            }
        }
        if (!$indexes) {
            throw new Exception('تعذر إستخراج الفهارس!');
        }
        if (!$footnotes) {
            $footnotes = new HtmlNode('div');
            // throw new Exception('تعذر إستخراج الحواشي!');
        }
        $this->saveIndexes($indexes);
        if ($footnotes) {
            $this->saveFootnotes($footnotes);
        }
        return [];
    }

    protected function saveIndexes(HtmlNode $indexes)
    {
        $path = parsing()->getFile()->getIndexesPath(true);
        file_put_contents(
            $path,
            '<html lang="ar">'
                . '<head><title>Indexes</title><meta http-equiv=Content-Type content="text/html; charset=utf-8"></head>'
                . '<body>' . $indexes->innerHtml() . '</body>'
                . '</html>'
        );
        parsing()->getFile()->setIndexesExtracted();
    }

    protected function saveFootnotes(HtmlNode $footnotes)
    {
        $path         = parsing()->getFile()->getFullPathAttribute('footnotes-');
        $footnotesArr = [];
        foreach ($footnotes->find('[id^=ftn], [name^=_ftn]') as $footnote) {
            /** @var HtmlNode $footnote */
            if ($footnote->hasAttribute('id')) {
                $id   = $footnote->id();
                $foot = $footnote->innerText();
            } else {
                $id   = $footnote->getAttribute('name');
                $foot = $footnote->parent->innerText();
            }
            $foot              = parsing()->stripText($foot);
            $footnotesArr[$id] = preg_replace('/(^\(([\s\d\[\]]+)\)([\s]+)?)/', '', $foot);
        }
        file_put_contents($path, '<?php return ' . var_export($footnotesArr, true) . ';');
    }
}

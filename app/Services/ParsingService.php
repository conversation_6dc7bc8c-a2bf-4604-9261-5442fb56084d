<?php


namespace App\Services;

use App\Enums\ParsingActions;
use App\Models\ParsingFile;
use Illuminate\Support\Str;
use PHPHtmlParser\Dom;
use PHPHtmlParser\Exceptions\ChildNotFoundException;
use PHPHtmlParser\Exceptions\CircularException;
use PHPHtmlParser\Exceptions\UnknownChildTypeException;
use PHPHtmlParser\Options;
use stringEncode\Exception;
use PHPHtmlParser\Dom\Node\HtmlNode;

class ParsingService
{
    use HasHeavyProcessing;

    protected ?ParsingFile $file   = null;
    protected ?HtmlNode    $dom    = null;
    protected ?string      $action = ParsingActions::START;

    public function __construct()
    {
        $this->setInitializationSettings();
    }

    public function setFile(ParsingFile $file): ParsingService
    {
        $this->file = $file;
        return $this;
    }

    public function getFile(): ?ParsingFile
    {
        return $this->file;
    }

    public function loadHTMLFile(string $filePath, $shouldBeUtf8 = false): ParsingService
    {
        $options = (new Options)->setEnforceEncoding("UTF-8");
        $dom     = (new Dom)->loadFromFile($filePath, $options);
        if ($shouldBeUtf8) {
            $this->isCharsetUtf8($dom);
        }
        $this->dom = $dom->find('body', 0);
        unset($dom);
        return $this;
    }

    public function isCharsetUtf8(Dom $dom): bool
    {
        /** @var Dom\Tag $head */
        $charset = $dom->find('head meta[content*=charset]', 0)->tag ?? null;
        if (! $charset) {
            return true;
        }
        $charset = strtolower($charset->getAttribute('content')->getValue());
        if (str_contains($charset, 'utf-8')) {
            return true;
        }

        throw new \Exception("Current Encoding ($charset) is not UTF-8!");
    }

    public function getDom(): ?HtmlNode
    {
        return $this->dom;
    }

    /**
     * @param string|ParsingActions $action
     *
     * @return $this
     * @throws \Throwable
     */
    public function setAction(string $action): ParsingService
    {
        throw_unless(ParsingActions::IS_EXISTS($action), new Exception(sprintf('Unknown Processing Action (%s)!', $action)));
        $this->action = $action;
        return $this;
    }

    /**
     * @throws \Throwable
     */
    public function process(): array
    {
        throw_unless($this->file, new Exception('There is no file to process!'));

        return ParsingActions::ActionClassFor($this->action)->run()->toArray();
    }

    /**
     * @param HtmlNode $section
     *
     * @return bool
     * @throws ChildNotFoundException
     * @throws CircularException
     * @throws UnknownChildTypeException
     */
    public function isIndexesSection(HtmlNode $section): bool
    {
        return (bool) $section->find('[href^="#_Toc"]', 0);
    }

    public function stripText($str, $strLen = 0, $addTxt = ''): string
    {
        $str = strip_tags($str);
        $str = str_replace('&nbsp;', ' ', $str);
        $str = preg_replace('!\s+!', ' ', $str);
        $str = html_entity_decode($str);
        $str = trim($str);
        if ($strLen != 0 && mb_strlen($str) > $strLen) {
            $str = mb_substr($str, 0, $strLen) . '...' . $addTxt;
        }

        return $str;
    }

    public function convertFileEncoding(string $path, string $from = null, ?string $to = 'UTF-8')
    {
        $html    = file_get_contents($path, false, null);
        // dd($html);
        $charset = (new Dom)->loadStr($html)->find('head meta[content*=charset]', 0)->tag ?? null;
        if (! $charset) {
            return;
        }
        $charset = strtolower($charset->getAttribute('content')->getValue());
        if (str_contains($charset, 'utf-8')) {
            return;
        }
        $charset = $from ?: Str::after($charset, 'charset=');
        $content = file_get_contents($path);
        try {
            $content = iconv(strtoupper($charset), $to, $content);
        } catch (\Exception $exception) {
            $this->getFile()->errors()
                ->create([
                    'error'  => $exception->getMessage(),
                    'source' => __METHOD__,
                    'other'  => json_encode(['charset' => $charset, 'path' => $path]),
                ]);
            //TODO:: make sure this not cause problems
            $content = mb_convert_encoding($content, $to, $charset);
        }
        $content = preg_replace('/<meta (.+)charset=([a-z\-0-9]+)">/i', '<meta charset="UTF-8">', $content);
        //
        $bom = chr(239) . chr(187) . chr(191); # use BOM to be on safe side
        file_put_contents($path, $bom . $content);
    }
}

<?php


namespace App\Services;


use App\Enums\AyatTypes;
use App\Enums\QuranVersions;
use Illuminate\Support\Facades\DB;
use PHPHtmlParser\Dom;

class QuranService
{
    use HasHeavyProcessing;

    protected array  $othmanyPages = [];
    protected string $quranVersion = QuranVersions::NORMAL;
    protected string $ayatType     = AyatTypes::SIMPLE;

    public function __construct()
    {
        $this->setInitializationSettings();
    }

    protected function getOthmanyForPage(int $page, ?string $fontName = null)
    {
        if (isset($this->othmanyPages[$page])) {
            return $this->othmanyPages[$page];
        }
        $table    = [
            'QCF_P' => QuranVersions::NORMAL,
            'QCF'   => QuranVersions::NORMAL,
            'QCF2'  => QuranVersions::V2,
        ][$fontName] ?? $this->quranVersion;
        $ayahType = $this->ayatType . 'Word';
        return $this->getOthmanyAyat($table, $page)
            ->map(function ($word) use ($ayahType) {
                $word->$ayahType = " " . $word->$ayahType . " ";
                return $word;
            })->pluck($ayahType, 'othmanyChar')
            ->toArray();
    }

    /**
     * @param string $fontPrefix //font name without numbers
     *
     * @return Dom
     * @throws \PHPHtmlParser\Exceptions\ChildNotFoundException
     * @throws \PHPHtmlParser\Exceptions\CircularException
     * @throws \PHPHtmlParser\Exceptions\CurlException
     * @throws \PHPHtmlParser\Exceptions\NotLoadedException
     * @throws \PHPHtmlParser\Exceptions\StrictException
     */
    public function replaceTextAyat($text): Dom
    {
        if (is_string($text)) {
            // add class to ayat brackets
            $text = preg_replace('/(﴿|\&\#64831\;|\&\#64830\;|﴾)+/', "<span class='ayah-bracket'> $0 </span>", $text);
            $text = str_replace("{", ' { ', $text);
            $text = str_replace("}", ' } ', $text);
            $text = str_replace("  ", ' ', $text);
            $dom  = new Dom;
            // dump($text);
            $html = $dom->loadStr($text);
        }
        // dump($html);

        foreach ($html->find('[style*="QCF"]') as $element) {
            // dump($element);
            preg_match('/(QCF2|QCF_P|AQF_P|QCF_|QCF)(\d+)/', $element->tag->getAttribute('style')
                ->getValue(), $matches);
            $element->removeAllAttributes('style');
            $element->tag->setAttributes(['class' => 'ayah']);
            if (!isset($matches[2])) {
                continue;
            }
            $this->setAyahText($element, intval($matches[2]), $matches[1]);
        }



        return $html;
    }

    /**
     * @param Dom $element
     * @param int $page
     *
     * @throws \PHPHtmlParser\Exceptions\NotLoadedException
     */
    private function setAyahText($element, $page, $fontPrefix = null)
    {
        if ($element->isTextNode()) {
            $text = strtr(parsing()->stripText($element->text), $this->getOthmanyForPage($page, $fontPrefix));
            $text != '' && $element->setText($text);
            return;
        }

        $children = $element->getChildren();
        foreach ($children as $child) {
            $this->setAyahText($child, $page, $fontPrefix);
        }
    }

    public function getOthmanyAyat($table = QuranVersions::NORMAL, $page = 1): \Illuminate\Support\Collection
    {
        return DB::table($table)
            ->select('*', DB::raw("concat(sora_id,'_',ayah) sora_ayah"))
            ->where('page', $page)
            ->where('ayah', '!=', 0)
            ->get();
    }

    public function getFontForPage($quran = QuranVersions::NORMAL, string $page3Digits = '001'): array
    {
        return [
            QuranVersions::SVG    => [
                'name'   => "AQF_P{$page3Digits}_HA",
                'url'    => url("fonts/SVG/AQF_P{$page3Digits}_HA.svg#AQF_P{$page3Digits}_HA"),
                'format' => 'svg'
            ],
            QuranVersions::NORMAL => [
                'name'   => "QCF_P" . $page3Digits,
                'url'    => url("fonts/QCF_P/QCF_P{$page3Digits}.ttf"),
                'format' => 'truetype'
            ],
            QuranVersions::V2     => [
                'name'   => "QCF2" . $page3Digits,
                'url'    => url("fonts/QCF2/QCF2{$page3Digits}.ttf"),
                'format' => 'truetype'
            ],
        ][$quran];
    }

    /**
     * @param string $text
     * @param string|QuranVersions $from
     * @param string|AyatTypes $to
     */
    public function decode(string $text, ?string $from = QuranVersions::NORMAL, ?string $to=AyatTypes::SIMPLE): string
    {
        if (QuranVersions::IS_EXISTS($from)) {
            $this->quranVersion = $from;
        }
        if (AyatTypes::IS_EXISTS($to)) {
            $this->ayatType = $to;
        }

        return $this->replaceTextAyat($text);
    }
}

<?php

namespace App\Traits;

use App\Facades\Panel;
use App\Models\SEO;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Support\Facades\Cache;

trait HasSEO
{


    public function seo(): MorphOne
    {
        return $this->morphOne(SEO::class, 'model')->withDefault();
    }

    public function CachedSeo()
    {
        return Cache::rememberForever("new_seo." . Panel::currentPanel() . '.' . class_basename($this) . "." . $this->id,  function () {

            return $this->seo;
        });
    }

    protected static function bootHasSEO()
    {
        // if you disable constraints should by run this static method to Delete children data
        static::deleting(function ($model) {
            $key = ("new_seo." . Panel::currentPanel() . '.' . class_basename($model) . "." . $model->id);
            Cache::forget($key);
        });
        static::updating(function ($model) {
            $key = ("new_seo." . Panel::currentPanel() . '.' . class_basename($model) . "." . $model->id);
            Cache::forget($key);
        });

        static::saved(function ($model) {
            
            $key = ("new_seo." . Panel::currentPanel() . '.' . class_basename($model) . "." . $model->id);
            Cache::forget($key);
        });
    }
}

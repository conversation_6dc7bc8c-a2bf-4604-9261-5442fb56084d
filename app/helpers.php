<?php

use Filament\Forms\Components\Group;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

if (!function_exists('quran')) {
    /**
     * @return \App\Services\QuranService
     */
    function quran()
    {
        return app('quran');
    }
}
if (!function_exists('footnote')) {
    /**
     * @return \App\Services\QuranService
     */
    function footnote()
    {
        return app('footnote');
    }
}

if (!function_exists('parsing')) {
    /**
     * @return \App\Services\ParsingService
     */
    function parsing()
    {
        return app('parsing');
    }
}
if (!function_exists('stripContentText')) {
    /**
     * @return string
     */
    function stripContentText(string $str): string
    {
        $str = str_replace('&nbsp;', ' ', $str);
        $str = html_entity_decode($str);
        return $str;
    }
}
if (!function_exists('searchText')) {
    /**
     * @return string
     */
    function searchText(string $str, string $search): string
    {

        $str = strip_tags($str);

        if (Str::contains($str, $search)) {
            $str =  $search . " " . Str::after($str, $search);
        }

        $str = Str::limit($str, "100", '...');
        return $str;
    }
}
if (!function_exists('bracketsToFootnote')) {
    /**
     * @return string
     */
    function bracketsToFootnote(string $text): string
    {
        $pattern = "/\[\[(.*?)\]\]/";

        $replacement = '<a role="button" data-toggle="popover" data-placement="top" data-trigger="focus" tabindex="0" data-content=\'$1\' data-original-title="" title="">[2]</a>';
        $counter = 1;
        $result = preg_replace_callback($pattern, function ($match) use (&$counter) {
            $replacement = '<a role="button" data-toggle="popover" data-placement="top" data-trigger="focus" tabindex="0" data-content=\'' . $match[1] . '\' data-original-title="" title="">[' . $counter . ']</a>';
            $counter++;
            return $replacement;
        }, $text);
        return $result;
    }
}
if (!function_exists('get_platform_type')) {
    /**
     * @return string
     */
    function get_platform_type(): string
    {
        return request()->get('platform_type') ?? 'Kholasah';
    }
}
if (!function_exists('get_sub_domain_from_url')) {
    function get_sub_domain_from_url(): string
    {
        return Str::before(request()->host(), '.');
    }
}


if (!function_exists('get_last_titles')) {
    function get_last_titles($title)
    {

        $titles = [];

        if ($title->subtitles->count() == 0) {
            return ($title);
        }

        foreach ($title->subtitles as $tit) {
            $titles[$tit->name] = get_last_titles($tit);
        }

        return $titles;
    }
}


if (!function_exists('download_titles')) {
    function download_titles($from_title, $to_title)
    {

        $_titles = \App\Models\Title::whereBetween('id', [$from_title, $to_title])->orderBy('order')
            ->with([

                "subtitles" => function ($query) {
                    $query->orderBy('order');
                }
            ])
            ->when(
                $from_title != $to_title,
                fn($qry) =>
                $qry->where('parent_id', -1)
            )
            ->get();

        $titles = [];
        foreach ($_titles as $title) {

            $titles[$title->name] = get_last_titles($title);
        }

        $titles = Arr::dot($titles);


        // $book_name = Arr::first($titles)->book->name;
        // $file_name = now() . ".pdf";
        $htmlContent = '';

        $template = '<html dir="rtl">
                    <head>
                    <meta charset="utf-8" />
                    <style>
                    @font-face {
                        src: url("' . url('/fonts/me_quran.ttf') . '");
                        font-family: "UthmanicHafs";
                    }
                    @font-face {
                        src: url("' . url('/fonts/al_majeed.ttf') . '");
                        font-family: "Almajeed";
                    }

                    @font-face {
                        font-family: "Droid Arabic Naskh";
                        src: url("' . url('/fonts/Droid-Arabic-Naskh.ttf.woff') . '") format("woff"),
                            url("' . url('/fonts/Droid-Arabic-Naskh.ttf.svg#Droid-Arabic-Naskh') . '") format("svg"),
                            url("' . url('/fonts/Droid-Arabic-Naskh.ttf.eot') . '"),
                            url("' . url('/fonts/Droid-Arabic-Naskh.ttf.eot?#iefix') . '") format("embedded-opentype");
                        font-weight: normal;
                        font-style: normal;
                    }

                    @font-face {
                        font-family: "Droid Arabic Naskh";
                        src: url("' . url('/fonts/Droid-Arabic-Naskh-bold.ttf') . '") format("truetype");
                        font-weight: 700;
                        font-style: normal;
                    }
                    img{
                        display:none;
                    }
                    img[src*="uploads"]{
                        display: block !important;
                        width: 100% !important;
                        height: auto !important;
                    }
                    .content::first-line {
                        // position: relative;
                        // color: #e78c3a;
                        // font-size: 2rem;
                        // font-weight: bold;
                        // text-decoration: underline #e78c3a 2px;
                        // font-family: "Greta Arabic" !important;
                    }

                    .header {
                        position: relative;
                        color: #e78c3a;
                        font-size: 2rem !important;
                        font-weight: bold;
                        text-decoration: underline #e78c3a 2px;
                        font-family: "Greta Arabic" !important;
                        vertical-align: middle;
                        height:100hv;
                    }
                    .content * {
                        font-family: "Droid Arabic Naskh", serif !important;
                        text-align: justify !important;
                        line-height: 2 !important;
                top: unset !important;
                bottom: unset !important;
                right: unset !important;
                left: unset !important;
                    }
                    .content{}
                    .ayah,
                    .ayah .ayah-bracket {
        font-family: "UthmanicHafs" !important;
        color: #e78c3a;
        font-size: 1.4rem !important;
        line-height: 1.5 !important;
        font-weight: 400;
        }
        .ayah-bracket{
        font-family: "Almajeed", serif !important;
        color: #e78c3a;
        font-size: 1.4rem !important;

        }
                    </style>
                    </head>
                    <body class="content">
                    {{html}}
                    </body>
                    </html>';




        foreach ($titles as $key => $title) {

            $footnote = "";
            foreach (footnote()->inject($title->content->text ?? "") as $_key => $value) {
                $footnote .= "<p dir='rtl'> [" . strval($_key + 1) . "] $value </p>";
            }

            if (!empty($footnote)) {
                $footnote = "<hr/> $footnote ";
            }
            $text =  $title->content?->text ?? "";
            $pattern = '/src="([^"]+)"/';
            $text = preg_replace($pattern, 'src="' . url('/') . '/$1"', $text);

            $html = $text . $footnote;

            $html = '<div>' . $html . '</div>';


            //set title
            $html = "<h1 style='page-break-before: always;' class='header'>" .  str_replace('.', ' - ', $key) . "</h1>" . $html;
            $htmlContent = $htmlContent . $html;
        }
        $pdf = App::make('dompdf.wrapper');
        $pdf->setOption('enable-local-file-access', true);
        return $pdf->loadHTML(str_replace('{{html}}', $htmlContent, $template))->output();
    }
}


if (!function_exists('get_seo_filament_fields')) {
    function get_seo_filament_fields($statePath = "seo")
    {

        return \Filament\Forms\Components\Fieldset::make('seo')->schema(
            [

                \Filament\Forms\Components\TagsInput::make('keywords')
                    ->translateLabel()
                    ->label(__('seo.fields.keywords.label'))
                    ->placeholder(__('seo.fields.keywords.placeholder'))
                    ->columnSpan(2),

                \Filament\Forms\Components\Textarea::make('description')
                    ->translateLabel()
                    ->label(__('seo.fields.description.label'))
                    ->placeholder(__('seo.fields.description.placeholder'))
                    ->helperText(function (?string $state): string {
                        return (string) Str::of(strlen($state))
                            ->append(' / ')
                            ->append(160 . ' ')
                            ->append(Str::of(__('seo.fields.characters.label'))->lower());
                    })

                    ->reactive()
                    ->columnSpan(2)

            ]

        )
            ->label("SEO")
            ->afterStateHydrated(function (\Filament\Forms\Components\Fieldset $component, ?Model $record): void {

                if ($record) {
                    $component->getChildComponentContainer()->fill(
                        $record?->seo->toArray() ?: []
                    );
                }
            })
            ->statePath($statePath ?? 'seo')
            // ->dehydrated(false)
            ->saveRelationshipsUsing(function (Model $record, array $state): void {
                $state = collect($state)->map(fn($value) => $value ?: null)->all();

                if ($record->seo && $record->seo->exists) {
                    $record->seo->update($state);
                } else {
                    $record->seo()->create($state);
                }
            });
    }
}


if (!function_exists('get_seo_meta_tag')) {
    function get_seo_meta_tag($seo)
    {

        $tags = [];
        if ($seo->keywords && count($seo->keywords)) {
            // <meta name="keywords" content="HTML, CSS, JavaScript">
            $tags[] = '<meta name="keywords" content="' . implode(', ', $seo->keywords) . '">';
        }
        if ($seo->description) {
            // <meta name="keywords" content="HTML, CSS, JavaScript">
            $tags[] = '<meta name="description" content="' . $seo->description . '">';
        }

        return implode('', $tags);
    }
}

if (!function_exists('add_highlight_to_text')) {
    function add_highlight_to_text($text, $highlightable_text)
    {

        if (!empty($text) && !empty($highlightable_text)) {

            $highlightable_text = get_searchable_text($highlightable_text);

            $words = explode(' ', $text);

            foreach ($words as $key => $word) {
                if (str_contains(get_searchable_text($word), $highlightable_text)) {
                    $words[$key] = "<span class='highlight'>$word</span>";
                }
            }
            return implode(' ', $words);
        }
        return $text;
    }
}


if (!function_exists('get_searchable_text')) {
    function get_searchable_text($text)
    {
        //remove html from text
        $text = strip_tags($text);
        //
        $diacritic = array("ِ", "ٰ", "ّ", "ۡ", "ٖ", "ٗ", "ؘ", "ؙ", "ؚ", "ٍ", "َ", "ُ", "ٓ", "ْ", "ٌ", "ٍ",  "ً",  "ّ", "ۤ", "ء", "~");
        // Remove diacritics and Hamza
        $text = str_replace($diacritic, '', $text);
        // Replace Ta Marbuta (ة) with Ha (ه)
        $text = str_replace('ة', 'ه', $text);
        // Replace Aleft (أ-آ-إ) with Alef (ا)
        $replacement = ['آ', 'أ', 'إ', 'أ'];
        $text = str_replace($replacement, 'ا', $text);
        return  $text;
    }
}


function createDynamicModel($className, $tableName, $tafseer_name)
{


    // Define the dynamic model class
    $modelCode = "
        namespace App\Models;
        use Illuminate\Database\Eloquent\Model;

        class $className extends Model
        {
            public \$timestamps = false;

            protected \$table = '$tableName';

            public function getModelTypeAttribute()
            {
             return '$tableName';
             }

             protected \$appends = ['model_type','title'];

            public function gettitleAttribute()
            {
               return [
                    'book'=>
                    ['name'=> 'تفسير  -   $tafseer_name']
               ];
           }
           public function quran(){
               return \$this->belongsTo(\App\Models\Quran::class,'verse_id');
             }
        }
    ";
    // Create the dynamic class
    eval($modelCode);

    // Return the new class name
    return "App\\Models\\$className";
}

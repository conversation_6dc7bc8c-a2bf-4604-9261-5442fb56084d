<?php

use App\Http\Middleware\Authenticate;
use App\Http\Middleware\EncryptCookies;
use App\Http\Middleware\VerifyCsrfToken;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Http\Middleware\MirrorConfigToSubpackages;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;

return [
    'default' => 'kholasah',
    'panels' => [
        "kholasah" => [
            'name' => 'الخلاصة العلمية',
            'domain' => env('KHOLASAH_DOMAIN', 'project.kholasah.com'),
            'path' => 'admin',
            'database_connection' => 'mysql',
            'auth' => [
                'guard' => env('FILAMENT_AUTH_GUARD', 'web'),
                'pages' => [
                    'login' => \Filament\Http\Livewire\Auth\Login::class,
                ],
            ],
            'middleware' => [
                'auth' => [
                    Authenticate::class,
                ],
                'base' => [
                    EncryptCookies::class,
                    AddQueuedCookiesToResponse::class,
                    StartSession::class,
                    AuthenticateSession::class,
                    ShareErrorsFromSession::class,
                    VerifyCsrfToken::class,
                    SubstituteBindings::class,
                    DispatchServingFilamentEvent::class,
                    MirrorConfigToSubpackages::class,
                ],
            ],
        ],
        "curriculums" => [
            'name' => 'المناهج التعليمية',
            'domain' => env('CURRICULUMS_DOMAIN', 'curriculums.kholasah.com'),
            'path' => 'admin',
            'database_connection' => 'curriculums',
            'auth' => [
                'guard' => env('FILAMENT_AUTH_GUARD', 'web'),
                'pages' => [
                    'login' => \Filament\Http\Livewire\Auth\Login::class,
                ],
            ],
            'middleware' => [
                'auth' => [
                    Authenticate::class,
                ],
                'base' => [
                    EncryptCookies::class,
                    AddQueuedCookiesToResponse::class,
                    StartSession::class,
                    AuthenticateSession::class,
                    ShareErrorsFromSession::class,
                    VerifyCsrfToken::class,
                    SubstituteBindings::class,
                    DispatchServingFilamentEvent::class,
                    MirrorConfigToSubpackages::class,
                ],
            ],
        ],
        "sahihalsunnah" => [
            'name' => 'صحيح السنة النبوية',
            'domain' => env('SAHIH_ALSUNNAH_DOMAIN', 'sahihalsunnah.kholasah.com'),
            'path' => 'admin',
            'database_connection' => 'sahihalsunnah',
            'auth' => [
                'guard' => env('FILAMENT_AUTH_GUARD', 'web'),
                'pages' => [
                    'login' => \Filament\Http\Livewire\Auth\Login::class,
                ],
            ],
            'middleware' => [
                'auth' => [
                    Authenticate::class,
                ],
                'base' => [
                    EncryptCookies::class,
                    AddQueuedCookiesToResponse::class,
                    StartSession::class,
                    AuthenticateSession::class,
                    ShareErrorsFromSession::class,
                    VerifyCsrfToken::class,
                    SubstituteBindings::class,
                    DispatchServingFilamentEvent::class,
                    MirrorConfigToSubpackages::class,
                ],
            ],
        ],
        "religions" => [
            'name' => 'المناهج التعليمية',
            'domain' => env('RELIGIONS_DOMAIN', 'religions.kholasah.com'),
            'path' => 'admin',
            'database_connection' => 'religions',
            'auth' => [
                'guard' => env('FILAMENT_AUTH_GUARD', 'web'),
                'pages' => [
                    'login' => \Filament\Http\Livewire\Auth\Login::class,
                ],
            ],
            'middleware' => [
                'auth' => [
                    Authenticate::class,
                ],
                'base' => [
                    EncryptCookies::class,
                    AddQueuedCookiesToResponse::class,
                    StartSession::class,
                    AuthenticateSession::class,
                    ShareErrorsFromSession::class,
                    VerifyCsrfToken::class,
                    SubstituteBindings::class,
                    DispatchServingFilamentEvent::class,
                    MirrorConfigToSubpackages::class,
                ],
            ],
        ],
    ]
];
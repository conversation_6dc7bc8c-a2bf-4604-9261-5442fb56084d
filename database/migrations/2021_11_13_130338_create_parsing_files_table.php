<?php

use App\Enums\ParsingStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateParsingFilesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {

        Schema::create('parsing_files', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('book_id');
            $table->string('name');
            $table->bigInteger('admin_id');
            $table->boolean('indexes_extracted')->unsigned()->default(false);
            $table->boolean('indexes_processed')->unsigned()->default(false);
            $table->boolean('status')->unsigned()->default(ParsingStatus::PENDING);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('parsing_files');
    }
}

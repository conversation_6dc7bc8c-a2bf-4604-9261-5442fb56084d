<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMailListSubscriptionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('mail_list_subscriptions', function (Blueprint $table) {
            $table->id();
            // name
            $table->string('name');
            // email
            $table->string('email');
            // country
            $table->string('country');
            // city
            $table->string('city');
            // type
            $table->integer('type');
            // organization name
            $table->string('organization_name')->nullable();
            // student count
            $table->integer('student_count')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('mail_list_subscriptions');
    }
}

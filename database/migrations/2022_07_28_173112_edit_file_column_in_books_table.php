<?php

use App\Models\Files;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('books', function (Blueprint $table) {
            // change the column name and datatype
            $table->dropColumn('file');
            $table->json('files')->nullable();
            $table->string('cover')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('books', function (Blueprint $table) {
            // change the column name and datatype
            $table->dropColumn('files');
            $table->string('file')->nullable();
            $table->dropColumn('cover');
        });
    }
};

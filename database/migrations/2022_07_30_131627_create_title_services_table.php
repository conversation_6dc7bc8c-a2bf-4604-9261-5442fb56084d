<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('title_services', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('title_id');
            $table->tinyInteger('view_type');
            $table->tinyInteger('service_type');
            $table->string('name');
            $table->json('data')->nullable();
            $table->integer('sort')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('title_services');
    }
};

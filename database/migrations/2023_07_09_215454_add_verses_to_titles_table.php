<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('titles', function (Blueprint $table) {
            $table->foreignId("from_verse")->nullable()->constrained("quran", "id");
            $table->foreignId("to_verse")->nullable()->constrained("quran", "id");
            $table->foreignId("sora_num")->nullable()->constrained("quran", "sora_num");
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('titles', function (Blueprint $table) {
            $table->dropForeign('from_verse');
            $table->dropForeign('to_verse');
            $table->dropForeign('sora_num');
        });
    }
};

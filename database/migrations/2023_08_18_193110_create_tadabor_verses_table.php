<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //
        Schema::create('tadabor_verses', function (Blueprint $table) {
     
            $table->integer('tadabor_id');
            $table->foreignId('verse_id')->constrained("quran");
            $table->foreign('tadabor_id')->references("id")->on("tadabor");
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //

        Schema::dropIfExists('zad_almaseer');
    }
};

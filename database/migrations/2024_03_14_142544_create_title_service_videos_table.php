<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        /*
        [
    {
        "url": null,
        "title": "منزلة المقدمة الآجُرومية",
        "detail": "الشيخ ابن عثيمين",
        "video_id": null,
        "thumbnail": "videos/thumbnails/TBra8Uk6ONE9uLDR4dpfhmZNK8ey0D-meta2YXZhtiy2YTYqSDYp9mE2KLYrNix2YjZhdmK2KkuanBn-.jpg",
        "video_file": "videos/VCw9k9WnNWqsyD4auv9jImJSLlFGap-meta2YXZhtiy2YTYqSDYp9mE2KLYrNix2YjZhdmK2KkubXA0-.mp4",
        "video_type": "1",
        "video_preview": null
    },
    {
        "url": null,
        "title": "النصيحة بحفظ متن الآجرومية",
        "detail": "الشيخ ابن عثيمين ",
        "video_id": null,
        "thumbnail": "videos/thumbnails/O0eVnpo0MEZuxEnabXstPX401IZcWU-meta2YXZhtiy2YTYqSDYp9mE2KLYrNix2YjZhdmK2KkuanBn-.jpg",
        "video_file": "videos/yzJAVUKIFOCe6ERqRLQ0I4dcUAOz2U-meta2KfZhNmG2LXZitit2Kkg2KjYrdmB2Lgg2KfZhNii2KzYsdmI2YXZitipLm1wNA==-.mp4",
        "video_type": "1",
        "video_preview": null
    }
]
[
    {
        "url": null,
        "file": [],
        "goal": null,
        "name": null,
        "text": null,
        "type": null,
        "style": null,
        "title": "العذر بالجهل لمن فعل شيئاً من الشرك",
        "answer": null,
        "detail": "الشيخ ابن عثيمين : العذر بالجهل في الشرك الأكبر",
        "needed": null,
        "duration": null,
        "imp_type": null,
        "video_id": "xvgYtguaosI",
        "thumbnail": "https://i.ytimg.com/vi/xvgYtguaosI/maxresdefault.jpg",
        "video_file": [],
        "video_type": "2",
        "description": null,
        "tools_questions": null,
        "feedback_answers": null
    },
    {
        "url": null,
        "file": [],
        "goal": null,
        "name": null,
        "text": null,
        "type": null,
        "style": null,
        "title": "العذر بالجهل لمن فعل شيئاً من الشرك",
        "answer": null,
        "detail": "هل يعذر بالجهل من مات علي الشرك الاكبر ؟ الشيخ صالح الفوزان",
        "needed": null,
        "duration": null,
        "imp_type": null,
        "video_id": "lZVX0c7fsOs",
        "thumbnail": "https://i.ytimg.com/vi/lZVX0c7fsOs/hqdefault.jpg",
        "video_file": [],
        "video_type": "2",
        "description": null,
        "tools_questions": null,
        "feedback_answers": null
    },
    {
        "url": null,
        "file": [],
        "goal": null,
        "name": null,
        "text": null,
        "type": null,
        "style": null,
        "title": "العذر بالجهل لمن فعل شيئاً من الشرك",
        "answer": null,
        "detail": "مسألة العذر بالجهل بالتوحيد - الشيخ ناصر الدين الألباني",
        "needed": null,
        "duration": null,
        "imp_type": null,
        "video_id": "HCI37IPpiuQ",
        "thumbnail": "https://i.ytimg.com/vi/HCI37IPpiuQ/hqdefault.jpg",
        "video_file": [],
        "video_type": "2",
        "description": null,
        "tools_questions": null,
        "feedback_answers": null
    },
    {
        "url": null,
        "file": [],
        "goal": null,
        "name": null,
        "text": null,
        "type": null,
        "style": null,
        "title": "العذر بالجهل لمن فعل شيئاً من الشرك",
        "answer": null,
        "detail": "التفصيل في قضية العذر بالجهل في الشرك الأكبر للشيخ عبد العزيز ابن باز رحمه الله",
        "needed": null,
        "duration": null,
        "imp_type": null,
        "video_id": "ulWAkenjKfE",
        "thumbnail": "https://i.ytimg.com/vi/ulWAkenjKfE/hqdefault.jpg",
        "video_file": [],
        "video_type": "2",
        "description": null,
        "tools_questions": null,
        "feedback_answers": null
    }
]
        */
        Schema::create('title_service_videos', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('service_id')->nullable();
            $table->tinyInteger('video_type');
            $table->text('video_file')->nullable();
            $table->string('video_id')->nullable();
            $table->text('thumbnail')->nullable();
            $table->string('title');
            $table->text('detail');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('title_service_videos');
    }
};

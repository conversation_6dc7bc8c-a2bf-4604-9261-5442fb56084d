<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        /*
         [
    {
        "file": "zqzit50ogz57nzoNJG7vduLYv5a6uQ-metaTm9vci1Cb29rLmNvbSAg2KfZhNil2YrZhdin2YYg2KjYp9mE2YXZhNin2KbZg9ipINmI2KjZitin2YYg2LXZgdin2KrZh9mFIDUgLnBkZg==-.pdf",
        "name": "الإيمان بالملائكة وبيان صفاتهم",
        "description": "<p dir=\"rtl\">الإيمان بالملائكة وبيان صفاتهم لعلي بن نايف الشحود</p>",
    }
]
         */
        Schema::create('title_service_references', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('service_id')->nullable();
            $table->text('name')->nullable();
            $table->longText('description')->nullable();
            $table->text('file')->nullable();
            $table->integer('sort')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('title_service_references');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        /*
        [
    {
        "url": null,
        "file": [],
        "goal": "<p dir=\"rtl\"><strong>يسهم في البحث مع زملائه عن: سبب تقديم الوصية على الدَّين في الحقوق المتعلقة بالتركة الواجب إخراجها قبل الشروع في تقسيم التركة.</strong></p>",
        "name": null,
        "text": null,
        "type": "فردي.",
        "style": "البحث.",
        "title": null,
        "answer": "<p dir=\"rtl\">قيل: المقصود تقديم الأمرين على الميراث، من غير قصد إلى الترتيب بينهما، وقيل: لما كانت الوصية أقل لزومًا من الدَّيْن قُدِّمتْ؛ اهتمامًا بها، وقيل: قدمت لكثرة وقوعها فصارت كالأمر اللازم لكل ميت، وقيل: قدمت لكونها حظَّ المساكين والفقراء، وأُخِّرَ الدَّيْنُ؛ لكونه حظَّ غريم يطلبه بقوة وسلطان، وقيل: لما كانت الوصية ناشئة من جهة الميت قدمت، بخلاف الدين فإنه ثابت مؤدى، ذُكِرَ أو لم يُذْكَرْ، وقيل: قدمت لكونها تشبه الميراث في كونها مأخوذة من غير عوض، فربما يشق على الورثة إخراجها، بخلاف الدَّيْن فإن نفوسهم مطمئنة بأدائه.&nbsp;</p>",
        "detail": null,
        "needed": "<p dir=\"rtl\">اذكر الأقوال التي أوردها الإمام الشوكاني في توجيه تقديم الوصية على الدَّيْن في (الآية رقم 11) من سورة النساء، مع أن قضاء الدَّيْن مقدم على الوصية، بالإجماع.&nbsp;</p>",
        "duration": null,
        "imp_type": "التوجيه إلى الرجوع إلى كتاب (فتح القدير للشوكاني) في المكتبات، أو من خلال الشبكة العنكبوتية، مع تحديد موعد لاستلام الإجابات.",
        "video_id": null,
        "thumbnail": [],
        "video_file": [],
        "video_type": null,
        "description": null,
        "video_preview": null,
        "tools_questions": null,
        "feedback_answers": null
    },
    {
        "url": null,
        "file": [],
        "goal": "<p dir=\"rtl\"><strong>يحدد أصحاب الفروض المقدرة في كتاب الله تعالى.</strong>&nbsp;</p>",
        "name": null,
        "text": null,
        "type": "مجموعات.",
        "style": "تحليل النص.",
        "title": null,
        "answer": "<p dir=\"rtl\">&nbsp;| <strong>الورثة </strong>&nbsp;| <strong>الآية</strong><br> | <strong>إرث الزوجين</strong> | ﴿ﱂ ﱃ ﱄ ﱅ ﱆ ﱇ ﱈ ﱉ ﱊ ﱋﱌ ﱍ ﱎ ﱏ ﱐ ﱑ ﱒ ﱓ ﱔﱕ ﱖ ﱗ ﱘ ﱙ ﱚ ﱛ ﱜﱝ ﱞ ﱟ ﱠ ﱡ ﱢ ﱣ ﱤ ﱥ ﱦﱧ ﱨ ﱩ ﱪ ﱫ ﱬ ﱭ ﱮ ﱯﱰ ﱱ ﱲ ﱳ ﱴ ﱵ ﱶ ﱷﱸ﴾.<br> | <strong>إرث الأصول</strong> | ﴿ﲝ ﲞ ﲟ ﲠ ﲡ ﲢ ﲣ ﲤ ﲥ ﲦ ﲧﲨ ﲩ ﲪ ﲫ ﲬ ﲭ ﲮ ﲯ ﲰ ﲱﲲ ﲳ ﲴ ﲵ ﲶ ﲷ ﲸﲹ﴾.<br> | <strong>إرث الفروع</strong> <strong>&nbsp; </strong>&nbsp;| ﴿ﲈ ﲉ ﲊ ﲋﲌ ﲍ ﲎ ﲏ ﲐ ﲑ ﲒ ﲓ ﲔ ﲕﲖ ﲗ ﲘ ﲙ ﲚ ﲛﲜ﴾.<br>&nbsp;| <strong>إرث الحواشي</strong> | ﴿ﱹ ﱺ ﱻ ﱼ ﱽ ﱾ ﱿ ﲀ ﲁ ﲂ ﲃ ﲄ ﲅ ﲆ ﲇﲈ ﲉ ﲊ ﲋ ﲌ ﲍ ﲎ ﲏ ﲐ ﲑﲒ﴾. مع قوله تعالى: ﴿ﱁ ﱂ ﱃ ﱄ ﱅ ﱆﱇ ﱈ ﱉ ﱊ ﱋ ﱌ ﱍ ﱎ ﱏ ﱐ ﱑ ﱒ ﱓﱔ ﱕ ﱖ ﱗ ﱘ ﱙ ﱚ ﱛﱜ ﱝ ﱞ ﱟ ﱠ ﱡ ﱢ ﱣﱤ ﱥ ﱦ ﱧ ﱨ ﱩ ﱪ ﱫ ﱬ ﱭﱮ﴾.</p>",
        "detail": null,
        "needed": "<p dir=\"rtl\"><strong>بالتعاون مع أفراد مجموعتك، حدد من آيات المواريث الثلاث (11 - 12 - 176) من سورة النساء ما يخص كُلًّا من إرث الزوجين، وإرث الأصول، وإرث الفروع، وإرث الحواشي.</strong></p><p dir=\"rtl\"><strong>قبل البدء، يوضح المعلم للطلاب المصطلحات المهمة هنا مثل: (الأصول، الفروع، الحواشي).</strong>&nbsp;</p>",
        "duration": null,
        "imp_type": "كل مجموعة تحضر مصحفًا وورقةً مُجَدْوَلَةً بالمطلوب؛ لتحديد ما يخص الورثة في الآيات الثلاث.",
        "video_id": null,
        "thumbnail": [],
        "video_file": [],
        "video_type": null,
        "description": null,
        "video_preview": null,
        "tools_questions": null,
        "feedback_answers": null
    },
    {
        "goal": "<p dir=\"rtl\">يستدل لكون اختلاف الدِّين مانعًا للإرث.&nbsp;</p>",
        "type": "فردي.",
        "style": "الاستدلال.",
        "answer": "<p dir=\"rtl\">قول الرسول صلى الله عليه وسلم في الحديث الصحيح المتفق عليه: (لا يرث المسلم الكافر، ولا الكافر المسلم).</p>",
        "needed": "<p dir=\"rtl\"><strong>أورد المؤلف تعليلًا لمانع الرِّقِّ، ودليلًا لمانع القتل، فابحث عن دليل يجعل اختلاف الدين مانعًا للإرث.</strong>&nbsp;</p>",
        "imp_type": "البحث في (صحيح البخاري) عبر الشبكة العنكبوتية.",

    }
]
        */
        Schema::create('title_service_activities', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('service_id')->nullable();
            $table->string('type')->nullable();
            $table->longText('goal')->nullable();
            $table->string('style')->nullable();
            $table->longText('answer')->nullable();
            $table->longText('needed')->nullable();
            $table->text('imp_type')->nullable();
            $table->integer('sort')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('title_service_activities');
    }
};

<?php

use App\Facades\Panel;
use App\Models\Role;
use App\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public $tableNames = [
        "books",
        'titles',
        'contents',
        'parsing_files',
        'parsing_files_errors',
        'titles_hrefs',
        'title_services',
        'service_sub_types',
        'activity_log',
        'title_service_activities',
        'title_service_calenders',
        'title_service_quotations',
        'title_service_references',
        'title_service_texts',
        'title_service_videos',
        'roles',
        'permissions',
        'users',
        'model_has_permissions',
        'model_has_roles',
        'role_has_permissions',
        'quran',
        'quran_othmany_qcf2',
        'quran_othmany_svg',
        'quran_othmany_v1',
        'quran_suar',
        'quran_text',
    ];
    public $tableNamesToCopy = [
        'quran',
        'quran_othmany_qcf2',
        'quran_othmany_svg',
        'quran_othmany_v1',
        'quran_suar',
        'quran_text',
    ];
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        foreach (config('panels.panels') as $id => $panel) {
            if (config('panels.default') == $id) continue;
            $db_connection = $panel['database_connection'];
            foreach ($this->tableNames as $tableName) {
                $sql = "CREATE TABLE IF NOT EXISTS ";
                $sql .= config('database.connections.' . $db_connection . '.database') . '.' . $tableName . ' LIKE ' . config('database.connections.mysql.database') . '.' . $tableName . ';';
                DB::statement($sql);
            }
            foreach ($this->tableNamesToCopy as $tableName) {
                $sql = "Insert Into ";
                $sql .= config('database.connections.' . $db_connection . '.database') . '.' . $tableName . ' SELECT * FROM ' . config('database.connections.mysql.database') . '.' . $tableName . ';';
                DB::statement($sql);
            }
            Panel::setPanel($id);
            $user =   User::updateOrCreate([
                "id" => 1,
            ], [
                'name' => 'Admin',
                'email' => '<EMAIL>',
                "is_admin" => true,
                "is_active" => true,
                "password" => Hash::make('25002500'),
            ],);
            $user2 =   User::updateOrCreate([
                "id" => 2,
            ], [
                'name' => 'Admin',
                'email' => '<EMAIL>',
                "is_admin" => true,
                "is_active" => true,
                "password" => Hash::make('123456'),
            ],);
            Role::updateOrCreate([
                "id" => 1,
            ], [
                "name" => "super_admin",
                "guard_name" => "web",
            ]);
            $user->assignRole('super_admin');
            $user2->assignRole('super_admin');
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        foreach (config('panels.panels') as $id => $panel) {
            if (config('panels.default') == $id) continue;
            $db_connection = $panel['database_connection'];
            foreach ($this->tableNames as $tableName) {
                $sql = "DROP TABLE IF EXISTS ";
                $sql .= config('database.connections.'.$db_connection.'.database') . '.' . $tableName . ';';
                DB::statement($sql);
            }
        }
    }
};

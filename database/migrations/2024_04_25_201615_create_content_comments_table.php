<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {

        foreach (config('panels.panels') as $id => $panel) {
            Schema::connection($panel['database_connection'])->create('content_comments', function (Blueprint $table) {

                $table->id();
                $table->text('comment');
                $table->tinyInteger('read')->default(0);
                $table->foreignId('content_id')->constrained('contents');

                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('content_comments');
    }
};

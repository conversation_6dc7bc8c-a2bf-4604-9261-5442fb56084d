<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        foreach (config('panels.panels') as $id => $panel) {
            Schema::connection($panel['database_connection'])->table('content_rates', function (Blueprint $table) {
                //
                $table->dropConstrainedForeignId('content_id');
                $table->morphs('ratable');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('content_rates', function (Blueprint $table) {
            //

            $table->dropMorphs('content_id');
        });
    }
};

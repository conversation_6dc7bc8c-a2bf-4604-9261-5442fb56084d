<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('sale_points', function (Blueprint $table) {
            //

            $table->string('place')->nullable()->change();
            $table->string('phone_number')->nullable()->change();
            $table->longtext('address')->nullable()->change();

            $table->string('map_url')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('sale_points', function (Blueprint $table) {
            //
            $table->string('place')->change();
            $table->string('phone_number')->change();
            $table->longtext('address')->change();

            $table->string('map_url')->change();
        });
    }
};

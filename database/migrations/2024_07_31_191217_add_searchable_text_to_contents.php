<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        foreach (config('panels.panels') as $id => $panel) {

            Schema::connection($panel['database_connection'])->table('contents', function (Blueprint $table) {
                //
                $table->longText('searchable_text')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        foreach (config('panels.panels') as $id => $panel) {
            Schema::connection($panel['database_connection'])->table('contents', function (Blueprint $table) {
                $table->dropColumn('searchable_text')->nullable();
            });
        }
    }
};

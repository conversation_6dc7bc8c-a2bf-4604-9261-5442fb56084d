<?php

use App\Models\TitleServiceActivity;
use App\Models\TitleServiceCalender;
use App\Models\TitleServiceQuotation;
use App\Models\TitleServiceReference;
use App\Models\TitleServiceText;
use App\Models\TitleServiceVideo;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;

return new class extends Migration
{
    public   $tafseers = [
        "ibn_katheer",
        "baghawy",
        "saady",
        "qortoby",
        "tabary",
        "tanweer",
        "zad_almaseer",
        "ibn_alqayyem",
        'ghareeb_ibn_qutaybah',
        'aljadwal',
        'iraab_aldarweesh',
        'iraab_alnahas',
        'nathm_aldurar',
        'qiraat_almawsoah',
        'siraaj_ghareeb',
        'tahbeer_altayseer',
        'tadabor'
    ];

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        foreach ($this->tafseers as $value) {
            Schema::table(
                $value,
                function (Blueprint $table) use ($value) {
                    if (!Schema::hasColumn($value, 'searchable_text')) {
                        $table->longText('searchable_text')->nullable();
                    }
                }
            );
        }
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        foreach ($this->tafseers as $value) {
            Schema::table(
                $value,
                function (Blueprint $table) {
                    $table->dropColumn('searchable_text');
                }
            );
        }
    }
};

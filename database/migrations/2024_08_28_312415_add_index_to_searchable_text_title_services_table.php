<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\TitleServiceActivity;
use App\Models\TitleServiceCalender;
use App\Models\TitleServiceQuotation;
use App\Models\TitleServiceReference;
use App\Models\TitleServiceText;
use App\Models\TitleServiceVideo;
use Illuminate\Support\Str;

return new class extends Migration
{

    public  $titleServices = [
        TitleServiceVideo::class,
        TitleServiceText::class,
        TitleServiceQuotation::class,
        TitleServiceReference::class,
        TitleServiceActivity::class,
        TitleServiceCalender::class,
    ];

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
       

        foreach (config('panels.panels') as $id => $panel) {

            foreach ($this->titleServices as $value) {
                Schema::connection($panel['database_connection'])->table(
                    Str::snake(Str::pluralStudly(class_basename($value))),
                    function (Blueprint $table) {
                        $table->longText('searchable_text')->nullable()->fulltext()->change();
                    }
                );
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        foreach (config('panels.panels') as $id => $panel) {

            foreach ($this->titleServices as $value) {
                Schema::connection($panel['database_connection'])->table(
                    Str::snake(Str::pluralStudly(class_basename($value))),
                    function (Blueprint $table) {
                        $table->longText('searchable_text')->nullable()->change();
                    }
                );
            }
        }
    }
};

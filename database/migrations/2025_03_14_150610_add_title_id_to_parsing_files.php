<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {

        foreach (config('panels.panels') as $id => $panel) {

            Schema::connection($panel['database_connection'])->table('parsing_files', function (Blueprint $table) {
                //
                $table->foreignId('title_id')->nullable()->constrained('titles')->onDelete('set null');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {

        foreach (config('panels.panels') as $id => $panel) {

            Schema::connection($panel['database_connection'])->table('parsing_files', function (Blueprint $table) {
                //
                $table->dropForeign(['title_id']);
                $table->dropColumn('title_id');
            });
        }
    }
};

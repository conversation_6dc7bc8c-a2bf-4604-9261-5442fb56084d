<?php

namespace Alareqi\FilamentActivityLog;

use Illuminate\Auth\Events\Login;
use Illuminate\Auth\Events\Logout;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Notifications\Events\NotificationFailed;
use Illuminate\Notifications\Events\NotificationSent;

class FilamentActivityLogEventServiceProvider extends ServiceProvider
{
    public function listens()
    {
        $listen = array_merge(
            config('filament-activity-log.access.enabled') ? [
                Login::class => [
                    config('filament-activity-log.access.logger'),
                ],
                Logout::class => [
                    config('filament-activity-log.access.logger'),
                ],
            ] : [],
            config('filament-activity-log.notifications.enabled') ? [
                NotificationSent::class => [
                    config('filament-activity-log.notifications.logger'),
                ],
                NotificationFailed::class => [
                    config('filament-activity-log.notifications.logger'),
                ],
            ] : []
        );
        return $listen;
    }
}

<?php

namespace Alareqi\FilamentActivityLog;

use Filament\Facades\Filament;
use Filament\PluginServiceProvider;
use Illuminate\Support\Facades\Gate;

class FilamentActivityLogServiceProvider extends PluginServiceProvider
{

    public static string $name = 'filament-activity-log';

    protected function getResources(): array
    {
        return [
            config('filament-activity-log.activity_resource')
        ];
    }
    public function bootingPackage()
    {
        Gate::policy('Spatie\Activitylog\Models\Activity', 'Alareqi\FilamentActivityLog\Policies\ActivityPolicy');
    }
    public function registeringPackage(): void
    {
        $this->app->register(FilamentActivityLogEventServiceProvider::class);
    }

    public function packageBooted(): void
    {
        parent::packageBooted();

        if (config('filament-activity-log.resources.enabled', true)) {
            $exceptResources = [...config('filament-activity-log.resources.exclude'), config('filament-activity-log.activity_resource')];
            $removedExcludedResources = collect(Filament::getResources())->filter(function ($resource) use ($exceptResources) {
                return !in_array($resource, $exceptResources);
            });

            foreach ($removedExcludedResources as $resource) {
                $resource::getModel()::observe(config('filament-activity-log.resources.logger'));
            }
        }

        if (config('filament-activity-log.models.enabled', true) && !empty(config('filament-activity-log.models.register'))) {
            foreach (config('filament-activity-log.models.register', []) as $model) {
                $model::observe(config('filament-activity-log.models.logger'));
            }
        }
    }
}

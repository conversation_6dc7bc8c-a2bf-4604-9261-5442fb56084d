<?php

namespace Alareqi\FilamentActivityLog\Loggers;

use Filament\Facades\Filament;
use Illuminate\Auth\GenericUser;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use Spatie\Activitylog\ActivityLogger;
use Spatie\Activitylog\ActivityLogStatus;

abstract class AbstractModelLogger
{
    protected abstract function getLogName(): string;

    protected function getUserName(?Authenticatable $user): string
    {
        if (blank($user) || $user instanceof GenericUser) {
            return 'Anonymous';
        }

        return Filament::getUserName($user);
    }

    protected function getModelName(Model $model)
    {
        return Str::of(class_basename($model))->lower();
    }


    protected function activityLogger(string $logName = null): ActivityLogger
    {
        $defaultLogName = $this->getLogName();

        $logStatus = app(ActivityLogStatus::class);

        return app(ActivityLogger::class)
            ->useLog($logName ?? $defaultLogName)
            ->setLogStatus($logStatus);
    }

    protected function log(Model $model, string $event, ?string $description = null, mixed $attributes = null)
    {
        if (is_null($description)) {
            $description = "تم " . __('filament-activity-log::filament-activity-log.events.' . Str::of($event)->lower()) . ' ' . $this->getModelName($model) . ' ';
        }

        if (auth()->check()) {
            $description .= ' بواسطة ' . $this->getUserName(auth()->user());
        }

        $this->activityLogger()
            ->event($event)
            ->performedOn($model)
            ->withProperties($attributes)
            ->log($description);
    }

    public function created(Model $model)
    {
        $this->log($model, 'Created', attributes: $model->getAttributes());
    }

    public function updated(Model $model)
    {
        $changes = $model->getChanges();

        //Ignore the changes to remember_token
        if (count($changes) === 1 && array_key_exists('remember_token', $changes)) {
            return;
        }
        if (count($changes) === 2 && (array_key_exists('deleted_at', $changes) && array_key_exists('updated_at', $changes))) {
            $this->log($model, 'Restored', attributes: $changes);
        } else {
            $this->log($model, 'Updated', attributes: $changes);
        }
    }

    public function deleted(Model $model)
    {
        $this->log($model, 'Deleted');
    }
}

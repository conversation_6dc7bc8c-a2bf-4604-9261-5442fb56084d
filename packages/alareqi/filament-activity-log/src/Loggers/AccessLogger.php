<?php

namespace Alareqi\FilamentActivityLog\Loggers;

use Filament\Facades\Filament;
use Illuminate\Auth\Events\Login;
use Illuminate\Auth\Events\Logout;
use Spatie\Activitylog\ActivityLogger;
use Spatie\Activitylog\ActivityLogStatus;
use Illuminate\Support\Str;

class AccessLogger
{
    /**
     * Log user login
     *
     * @param  Login  $event
     * @return void
     */
    public function handle(Login|Logout $event)
    {
        if ($event instanceof Login) {
            $description = "قام " . Filament::getUserName($event->user) . ' بتسجيل الدخول';
        } else {
            $description = "قام " . Filament::getUserName($event->user) . ' بتسجيل الخروج';
        }
        $description = "قام " . Filament::getUserName($event->user) . ' بتسجيل الدخول';

        app(ActivityLogger::class)
            ->useLog(__(config('filament-activity-log.access.log_name')))
            ->setLogStatus(app(ActivityLogStatus::class))
            ->withProperties(['ip' => request()->ip(), 'user_agent' => request()->userAgent()])
            ->event(Str::of(class_basename($event))->headline())
            ->log($description);
    }
}

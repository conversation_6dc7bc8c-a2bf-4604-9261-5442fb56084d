<?php

namespace Alareqi\FilamentActivityLog\Resources;

use Filament\Facades\Filament;
use Filament\Forms\Components\Card;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\KeyValue;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use Spatie\Activitylog\Models\Activity;
use Alareqi\FilamentActivityLog\Resources\ActivityResource\Pages;
use App\Models\User;
use App\Traits\MultiPanelResourse;
use Illuminate\Support\Carbon;

class ActivityResource extends Resource
{
    use MultiPanelResourse;
    protected static ?string $model = Activity::class;
    protected static ?string $slug = 'activity-logs';

    protected static ?string $navigationIcon = 'heroicon-o-clipboard-list';

    protected static function getNavigationGroup(): ?string
    {
        return __(config('filament-activity-log.navigation_group', true));
    }

    public static function getModelLabel(): string
    {
        return __('filament-activity-log::filament-activity-log.singular_label');
    }
    public static function getPluralModelLabel(): string
    {
        return __('filament-activity-log::filament-activity-log.plural_label');
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Group::make([
                    Card::make([
                        TextInput::make('causer_id')
                            ->afterStateHydrated(fn ($component, ?Model $record) => $component->state($record->causer?->name))
                            ->label(__('filament-activity-log::filament-activity-log.fields.user.label')),
                        TextInput::make('subject_type')
                            ->afterStateHydrated(
                                fn ($component, ?Model $record, $state) =>
                                $state ? $component->state(Str::of($state)->afterLast('\\')->snake()->plural()) : '-'
                            )
                            ->label(__('filament-activity-log::filament-activity-log.fields.subject.label')),
                        TextInput::make('subject_id')
                            ->afterStateHydrated(
                                fn ($component, ?Model $record, $state) =>
                                $state ?? '-'
                            )
                            ->label(__('filament-activity-log::filament-activity-log.fields.subject_id.label')),

                        Textarea::make('description')
                            ->label(__('filament-activity-log::filament-activity-log.fields.description.label'))
                            ->rows(2)
                            ->columnSpan([
                                "default" => 1,
                                "xl" => 3,
                            ]),
                    ])
                        ->columns(),
                ])
                    ->columnSpan(['sm' => 3]),

                Group::make([
                    Card::make([
                        Placeholder::make('log_name')
                            ->content(
                                fn (?Model $record): string => $record?->log_name
                                    ? ucwords($record?->log_name)
                                    : '-'
                            )
                            ->label(__('filament-activity-log::filament-activity-log.fields.type.label')),

                        Placeholder::make('event')
                            ->content(
                                fn (?Model $record): string => $record?->event
                                    ? __('filament-activity-log::filament-activity-log.events.' . Str::lower($record?->event))
                                    : '-'
                            )
                            ->label(__('filament-activity-log::filament-activity-log.fields.event.label')),

                        Placeholder::make('created_at')
                            ->label(__('filament-activity-log::filament-activity-log.fields.logged_at.label'))
                            ->content(
                                fn (?Model $record): string => $record?->created_at
                                    ? $record?->created_at->diffForHumans()
                                    : '-'
                            ),
                    ])
                ]),
                Card::make([
                    KeyValue::make('properties')
                        ->label(__('filament-activity-log::filament-activity-log.fields.properties.label')),
                ])
                    ->visible(fn ($record) => $record->properties?->count() > 0)
            ])
            ->columns(['sm' => 4, 'lg' => null]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('id', 'desc')
            ->columns([
                TextColumn::make('id')
                    ->sortable()
                    ->label(__('filament-activity-log::filament-activity-log.fields.id.label')),
                BadgeColumn::make('log_name')
                    ->colors(static::getLogNameColors())
                    ->label(__('filament-activity-log::filament-activity-log.fields.type.label'))
                    ->formatStateUsing(fn ($state) => ucwords($state))
                    ->sortable(),

                TextColumn::make('event')
                    ->label(__('filament-activity-log::filament-activity-log.fields.event.label'))
                    ->formatStateUsing(function ($state, Model $record) {
                        $event = Str::of($state)->lower();
                        return __("filament-activity-log::filament-activity-log.events.{$event}");
                    })
                    ->sortable(),

                TextColumn::make('description')
                    ->label(__('filament-activity-log::filament-activity-log.fields.description.label'))
                    ->toggleable()
                    ->toggledHiddenByDefault()
                    ->wrap(),

                TextColumn::make('subject_type')
                    ->label(__('filament-activity-log::filament-activity-log.fields.subject.label'))
                    ->formatStateUsing(function ($state, Model $record) {
                        if (!$state) {
                            return '-';
                        }
                        return Str::of($state)->afterLast('\\')->snake()->plural();
                    }),
                TextColumn::make('subject_id')
                    ->label(__('filament-activity-log::filament-activity-log.fields.subject_id.label'))
                    ->formatStateUsing(function ($state, Model $record) {
                        return $state ?? '-';
                    }),

                TextColumn::make('causer.name')
                    ->label(__('filament-activity-log::filament-activity-log.fields.user.label')),

                TextColumn::make('created_at')
                    ->label(__('filament-activity-log::filament-activity-log.fields.logged_at.label'))
                    ->dateTime('Y-m-d H:i A')
                    ->sortable(),
            ])
            ->bulkActions([])
            ->filters([
                SelectFilter::make('log_name')
                    ->label(__('filament-activity-log::filament-activity-log.fields.type.label'))
                    ->options(static::getLogNameList()),
                SelectFilter::make('subject_type')
                    ->label(__('filament-activity-log::filament-activity-log.fields.subject.label'))
                    ->options(static::getSubjectTypeList()),
                SelectFilter::make('causer_id')
                    ->label(__('filament-activity-log::filament-activity-log.fields.user.label'))
                    ->options(User::all()->pluck('name', 'id')),
                Filter::make('created_at')
                    ->form([
                        DatePicker::make('logged_at')
                            ->label(__('filament-activity-log::filament-activity-log.fields.logged_at.label'))
                            ->displayFormat('d/m/Y'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['logged_at'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', $date),
                            );
                    }),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListActivities::route('/'),
            'view' => Pages\ViewActivity::route('/{record}'),
        ];
    }

    protected static function getSubjectTypeList(): array
    {
        if (config('filament-activity-log.resources.enabled', true)) {
            $subjects = [];
            $exceptResources = [...config('filament-activity-log.resources.exclude'), config('filament-activity-log.activity_resource')];
            $removedExcludedResources = collect(Filament::getResources())->filter(function ($resource) use ($exceptResources) {
                return !in_array($resource, $exceptResources);
            });
            foreach ($removedExcludedResources as $resource) {
                $model = $resource::getModel();
                $subjects[$model] =  Str::of($model)->afterLast('\\')->snake()->plural();
            }
            return $subjects;
        }
        return [];
    }

    protected static function getLogNameList(): array
    {
        $customs = [];

        foreach (config('filament-activity-log.custom') ?? [] as $custom) {
            $customs[$custom['log_name']] = $custom['log_name'];
        }

        return array_merge(
            config('filament-activity-log.resources.enabled') ? [
                config('filament-activity-log.resources.log_name') => config('filament-activity-log.resources.log_name'),
            ] : [],
            config('filament-activity-log.models.enabled') ? [
                config('filament-activity-log.models.log_name') => config('filament-activity-log.models.log_name'),
            ] : [],
            config('filament-activity-log.access.enabled')
                ? [config('filament-activity-log.access.log_name') => config('filament-activity-log.access.log_name')]
                : [],
            config('filament-activity-log.notifications.enabled') ? [
                config('filament-activity-log.notifications.log_name') => config('filament-activity-log.notifications.log_name'),
            ] : [],
            $customs,
        );
    }

    protected static function getLogNameColors(): array
    {
        $customs = [];

        foreach (config('filament-activity-log.custom') ?? [] as $custom) {
            if (filled($custom['color'] ?? null)) {
                $customs[$custom['color']] = $custom['log_name'];
            }
        }

        return array_merge(
            (config('filament-activity-log.resources.enabled') && config('filament-activity-log.resources.color')) ? [
                config('filament-activity-log.resources.color') => __(config('filament-activity-log.resources.log_name')),
            ] : [],
            (config('filament-activity-log.models.enabled') && config('filament-activity-log.models.color')) ? [
                config('filament-activity-log.models.color') => __(config('filament-activity-log.models.log_name')),
            ] : [],
            (config('filament-activity-log.access.enabled') && config('filament-activity-log.access.color')) ? [
                config('filament-activity-log.access.color') => __(config('filament-activity-log.access.log_name')),
            ] : [],
            (config('filament-activity-log.notifications.enabled') &&  config('filament-activity-log.notifications.color')) ? [
                config('filament-activity-log.notifications.color') => __(config('filament-activity-log.notifications.log_name')),
            ] : [],
            $customs,
        );
    }
    public static $permissions = [
        'view',
        'view_any',
        // 'create',
        // 'update',
        // 'restore',
        // 'restore_any',
        // 'replicate',
        // 'reorder',
        // 'delete',
        // 'delete_any',
        // 'force_delete',
        // 'force_delete_any',
    ];
}

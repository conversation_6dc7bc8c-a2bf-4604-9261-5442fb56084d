<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('email_verified_at');
            $table->boolean("is_admin")->default(false);
            $table->boolean("is_active")->default(false);
            $table->string("avatar_url")->nullable();
            $table->dropIndex('users_email_unique');
            $table->unique(['email', "is_admin"]);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex("users_email_is_admin_unique");
            $table->timestamp('email_verified_at')->nullable();
            $table->dropColumn("is_admin");
            $table->dropColumn("avatar_url");
            $table->unique('email');
        });
    }
};

<?php

namespace <PERSON>areqi\FilamentAdminsRoles;

use Alareqi\FilamentAdminsRoles\Http\Livewire\Auth\Login;
use Alareqi\FilamentAdminsRoles\Pages\Profile;
use Alareqi\FilamentAdminsRoles\Resources\AdminResource;
use Alareqi\FilamentAdminsRoles\Resources\RoleResource;
use Alareqi\FilamentAdminsRoles\Http\Middleware\UserActive;
use App\Facades\Panel;
use App\Http\Middleware\ApplyPanel;
use Filament\Facades\Filament;
use Filament\Navigation\UserMenuItem;
use Filament\PluginServiceProvider;
use Illuminate\Support\Facades\Gate;
use Spatie\LaravelPackageTools\Package;
use Livewire\Livewire;
use Spatie\Activitylog\Models\Activity;
use Spatie\Activitylog\Policies\ActivityPolicy;

class FilamentAdminsRolesServiceProvider extends PluginServiceProvider
{
    protected array $resources = [
        RoleResource::class,
        AdminResource::class,
    ];

    protected array $pages = [
        Profile::class,
    ];

    public function configurePackage(Package $package): void
    {
        $package
            ->name('filament-admins-roles')
            ->hasMigrations(["edit_users_table"])
            ->runsMigrations()
            ->hasConfigFile()
            ->hasViews()
            ->hasRoute('web')
            ->hasCommands($this->getCommands())
            ->hasTranslations();
    }
    public function bootingPackage()
    {
        Livewire::component(Login::getName(), Login::class);
        Livewire::addPersistentMiddleware([
            ApplyPanel::class,
        ]);

        Filament::serving(function () {
          $panel =  Panel::getPanelIdByDomain(request()->host());
          Panel::setPanel($panel);
            Filament::registerUserMenuItems([
                'account' => UserMenuItem::make()->url(route(Panel::currentPanel().'.pages.profile')),
                'logout' => UserMenuItem::make()->url(route(Panel::currentPanel().'.auth.logout')),
                // "change_password" => UserMenuItem::make()
                //     ->label(__('filament-admins-roles::common.menu.change_password'))
                //     ->icon('heroicon-o-key'),
            ]);
        });
        Gate::before(function ($user, $ability) {
            return $user->hasRole('super_admin') ? true : null;
        });
        Gate::policy('Spatie\Permission\Models\Role', 'Alareqi\FilamentAdminsRoles\Policies\RolePolicy');
        app('router')->aliasMiddleware('user_active', UserActive::class);
    }
    protected function getCommands(): array
    {
        return [
            Commands\MakeShieldDoctorCommand::class,
            Commands\MakeShieldUpgradeCommand::class,
            Commands\MakeShieldInstallCommand::class,
            Commands\MakeShieldGenerateCommand::class,
            Commands\MakeShieldSuperAdminCommand::class,
        ];
    }
}

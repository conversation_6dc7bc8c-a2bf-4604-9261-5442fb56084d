<?php

namespace  Alareqi\FilamentAdminsRoles\Http\Livewire\Auth;

use Filament\Http\Livewire\Auth\Login as BaseLoginPage;
use Yepsua\Filament\Forms\Components\Captcha;
use <PERSON><PERSON><PERSON>rin\LivewireRateLimiting\Exceptions\TooManyRequestsException;
use Dan<PERSON><PERSON>rin\LivewireRateLimiting\WithRateLimiting;
use Filament\Facades\Filament;
use Filament\Http\Responses\Auth\Contracts\LoginResponse;
use Illuminate\Validation\ValidationException;

class Login extends BaseLoginPage
{

    protected function getFormSchema(): array
    {
        $formSchema = parent::getFormSchema();
        $formSchema[3] = $formSchema[2];
        $formSchema[2] = Captcha::make('captcha')
            ->label(__("filament-admins-roles::user.fields.captcha.label"));
        return $formSchema;
    }

    public function authenticate(): ?LoginResponse
    {
        try {
            $this->rateLimit(5);
        } catch (TooManyRequestsException $exception) {
            throw ValidationException::withMessages([
                'email' => __('filament::login.messages.throttled', [
                    'seconds' => $exception->secondsUntilAvailable,
                    'minutes' => ceil($exception->secondsUntilAvailable / 60),
                ]),
            ]);
        }

        $data = $this->form->getState();

        if (!Filament::auth()->attempt([
            'email' => $data['email'],
            'password' => $data['password'],
            "is_active" => 1,
            "is_admin" => 1,
        ], $data['remember'])) {
            throw ValidationException::withMessages([
                'email' => __('filament::login.messages.failed'),
            ]);
        }

        return app(LoginResponse::class);
    }
}

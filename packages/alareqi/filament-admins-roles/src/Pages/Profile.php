<?php

namespace Alareqi\FilamentAdminsRoles\Pages;

use Alareqi\FilamentAdminsRoles\Resources\AdminResource;
use App\Traits\MultiPanelPage;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Pages\Page;
use Illuminate\Support\Facades\Hash;

class Profile extends Page implements HasForms
{
    use MultiPanelPage;

    protected static string $resource = AdminResource::class;

    use InteractsWithForms;

    protected static ?string $slug = 'profile';

    protected static ?string $navigationIcon = 'heroicon-o-user';

    protected static ?string $navigationGroup = 'Account';

    protected static string $view = 'filament-admins-roles::filament.pages.profile';

    public $name;

    public $email;

    public $current_password;

    public $new_password;

    public $new_password_confirmation;

    protected function getTitle(): string
    {
        return __('filament-admins-roles::user.profile');
    }

    public function mount()
    {
        $this->form->fill([
            'name' => auth()->user()->name,
            'email' => auth()->user()->email,
        ]);
    }

    public function submit()
    {
        $this->form->getState();

        $state = array_filter([
            'name' => $this->name,
            'email' => $this->email,
            'password' => $this->new_password ? Hash::make($this->new_password) : null,
        ]);

        auth()->user()->update($state);

        $this->reset(['current_password', 'new_password', 'new_password_confirmation']);
        $this->notify('success', __('filament-admins-roles::user.messages.updated'));
    }

    public function getCancelButtonUrlProperty()
    {
        return static::getUrl();
    }

    protected function getBreadcrumbs(): array
    {
        return [
            url()->current() => __('filament-admins-roles::user.profile'),
        ];
    }
    protected static function shouldRegisterNavigation(): bool
    {
        return false;
    }
    protected function getFormSchema(): array
    {
        return [
            Section::make(__('filament-admins-roles::user.groups.profile'))
                ->columns(2)
                ->schema([
                    TextInput::make('name')
                        ->label(__('filament-admins-roles::user.fields.name.label'))
                        ->required(),
                    TextInput::make('email')
                        ->label(__('filament-admins-roles::user.fields.email.label'))
                        ->required(),
                ]),
            Section::make(__('filament-admins-roles::user.groups.change_password'))
                ->columns(2)
                ->schema([
                    TextInput::make('current_password')
                        ->label(__('filament-admins-roles::user.fields.current_password.label'))
                        ->password()
                        ->rules(['required_with:new_password'])
                        ->currentPassword()
                        ->autocomplete('off')
                        ->columnSpan(1),
                    Grid::make()
                        ->schema([
                            TextInput::make('new_password')
                                ->label(__('filament-admins-roles::user.fields.new_password.label'))
                                ->password()
                                ->rules(['confirmed'])
                                ->autocomplete('new-password'),
                            TextInput::make('new_password_confirmation')
                                ->label(__('filament-admins-roles::user.fields.new_password_confirmation.label'))
                                ->password()
                                ->rules([
                                    'required_with:new_password',
                                ])
                                ->autocomplete('new-password'),
                        ]),
                ]),
        ];
    }
}

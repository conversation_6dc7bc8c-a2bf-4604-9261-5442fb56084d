<?php

namespace Alareqi\FilamentAdminsRoles\Resources;

use Alareqi\FilamentAdminsRoles\Resources\AdminResource\Pages;
use Alareqi\FilamentAdminsRoles\Resources\AdminResource\RelationManagers;
use App\Models\User;
use App\Traits\MultiPanelResourse;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Pages\Page;
use Filament\Resources\Pages\CreateRecord;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Route;

class AdminResource extends Resource
{
    use MultiPanelResourse;

    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?int $navigationSort = 0;


    public static function getModelLabel(): string
    {
        return __('filament-admins-roles::user.singular_label');
    }

    public static function getPluralModelLabel(): string
    {
        return __('filament-admins-roles::user.plural_label');
    }


    protected static function getNavigationGroup(): ?string
    {
        return __('filament-admins-roles::common.nav.group');
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Card::make(
                    [
                        Forms\Components\TextInput::make('name')
                            ->label(__('filament-admins-roles::user.fields.name.label'))
                            ->placeholder(__('filament-admins-roles::user.fields.name.placeholder'))
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('email')
                            ->label(__('filament-admins-roles::user.fields.email.label'))
                            ->placeholder(__('filament-admins-roles::user.fields.email.placeholder'))
                            ->email()
                            ->required()
                            ->maxLength(255),

                        Forms\Components\TextInput::make('password')
                            ->label(__('filament-admins-roles::user.fields.password.label'))
                            ->placeholder(__('filament-admins-roles::user.fields.password.placeholder'))
                            ->password()
                            ->maxLength(255)
                            ->autocomplete('off')
                            ->dehydrateStateUsing(fn ($state) => Hash::make($state))
                            ->dehydrated(fn ($state) => filled($state))
                            ->required(fn (Page $livewire): bool => $livewire instanceof CreateRecord),
                        Forms\Components\Toggle::make('is_active')
                            ->label(__('filament-admins-roles::user.fields.is_active.label')),
                        Forms\Components\CheckboxList::make('roles')
                            ->label(__('filament-admins-roles::user.fields.roles.label'))
                            ->relationship('roles', 'name'),
                    ]
                )->columnSpan(
                    [
                        "sm" => 2
                    ]
                ),
                Forms\Components\Card::make([
                    Forms\Components\Placeholder::make('created_at')
                        ->label(__('common.fields.created_at'))
                        ->content(fn (?Model $record): string => $record ? $record->created_at->diffForHumans() : '-'),
                    Forms\Components\Placeholder::make('updated_at')
                        ->label(__('common.fields.updated_at'))
                        ->content(fn (?Model $record): string => $record ? $record->updated_at->diffForHumans() : '-'),
                ])->columnSpan(1),
            ])->columns(
                [
                    "sm" => 3,
                    "lg" => null,
                ]
            );
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')
                    ->label(__('filament-admins-roles::user.fields.id.label')),
                Tables\Columns\TextColumn::make('name')
                    ->label(__('filament-admins-roles::user.fields.name.label'))
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('email')
                    ->label(__('filament-admins-roles::user.fields.email.label'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\BooleanColumn::make('is_active')
                    ->label(__('filament-admins-roles::user.fields.is_active.label'))
                    ->sortable(),
                Tables\Columns\TagsColumn::make('roles')
                    ->label(__('filament-admins-roles::user.fields.roles.label'))
                    ->getStateUsing(function (User $record) {
                        return $record->roles->pluck("name")->toArray();
                    }),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('filament-admins-roles::user.fields.created_at.label'))
                    ->dateTime("Y-m-d h:i A"),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('filament-admins-roles::user.fields.updated_at.label'))
                    ->dateTime("Y-m-d h:i A"),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                // Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }
    public static function getApiRoutes(): array
    {
        return [
            // Route::get('/users', function () {
            //     $users = User::all();
            //     return response()->json($users, 200);
            // }),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->where("is_admin", true);
    }
    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['is_admin'] = 1;

        return $data;
    }
    public static $permissions = [
        'view',
        'view_any',
        'create',
        'update',
        // 'restore',
        // 'restore_any',
        // 'replicate',
        // 'reorder',
        'delete',
        'delete_any',
        // 'force_delete',
        // 'force_delete_any',
    ];
}

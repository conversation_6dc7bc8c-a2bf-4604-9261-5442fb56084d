<?php

namespace Alareqi\FilamentAdminsRoles\Resources;

use Alareqi\FilamentAdminsRoles\FilamentShield;
use Alareqi\FilamentAdminsRoles\Resources\RoleResource\Pages;
use Alareqi\FilamentAdminsRoles\Support\Utils;
use App\Models\Permission;
use App\Models\Role;
use App\Traits\MultiPanelResourse;
use Closure;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class RoleResource extends Resource
{
    use MultiPanelResourse;

    protected static ?string $model = Role::class;

    protected static ?string $recordTitleAttribute = 'name';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Grid::make()
                    ->schema([
                        Forms\Components\Card::make()
                            ->schema([
                                Forms\Components\TextInput::make('name')
                                    ->label(__('filament-admins-roles::role.fields.name.label'))
                                    ->required()
                                    ->maxLength(255)
                                    ->columnSpan([
                                        "default" => 1,
                                        "xl" => 2
                                    ]),
                                Forms\Components\Hidden::make('guard_name')
                                    ->default(config('filament.auth.guard')),
                                Forms\Components\Toggle::make('select_all')
                                    ->onIcon('heroicon-s-shield-check')
                                    ->offIcon('heroicon-s-shield-exclamation')
                                    ->label(__('filament-admins-roles::role.fields.select_all.label'))
                                    ->helperText(__('filament-admins-roles::role.fields.select_all.helper_text'))
                                    ->reactive()
                                    ->afterStateUpdated(function (Closure $set, $state) {
                                        static::refreshEntitiesStatesViaSelectAll($set, $state);
                                    })
                                    ->dehydrated(fn ($state): bool => $state),
                            ])
                            ->columns([
                                'sm' => 2,
                                'lg' => 3,
                            ]),
                    ]),
                Forms\Components\Tabs::make('Permissions')
                    ->tabs([
                        Forms\Components\Tabs\Tab::make(__('filament-admins-roles::role.tabs.resources'))
                            ->visible(fn (): bool => (bool) config('filament-admins-roles.entities.resources'))
                            ->reactive()
                            ->schema([
                                Forms\Components\Grid::make([
                                    'sm' => 2,
                                    'lg' => 2,
                                ])
                                    ->schema(static::getResourceEntitiesSchema())
                                    ->columns([
                                        'sm' => 2,
                                        'lg' => 2,
                                    ]),
                            ]),
                        Forms\Components\Tabs\Tab::make(__('filament-admins-roles::role.tabs.pages'))
                            ->visible(fn (): bool => (bool) (config('filament-admins-roles.entities.pages') && count(FilamentShield::getPages())) > 0 ? true : false)
                            ->reactive()
                            ->schema([
                                Forms\Components\Grid::make([
                                    'sm' => 3,
                                    'lg' => 4,
                                ])
                                    ->schema(static::getPageEntityPermissionsSchema())
                                    ->columns([
                                        'sm' => 3,
                                        'lg' => 4,
                                    ]),
                            ]),
                        Forms\Components\Tabs\Tab::make(__('filament-admins-roles::role.tabs.widgets'))
                            ->visible(fn (): bool => (bool) (config('filament-admins-roles.entities.widgets') && count(FilamentShield::getWidgets())) > 0 ? true : false)
                            ->reactive()
                            ->schema([
                                Forms\Components\Grid::make([
                                    'sm' => 3,
                                    'lg' => 4,
                                ])
                                    ->schema(static::getWidgetEntityPermissionSchema())
                                    ->columns([
                                        'sm' => 3,
                                        'lg' => 4,
                                    ]),
                            ]),

                        Forms\Components\Tabs\Tab::make(__('filament-admins-roles::role.custom'))
                            ->visible(fn (): bool => (bool) config('filament-admins-roles.entities.custom_permissions'))
                            ->reactive()
                            ->schema([
                                Forms\Components\Grid::make([
                                    'sm' => 3,
                                    'lg' => 4,
                                ])
                                    ->schema(static::getCustomEntitiesPermisssionSchema())
                                    ->columns([
                                        'sm' => 3,
                                        'lg' => 4,
                                    ]),
                            ]),
                    ])
                    ->columnSpan('full'),


            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\BadgeColumn::make('name')
                    ->label(__('filament-admins-roles::role.fields.name.label'))
                    ->formatStateUsing(fn ($state): string => Str::headline($state))
                    ->colors(['primary'])
                    ->searchable(),

                Tables\Columns\BadgeColumn::make('permissions_count')
                    ->label(__('filament-admins-roles::role.fields.permissions_count.label'))
                    ->counts('permissions')
                    ->getStateUsing(fn ($record): string => $record->name == "super_admin" ? __('filament-admins-roles::role.options.all') : $record->permissions->count())
                    ->colors(['success']),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('filament-admins-roles::common.fields.updated_at.label'))
                    ->dateTime(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->hidden(fn ($record): bool =>  $record->name == "super_admin"),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRoles::route('/'),
            'create' => Pages\CreateRole::route('/create'),
            'view' => Pages\ViewRole::route('/{record}'),
            'edit' => Pages\EditRole::route('/{record}/edit'),
        ];
    }

    public static function getModelLabel(): string
    {
        return __('filament-admins-roles::role.singular_label');
    }

    public static function getPluralModelLabel(): string
    {
        return __('filament-admins-roles::role.plural_label');
    }

    protected static function getNavigationGroup(): ?string
    {
        return __('filament-admins-roles::common.nav.group');
    }

    protected static function getNavigationIcon(): string
    {
        return "heroicon-o-shield-check";
    }

    protected static function getNavigationSort(): ?int
    {
        return Utils::getResourceNavigationSort();
    }

    public static function getSlug(): string
    {
        return Utils::getResourceSlug();
    }

    protected static function getNavigationBadge(): ?string
    {
        return Utils::isResourceNavigationBadgeEnabled()
            ? static::$model::count()
            : null;
    }

    /**--------------------------------*
    | Resource Related Logic Start     |
     *----------------------------------*/

    public static function getResourceEntitiesSchema(): ?array
    {
        return collect(FilamentShield::getResources())->sortKeys()->reduce(function ($entities, $entity) {
            $entities[] = Forms\Components\Card::make()
                // ->extraAttributes(['class' => 'border-0 shadow-xl'])
                ->schema([
                    Forms\Components\Toggle::make($entity['resource'])
                        ->label(FilamentShield::getLocalizedResourceLabel($entity['fqcn']))
                        // ->helperText(get_class(new ($entity['fqcn']::getModel())()))
                        ->onIcon('heroicon-s-lock-open')
                        ->offIcon('heroicon-s-lock-closed')
                        ->reactive()
                        ->afterStateUpdated(function (Closure $set, Closure $get, $state) use ($entity) {
                            collect(config('filament-admins-roles.permission_prefixes.resource'))->each(function ($permission) use ($set, $entity, $state) {
                                $set($permission . '_' . $entity['resource'], $state);
                            });

                            if (!$state) {
                                $set('select_all', false);
                            }

                            static::refreshSelectAllStateViaEntities($set, $get);
                        })
                        ->dehydrated(false),
                    Forms\Components\Fieldset::make('Permissions')
                        ->label(__('filament-admins-roles::role.fields.permissions.label'))
                        ->extraAttributes(['class' => 'text-primary-600', 'style' => 'border-color:var(--primary)'])
                        ->columns([
                            'default' => 2,
                            'xl' => 2,
                        ])
                        ->schema(static::getResourceEntityPermissionsSchema($entity)),
                ])
                ->columns(2)
                ->columnSpan(1);

            return $entities;
        }, collect())
            ->toArray();
    }

    public static function getResourceEntityPermissionsSchema($entity): ?array
    {
        $permissions = $entity['resource_class']::$permissions ?? null;
        if ($permissions == null) {
            $permissions = config('filament-admins-roles.permission_prefixes.resource');
        }
        return collect($permissions)->reduce(function (
            $permissions
            /** @phpstan ignore-line */
            ,
            $permission
        ) use ($entity) {
            $permissions[] = Forms\Components\Checkbox::make($permission . '_' . $entity['resource'])
                ->label(FilamentShield::getLocalizedResourcePermissionLabel($permission))
                ->extraAttributes(['class' => 'text-primary-600'])
                ->afterStateHydrated(function (Closure $set, Closure $get, $record) use ($entity, $permission) {
                    if (is_null($record)) {
                        return;
                    }

                    $set($permission . '_' . $entity['resource'], $record->checkPermissionTo($permission . '_' . $entity['resource']));

                    static::refreshResourceEntityStateAfterHydrated($record, $set, $entity['resource']);

                    static::refreshSelectAllStateViaEntities($set, $get);
                })
                ->reactive()
                ->afterStateUpdated(function (Closure $set, Closure $get, $state) use ($entity) {
                    static::refreshResourceEntityStateAfterUpdate($set, $get, $entity);

                    if (!$state) {
                        $set($entity['resource'], false);
                        $set('select_all', false);
                    }

                    static::refreshSelectAllStateViaEntities($set, $get);
                })
                ->dehydrated(fn ($state): bool => $state);

            return $permissions;
        }, collect())
            ->toArray();
    }

    protected static function refreshSelectAllStateViaEntities(Closure $set, Closure $get): void
    {
        $entitiesStates = collect(FilamentShield::getResources())
            ->when(config('filament-admins-roles.entities.pages'), fn ($entities) => $entities->merge(FilamentShield::getPages()))
            ->when(config('filament-admins-roles.entities.widgets'), fn ($entities) => $entities->merge(FilamentShield::getWidgets()))
            ->when(config('filament-admins-roles.entities.custom_permissions'), fn ($entities) => $entities->merge(static::getCustomEntities()))
            ->map(function ($entity) use ($get) {
                if (is_array($entity)) {
                    return (bool) $get($entity['resource']);
                }

                return (bool) $get($entity);
            });

        if ($entitiesStates->containsStrict(false) === false) {
            $set('select_all', true);
        }

        if ($entitiesStates->containsStrict(false) === true) {
            $set('select_all', false);
        }
    }

    protected static function refreshEntitiesStatesViaSelectAll(Closure $set, $state): void
    {
        collect(FilamentShield::getResources())->each(function ($entity) use ($set, $state) {
            $set($entity['resource'], $state);
            $permissions = $entity['resource_class']::$permissions ?? null;
            if ($permissions == null) {
                $permissions = config('filament-admins-roles.permission_prefixes.resource');
            }
            collect($permissions)->each(function ($permission) use ($entity, $set, $state) {
                $set($permission . '_' . $entity['resource'], $state);
            });
        });

        collect(FilamentShield::getPages())->each(function ($page) use ($set, $state) {
            if (config('filament-admins-roles.entities.pages')) {
                $set($page, $state);
            }
        });

        collect(FilamentShield::getWidgets())->each(function ($widget) use ($set, $state) {
            $set($widget, $state);
        });

        static::getCustomEntities()->each(function ($custom) use ($set, $state) {
            if (config('filament-admins-roles.entities.custom_permissions')) {
                $set($custom, $state);
            }
        });
    }

    protected static function refreshResourceEntityStateAfterUpdate(Closure $set, Closure $get, $entity): void
    {
        $permissions = $entity['resource_class']::$permissions ?? null;
        if ($permissions == null) {
            $permissions = config('filament-admins-roles.permission_prefixes.resource');
        }
        $permissionStates = collect($permissions)
            ->map(function ($permission) use ($get, $entity) {
                return (bool) $get($permission . '_' . $entity['resource']);
            });

        if ($permissionStates->containsStrict(false) === false) {
            $set($entity['resource'], true);
        }

        if ($permissionStates->containsStrict(false) === true) {
            $set($entity['resource'], false);
        }
    }

    protected static function refreshResourceEntityStateAfterHydrated(Model $record, Closure $set, string $entity): void
    {
        $entities = $record->permissions->pluck('name')
            ->reduce(function ($roles, $role) {
                $roles[$role] = Str::afterLast($role, '_');

                return $roles;
            }, collect())
            ->values()
            ->groupBy(function ($item) {
                return $item;
            })->map->count()
            ->reduce(function ($counts, $role, $key) {
                if ($role > 1 && $role = count(config('filament-admins-roles.permission_prefixes.resource'))) {
                    $counts[$key] = true;
                } else {
                    $counts[$key] = false;
                }

                return $counts;
            }, []);

        // set entity's state if one are all permissions are true
        if (in_array($entity, array_keys($entities)) && $entities[$entity]) {
            $set($entity, true);
        } else {
            $set($entity, false);
            $set('select_all', false);
        }
    }
    /**--------------------------------*
    | Resource Related Logic End       |
     *----------------------------------*/

    /**--------------------------------*
    | Page Related Logic Start       |
     *----------------------------------*/

    protected static function getPageEntityPermissionsSchema(): ?array
    {
        return collect(FilamentShield::getPages())->sortKeys()->reduce(function ($pages, $page) {
            $pages[] = Forms\Components\Grid::make()
                ->schema([
                    Forms\Components\Checkbox::make($page)
                        ->label(FilamentShield::getLocalizedPageLabel($page))
                        ->inline()
                        ->afterStateHydrated(function (Closure $set, Closure $get, $record) use ($page) {
                            if (is_null($record)) {
                                return;
                            }

                            $set($page, $record->checkPermissionTo($page));

                            static::refreshSelectAllStateViaEntities($set, $get);
                        })
                        ->reactive()
                        ->afterStateUpdated(function (Closure $set, Closure $get, $state) {
                            if (!$state) {
                                $set('select_all', false);
                            }

                            static::refreshSelectAllStateViaEntities($set, $get);
                        })
                        ->dehydrated(fn ($state): bool => $state),
                ])
                ->columns(1)
                ->columnSpan(1);

            return $pages;
        }, []);
    }
    /**--------------------------------*
    | Page Related Logic End          |
     *----------------------------------*/

    /**--------------------------------*
    | Widget Related Logic Start       |
     *----------------------------------*/

    protected static function getWidgetEntityPermissionSchema(): ?array
    {
        return collect(FilamentShield::getWidgets())->reduce(function ($widgets, $widget) {
            $widgets[] = Forms\Components\Grid::make()
                ->schema([
                    Forms\Components\Checkbox::make($widget)
                        ->label(FilamentShield::getLocalizedWidgetLabel($widget))
                        ->inline()
                        ->afterStateHydrated(function (Closure $set, Closure $get, $record) use ($widget) {
                            if (is_null($record)) {
                                return;
                            }

                            $set($widget, $record->checkPermissionTo($widget));

                            static::refreshSelectAllStateViaEntities($set, $get);
                        })
                        ->reactive()
                        ->afterStateUpdated(function (Closure $set, Closure $get, $state) {
                            if (!$state) {
                                $set('select_all', false);
                            }

                            static::refreshSelectAllStateViaEntities($set, $get);
                        })
                        ->dehydrated(fn ($state): bool => $state),
                ])
                ->columns(1)
                ->columnSpan(1);

            return $widgets;
        }, []);
    }
    /**--------------------------------*
    | Widget Related Logic End          |
     *----------------------------------*/

    protected static function getCustomEntities(): ?Collection
    {
        $resourcePermissions = collect();
        collect(FilamentShield::getResources())->each(function ($entity) use ($resourcePermissions) {
            collect(config('filament-admins-roles.permission_prefixes.resource'))->map(function ($permission) use ($resourcePermissions, $entity) {
                $resourcePermissions->push((string) Str::of($permission . '_' . $entity['resource']));
            });
        });
        $entitiesPermissions = $resourcePermissions
            ->merge(FilamentShield::getPages())
            ->merge(FilamentShield::getWidgets())
            ->values();
        return Permission::whereNotIn('name', $entitiesPermissions)->pluck('name');
    }

    protected static function getCustomEntitiesPermisssionSchema(): ?array
    {
        return collect(static::getCustomEntities())->reduce(function ($customEntities, $customPermission) {
            $customEntities[] = Forms\Components\Grid::make()
                ->schema([
                    Forms\Components\Checkbox::make($customPermission)
                        ->label(Str::of($customPermission)->headline())
                        ->inline()
                        ->afterStateHydrated(function (Closure $set, Closure $get, $record) use ($customPermission) {
                            if (is_null($record)) {
                                return;
                            }

                            $set($customPermission, $record->checkPermissionTo($customPermission));

                            static::refreshSelectAllStateViaEntities($set, $get);
                        })
                        ->reactive()
                        ->afterStateUpdated(function (Closure $set, Closure $get, $state) {
                            if (!$state) {
                                $set('select_all', false);
                            }

                            static::refreshSelectAllStateViaEntities($set, $get);
                        })
                        ->dehydrated(fn ($state): bool => $state),
                ])
                ->columns(1)
                ->columnSpan(1);

            return $customEntities;
        }, []);
    }
    public static $permissions = [
        'view',
        'view_any',
        'create',
        'update',
        // 'restore',
        // 'restore_any',
        // 'replicate',
        // 'reorder',
        'delete',
        'delete_any',
        // 'force_delete',
        // 'force_delete_any',
    ];
}

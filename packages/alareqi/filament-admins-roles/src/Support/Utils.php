<?php

namespace Alareqi\FilamentAdminsRoles\Support;

class Utils
{
    public static function getResourceSlug(): string
    {
        return (string) config('filament-admins-roles.shield_resource.slug');
    }

    public static function getResourceNavigationSort(): int
    {
        return config('filament-admins-roles.shield_resource.navigation_sort');
    }

    public static function isResourceNavigationBadgeEnabled(): bool
    {
        return config('filament-admins-roles.shield_resource.navigation_badge', true);
    }

    public static function getAuthProviderFQCN()
    {
        return config('filament-admins-roles.auth_provider_model.fqcn');
    }

    public static function isAuthProviderConfigured(): bool
    {
        return in_array("Alareqi\FilamentAdminsRoles\Traits\HasFilamentShield", class_uses(static::getAuthProviderFQCN()))
            || in_array("Spatie\Permission\Traits\HasRoles", class_uses(static::getAuthProviderFQCN()));
    }
}

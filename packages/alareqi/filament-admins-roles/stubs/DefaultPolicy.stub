<?php

namespace {{ namespace }};

use {{ auth_model_fqcn }};
use Illuminate\Auth\Access\HandlesAuthorization;

class {{ modelPolicy }}
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param  \{{ auth_model_fqcn }}  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny({{ auth_model_name }} $user)
    {
        return $user->can('{{ ViewAny }}');
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param  \{{ auth_model_fqcn }}  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view({{ auth_model_name }} $user)
    {
        return $user->can('{{ View }}');
    }

    /**
     * Determine whether the user can create models.
     *
     * @param  \{{ auth_model_fqcn }}  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create({{ auth_model_name }} $user)
    {
        return $user->can('{{ Create }}');
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param  \{{ auth_model_fqcn }}  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update({{ auth_model_name }} $user)
    {
        return $user->can('{{ Update }}');
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param  \{{ auth_model_fqcn }}  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete({{ auth_model_name }} $user)
    {
        return $user->can('{{ Delete }}');
    }

    /**
     * Determine whether the user can bulk delete.
     *
     * @param  \{{ auth_model_fqcn }}  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function deleteAny({{ auth_model_name }} $user)
    {
        return $user->can('{{ DeleteAny }}');
    }

    /**
     * Determine whether the user can permanently delete.
     *
     * @param  \{{ auth_model_fqcn }}  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function forceDelete({{ auth_model_name }} $user)
    {
        return $user->can('{{ ForceDelete }}');
    }

    /**
     * Determine whether the user can permanently bulk delete.
     *
     * @param  \{{ auth_model_fqcn }}  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function forceDeleteAny({{ auth_model_name }} $user)
    {
        return $user->can('{{ ForceDeleteAny }}');
    }

    /**
     * Determine whether the user can restore.
     *
     * @param  \{{ auth_model_fqcn }}  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function restore({{ auth_model_name }} $user)
    {
        return $user->can('{{ Restore }}');
    }

    /**
     * Determine whether the user can bulk restore.
     *
     * @param  \{{ auth_model_fqcn }}  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function restoreAny({{ auth_model_name }} $user)
    {
        return $user->can('{{ RestoreAny }}');
    }

    /**
     * Determine whether the user can bulk restore.
     *
     * @param  \{{ auth_model_fqcn }}  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function replicate({{ auth_model_name }} $user)
    {
        return $user->can('{{ Replicate }}');
    }

    /**
     * Determine whether the user can reorder.
     *
     * @param  \{{ auth_model_fqcn }}  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function reorder({{ auth_model_name }} $user)
    {
        return $user->can('{{ Reorder }}');
    }

}

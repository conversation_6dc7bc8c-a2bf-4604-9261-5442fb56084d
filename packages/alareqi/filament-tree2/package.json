{"private": true, "scripts": {"dev:styles": "npx tailwindcss -i resources/css/plugin.css -o resources/dist/filament-tree.css --postcss --watch", "dev:scripts": "esbuild resources/js/plugin.js --bundle --sourcemap=inline --outfile=resources/dist/filament-tree.js --watch", "build:styles": "npx tailwindcss -i resources/css/plugin.css -o resources/dist/filament-tree.css --postcss --minify && npm run purge", "build:scripts": "esbuild resources/js/plugin.js --bundle --minify --outfile=resources/dist/filament-tree.js", "purge": "filament-purge -i resources/dist/filament-tree.css -o resources/dist/filament-tree.css", "dev": "npm-run-all --parallel dev:*", "build": "npm-run-all build:*"}, "devDependencies": {"@awcodes/filament-plugin-purge": "^1.0.2", "autoprefixer": "^10.4.7", "esbuild": "^0.8.57", "imports-loader": "^4.0.1", "npm-run-all": "^4.1.5", "postcss": "^8.4.14", "postcss-import": "^15.0.0", "prettier": "^2.7.1", "prettier-plugin-tailwindcss": "^0.1.13", "tailwindcss": "^3.2"}, "dependencies": {"jquery": "^3.6.4"}}
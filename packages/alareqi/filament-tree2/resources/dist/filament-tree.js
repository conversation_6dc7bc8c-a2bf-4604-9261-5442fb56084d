(()=>{var Yn=(N,K)=>()=>(K||(K={exports:{}},N(K.exports,K)),K.exports);var Un=Yn((zn,Et)=>{(function(N,K){"use strict";typeof Et=="object"&&typeof Et.exports=="object"?Et.exports=N.document?K(N,!0):function(V){if(!V.document)throw new Error("jQuery requires a window with a document");return K(V)}:K(N)})(typeof window!="undefined"?window:zn,function(N,K){"use strict";var V=[],Je=Object.getPrototypeOf,ve=V.slice,Xe=V.flat?function(e){return V.flat.call(e)}:function(e){return V.concat.apply([],e)},_e=V.push,We=V.indexOf,je={},w=je.toString,L=je.hasOwnProperty,Y=L.toString,W=Y.call(Object),D={},A=function(t){return typeof t=="function"&&typeof t.nodeType!="number"&&typeof t.item!="function"},R=function(t){return t!=null&&t===t.window},T=N.document,Se={type:!0,src:!0,nonce:!0,noModule:!0};function Ce(e,t,n){n=n||T;var i,s,o=n.createElement("script");if(o.text=e,t)for(i in Se)s=t[i]||t.getAttribute&&t.getAttribute(i),s&&o.setAttribute(i,s);n.head.appendChild(o).parentNode.removeChild(o)}function me(e){return e==null?e+"":typeof e=="object"||typeof e=="function"?je[w.call(e)]||"object":typeof e}var Be="3.6.4",r=function(e,t){return new r.fn.init(e,t)};r.fn=r.prototype={jquery:Be,constructor:r,length:0,toArray:function(){return ve.call(this)},get:function(e){return e==null?ve.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){var t=r.merge(this.constructor(),e);return t.prevObject=this,t},each:function(e){return r.each(this,e)},map:function(e){return this.pushStack(r.map(this,function(t,n){return e.call(t,n,t)}))},slice:function(){return this.pushStack(ve.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(r.grep(this,function(e,t){return(t+1)%2}))},odd:function(){return this.pushStack(r.grep(this,function(e,t){return t%2}))},eq:function(e){var t=this.length,n=+e+(e<0?t:0);return this.pushStack(n>=0&&n<t?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:_e,sort:V.sort,splice:V.splice},r.extend=r.fn.extend=function(){var e,t,n,i,s,o,a=arguments[0]||{},c=1,f=arguments.length,h=!1;for(typeof a=="boolean"&&(h=a,a=arguments[c]||{},c++),typeof a!="object"&&!A(a)&&(a={}),c===f&&(a=this,c--);c<f;c++)if((e=arguments[c])!=null)for(t in e)i=e[t],!(t==="__proto__"||a===i)&&(h&&i&&(r.isPlainObject(i)||(s=Array.isArray(i)))?(n=a[t],s&&!Array.isArray(n)?o=[]:!s&&!r.isPlainObject(n)?o={}:o=n,s=!1,a[t]=r.extend(h,o,i)):i!==void 0&&(a[t]=i));return a},r.extend({expando:"jQuery"+(Be+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isPlainObject:function(e){var t,n;return!e||w.call(e)!=="[object Object]"?!1:(t=Je(e),t?(n=L.call(t,"constructor")&&t.constructor,typeof n=="function"&&Y.call(n)===W):!0)},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},globalEval:function(e,t,n){Ce(e,{nonce:t&&t.nonce},n)},each:function(e,t){var n,i=0;if(Nt(e))for(n=e.length;i<n&&t.call(e[i],i,e[i])!==!1;i++);else for(i in e)if(t.call(e[i],i,e[i])===!1)break;return e},makeArray:function(e,t){var n=t||[];return e!=null&&(Nt(Object(e))?r.merge(n,typeof e=="string"?[e]:e):_e.call(n,e)),n},inArray:function(e,t,n){return t==null?-1:We.call(t,e,n)},merge:function(e,t){for(var n=+t.length,i=0,s=e.length;i<n;i++)e[s++]=t[i];return e.length=s,e},grep:function(e,t,n){for(var i,s=[],o=0,a=e.length,c=!n;o<a;o++)i=!t(e[o],o),i!==c&&s.push(e[o]);return s},map:function(e,t,n){var i,s,o=0,a=[];if(Nt(e))for(i=e.length;o<i;o++)s=t(e[o],o,n),s!=null&&a.push(s);else for(o in e)s=t(e[o],o,n),s!=null&&a.push(s);return Xe(a)},guid:1,support:D}),typeof Symbol=="function"&&(r.fn[Symbol.iterator]=V[Symbol.iterator]),r.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){je["[object "+t+"]"]=t.toLowerCase()});function Nt(e){var t=!!e&&"length"in e&&e.length,n=me(e);return A(e)||R(e)?!1:n==="array"||t===0||typeof t=="number"&&t>0&&t-1 in e}var ke=function(e){var t,n,i,s,o,a,c,f,h,y,x,g,v,k,X,j,Z,J,ae,z="sizzle"+1*new Date,I=e.document,se=0,B=0,G=xt(),ot=xt(),vt=xt(),ue=xt(),Pe=function(u,l){return u===l&&(x=!0),0},Oe={}.hasOwnProperty,oe=[],Ae=oe.pop,pe=oe.push,De=oe.push,On=oe.slice,Me=function(u,l){for(var d=0,m=u.length;d<m;d++)if(u[d]===l)return d;return-1},Bt="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",F="[\\x20\\t\\r\\n\\f]",Re="(?:\\\\[\\da-fA-F]{1,6}"+F+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",Mn="\\["+F+"*("+Re+")(?:"+F+"*([*^$|!~]?=)"+F+`*(?:'((?:\\\\.|[^\\\\'])*)'|"((?:\\\\.|[^\\\\"])*)"|(`+Re+"))|)"+F+"*\\]",Ft=":("+Re+`)(?:\\((('((?:\\\\.|[^\\\\'])*)'|"((?:\\\\.|[^\\\\"])*)")|((?:\\\\.|[^\\\\()[\\]]|`+Mn+")*)|.*)\\)|)",zi=new RegExp(F+"+","g"),mt=new RegExp("^"+F+"+|((?:^|[^\\\\])(?:\\\\.)*)"+F+"+$","g"),Ui=new RegExp("^"+F+"*,"+F+"*"),Rn=new RegExp("^"+F+"*([>+~]|"+F+")"+F+"*"),$i=new RegExp(F+"|>"),Vi=new RegExp(Ft),Qi=new RegExp("^"+Re+"$"),bt={ID:new RegExp("^#("+Re+")"),CLASS:new RegExp("^\\.("+Re+")"),TAG:new RegExp("^("+Re+"|[*])"),ATTR:new RegExp("^"+Mn),PSEUDO:new RegExp("^"+Ft),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+F+"*(even|odd|(([+-]|)(\\d*)n|)"+F+"*(?:([+-]|)"+F+"*(\\d+)|))"+F+"*\\)|)","i"),bool:new RegExp("^(?:"+Bt+")$","i"),needsContext:new RegExp("^"+F+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+F+"*((?:-\\d)?\\d*)"+F+"*\\)|)(?=[^-]|$)","i")},Gi=/HTML$/i,Ji=/^(?:input|select|textarea|button)$/i,Ki=/^h\d$/i,at=/^[^{]+\{\s*\[native \w/,Zi=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,Yt=/[+~]/,Ee=new RegExp("\\\\[\\da-fA-F]{1,6}"+F+"?|\\\\([^\\r\\n\\f])","g"),Ne=function(u,l){var d="0x"+u.slice(1)-65536;return l||(d<0?String.fromCharCode(d+65536):String.fromCharCode(d>>10|55296,d&1023|56320))},In=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,Xn=function(u,l){return l?u==="\0"?"\uFFFD":u.slice(0,-1)+"\\"+u.charCodeAt(u.length-1).toString(16)+" ":"\\"+u},_n=function(){g()},er=Tt(function(u){return u.disabled===!0&&u.nodeName.toLowerCase()==="fieldset"},{dir:"parentNode",next:"legend"});try{De.apply(oe=On.call(I.childNodes),I.childNodes),oe[I.childNodes.length].nodeType}catch(u){De={apply:oe.length?function(l,d){pe.apply(l,On.call(d))}:function(l,d){for(var m=l.length,p=0;l[m++]=d[p++];);l.length=m-1}}}function U(u,l,d,m){var p,b,C,E,S,P,q,M=l&&l.ownerDocument,_=l?l.nodeType:9;if(d=d||[],typeof u!="string"||!u||_!==1&&_!==9&&_!==11)return d;if(!m&&(g(l),l=l||v,X)){if(_!==11&&(S=Zi.exec(u)))if(p=S[1]){if(_===9)if(C=l.getElementById(p)){if(C.id===p)return d.push(C),d}else return d;else if(M&&(C=M.getElementById(p))&&ae(l,C)&&C.id===p)return d.push(C),d}else{if(S[2])return De.apply(d,l.getElementsByTagName(u)),d;if((p=S[3])&&n.getElementsByClassName&&l.getElementsByClassName)return De.apply(d,l.getElementsByClassName(p)),d}if(n.qsa&&!ue[u+" "]&&(!j||!j.test(u))&&(_!==1||l.nodeName.toLowerCase()!=="object")){if(q=u,M=l,_===1&&($i.test(u)||Rn.test(u))){for(M=Yt.test(u)&&Ut(l.parentNode)||l,(M!==l||!n.scope)&&((E=l.getAttribute("id"))?E=E.replace(In,Xn):l.setAttribute("id",E=z)),P=a(u),b=P.length;b--;)P[b]=(E?"#"+E:":scope")+" "+Ct(P[b]);q=P.join(",")}try{return De.apply(d,M.querySelectorAll(q)),d}catch($){ue(u,!0)}finally{E===z&&l.removeAttribute("id")}}}return f(u.replace(mt,"$1"),l,d,m)}function xt(){var u=[];function l(d,m){return u.push(d+" ")>i.cacheLength&&delete l[u.shift()],l[d+" "]=m}return l}function ye(u){return u[z]=!0,u}function he(u){var l=v.createElement("fieldset");try{return!!u(l)}catch(d){return!1}finally{l.parentNode&&l.parentNode.removeChild(l),l=null}}function zt(u,l){for(var d=u.split("|"),m=d.length;m--;)i.attrHandle[d[m]]=l}function Wn(u,l){var d=l&&u,m=d&&u.nodeType===1&&l.nodeType===1&&u.sourceIndex-l.sourceIndex;if(m)return m;if(d){for(;d=d.nextSibling;)if(d===l)return-1}return u?1:-1}function tr(u){return function(l){var d=l.nodeName.toLowerCase();return d==="input"&&l.type===u}}function nr(u){return function(l){var d=l.nodeName.toLowerCase();return(d==="input"||d==="button")&&l.type===u}}function Bn(u){return function(l){return"form"in l?l.parentNode&&l.disabled===!1?"label"in l?"label"in l.parentNode?l.parentNode.disabled===u:l.disabled===u:l.isDisabled===u||l.isDisabled!==!u&&er(l)===u:l.disabled===u:"label"in l?l.disabled===u:!1}}function Ie(u){return ye(function(l){return l=+l,ye(function(d,m){for(var p,b=u([],d.length,l),C=b.length;C--;)d[p=b[C]]&&(d[p]=!(m[p]=d[p]))})})}function Ut(u){return u&&typeof u.getElementsByTagName!="undefined"&&u}n=U.support={},o=U.isXML=function(u){var l=u&&u.namespaceURI,d=u&&(u.ownerDocument||u).documentElement;return!Gi.test(l||d&&d.nodeName||"HTML")},g=U.setDocument=function(u){var l,d,m=u?u.ownerDocument||u:I;return m==v||m.nodeType!==9||!m.documentElement||(v=m,k=v.documentElement,X=!o(v),I!=v&&(d=v.defaultView)&&d.top!==d&&(d.addEventListener?d.addEventListener("unload",_n,!1):d.attachEvent&&d.attachEvent("onunload",_n)),n.scope=he(function(p){return k.appendChild(p).appendChild(v.createElement("div")),typeof p.querySelectorAll!="undefined"&&!p.querySelectorAll(":scope fieldset div").length}),n.cssHas=he(function(){try{return v.querySelector(":has(*,:jqfake)"),!1}catch(p){return!0}}),n.attributes=he(function(p){return p.className="i",!p.getAttribute("className")}),n.getElementsByTagName=he(function(p){return p.appendChild(v.createComment("")),!p.getElementsByTagName("*").length}),n.getElementsByClassName=at.test(v.getElementsByClassName),n.getById=he(function(p){return k.appendChild(p).id=z,!v.getElementsByName||!v.getElementsByName(z).length}),n.getById?(i.filter.ID=function(p){var b=p.replace(Ee,Ne);return function(C){return C.getAttribute("id")===b}},i.find.ID=function(p,b){if(typeof b.getElementById!="undefined"&&X){var C=b.getElementById(p);return C?[C]:[]}}):(i.filter.ID=function(p){var b=p.replace(Ee,Ne);return function(C){var E=typeof C.getAttributeNode!="undefined"&&C.getAttributeNode("id");return E&&E.value===b}},i.find.ID=function(p,b){if(typeof b.getElementById!="undefined"&&X){var C,E,S,P=b.getElementById(p);if(P){if(C=P.getAttributeNode("id"),C&&C.value===p)return[P];for(S=b.getElementsByName(p),E=0;P=S[E++];)if(C=P.getAttributeNode("id"),C&&C.value===p)return[P]}return[]}}),i.find.TAG=n.getElementsByTagName?function(p,b){if(typeof b.getElementsByTagName!="undefined")return b.getElementsByTagName(p);if(n.qsa)return b.querySelectorAll(p)}:function(p,b){var C,E=[],S=0,P=b.getElementsByTagName(p);if(p==="*"){for(;C=P[S++];)C.nodeType===1&&E.push(C);return E}return P},i.find.CLASS=n.getElementsByClassName&&function(p,b){if(typeof b.getElementsByClassName!="undefined"&&X)return b.getElementsByClassName(p)},Z=[],j=[],(n.qsa=at.test(v.querySelectorAll))&&(he(function(p){var b;k.appendChild(p).innerHTML="<a id='"+z+"'></a><select id='"+z+"-\r\\' msallowcapture=''><option selected=''></option></select>",p.querySelectorAll("[msallowcapture^='']").length&&j.push("[*^$]="+F+`*(?:''|"")`),p.querySelectorAll("[selected]").length||j.push("\\["+F+"*(?:value|"+Bt+")"),p.querySelectorAll("[id~="+z+"-]").length||j.push("~="),b=v.createElement("input"),b.setAttribute("name",""),p.appendChild(b),p.querySelectorAll("[name='']").length||j.push("\\["+F+"*name"+F+"*="+F+`*(?:''|"")`),p.querySelectorAll(":checked").length||j.push(":checked"),p.querySelectorAll("a#"+z+"+*").length||j.push(".#.+[+~]"),p.querySelectorAll("\\\f"),j.push("[\\r\\n\\f]")}),he(function(p){p.innerHTML="<a href='' disabled='disabled'></a><select disabled='disabled'><option/></select>";var b=v.createElement("input");b.setAttribute("type","hidden"),p.appendChild(b).setAttribute("name","D"),p.querySelectorAll("[name=d]").length&&j.push("name"+F+"*[*^$|!~]?="),p.querySelectorAll(":enabled").length!==2&&j.push(":enabled",":disabled"),k.appendChild(p).disabled=!0,p.querySelectorAll(":disabled").length!==2&&j.push(":enabled",":disabled"),p.querySelectorAll("*,:x"),j.push(",.*:")})),(n.matchesSelector=at.test(J=k.matches||k.webkitMatchesSelector||k.mozMatchesSelector||k.oMatchesSelector||k.msMatchesSelector))&&he(function(p){n.disconnectedMatch=J.call(p,"*"),J.call(p,"[s!='']:x"),Z.push("!=",Ft)}),n.cssHas||j.push(":has"),j=j.length&&new RegExp(j.join("|")),Z=Z.length&&new RegExp(Z.join("|")),l=at.test(k.compareDocumentPosition),ae=l||at.test(k.contains)?function(p,b){var C=p.nodeType===9&&p.documentElement||p,E=b&&b.parentNode;return p===E||!!(E&&E.nodeType===1&&(C.contains?C.contains(E):p.compareDocumentPosition&&p.compareDocumentPosition(E)&16))}:function(p,b){if(b){for(;b=b.parentNode;)if(b===p)return!0}return!1},Pe=l?function(p,b){if(p===b)return x=!0,0;var C=!p.compareDocumentPosition-!b.compareDocumentPosition;return C||(C=(p.ownerDocument||p)==(b.ownerDocument||b)?p.compareDocumentPosition(b):1,C&1||!n.sortDetached&&b.compareDocumentPosition(p)===C?p==v||p.ownerDocument==I&&ae(I,p)?-1:b==v||b.ownerDocument==I&&ae(I,b)?1:y?Me(y,p)-Me(y,b):0:C&4?-1:1)}:function(p,b){if(p===b)return x=!0,0;var C,E=0,S=p.parentNode,P=b.parentNode,q=[p],M=[b];if(!S||!P)return p==v?-1:b==v?1:S?-1:P?1:y?Me(y,p)-Me(y,b):0;if(S===P)return Wn(p,b);for(C=p;C=C.parentNode;)q.unshift(C);for(C=b;C=C.parentNode;)M.unshift(C);for(;q[E]===M[E];)E++;return E?Wn(q[E],M[E]):q[E]==I?-1:M[E]==I?1:0}),v},U.matches=function(u,l){return U(u,null,null,l)},U.matchesSelector=function(u,l){if(g(u),n.matchesSelector&&X&&!ue[l+" "]&&(!Z||!Z.test(l))&&(!j||!j.test(l)))try{var d=J.call(u,l);if(d||n.disconnectedMatch||u.document&&u.document.nodeType!==11)return d}catch(m){ue(l,!0)}return U(l,v,null,[u]).length>0},U.contains=function(u,l){return(u.ownerDocument||u)!=v&&g(u),ae(u,l)},U.attr=function(u,l){(u.ownerDocument||u)!=v&&g(u);var d=i.attrHandle[l.toLowerCase()],m=d&&Oe.call(i.attrHandle,l.toLowerCase())?d(u,l,!X):void 0;return m!==void 0?m:n.attributes||!X?u.getAttribute(l):(m=u.getAttributeNode(l))&&m.specified?m.value:null},U.escape=function(u){return(u+"").replace(In,Xn)},U.error=function(u){throw new Error("Syntax error, unrecognized expression: "+u)},U.uniqueSort=function(u){var l,d=[],m=0,p=0;if(x=!n.detectDuplicates,y=!n.sortStable&&u.slice(0),u.sort(Pe),x){for(;l=u[p++];)l===u[p]&&(m=d.push(p));for(;m--;)u.splice(d[m],1)}return y=null,u},s=U.getText=function(u){var l,d="",m=0,p=u.nodeType;if(p){if(p===1||p===9||p===11){if(typeof u.textContent=="string")return u.textContent;for(u=u.firstChild;u;u=u.nextSibling)d+=s(u)}else if(p===3||p===4)return u.nodeValue}else for(;l=u[m++];)d+=s(l);return d},i=U.selectors={cacheLength:50,createPseudo:ye,match:bt,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(u){return u[1]=u[1].replace(Ee,Ne),u[3]=(u[3]||u[4]||u[5]||"").replace(Ee,Ne),u[2]==="~="&&(u[3]=" "+u[3]+" "),u.slice(0,4)},CHILD:function(u){return u[1]=u[1].toLowerCase(),u[1].slice(0,3)==="nth"?(u[3]||U.error(u[0]),u[4]=+(u[4]?u[5]+(u[6]||1):2*(u[3]==="even"||u[3]==="odd")),u[5]=+(u[7]+u[8]||u[3]==="odd")):u[3]&&U.error(u[0]),u},PSEUDO:function(u){var l,d=!u[6]&&u[2];return bt.CHILD.test(u[0])?null:(u[3]?u[2]=u[4]||u[5]||"":d&&Vi.test(d)&&(l=a(d,!0))&&(l=d.indexOf(")",d.length-l)-d.length)&&(u[0]=u[0].slice(0,l),u[2]=d.slice(0,l)),u.slice(0,3))}},filter:{TAG:function(u){var l=u.replace(Ee,Ne).toLowerCase();return u==="*"?function(){return!0}:function(d){return d.nodeName&&d.nodeName.toLowerCase()===l}},CLASS:function(u){var l=G[u+" "];return l||(l=new RegExp("(^|"+F+")"+u+"("+F+"|$)"))&&G(u,function(d){return l.test(typeof d.className=="string"&&d.className||typeof d.getAttribute!="undefined"&&d.getAttribute("class")||"")})},ATTR:function(u,l,d){return function(m){var p=U.attr(m,u);return p==null?l==="!=":l?(p+="",l==="="?p===d:l==="!="?p!==d:l==="^="?d&&p.indexOf(d)===0:l==="*="?d&&p.indexOf(d)>-1:l==="$="?d&&p.slice(-d.length)===d:l==="~="?(" "+p.replace(zi," ")+" ").indexOf(d)>-1:l==="|="?p===d||p.slice(0,d.length+1)===d+"-":!1):!0}},CHILD:function(u,l,d,m,p){var b=u.slice(0,3)!=="nth",C=u.slice(-4)!=="last",E=l==="of-type";return m===1&&p===0?function(S){return!!S.parentNode}:function(S,P,q){var M,_,$,O,ee,te,fe=b!==C?"nextSibling":"previousSibling",Q=S.parentNode,ut=E&&S.nodeName.toLowerCase(),ft=!q&&!E,le=!1;if(Q){if(b){for(;fe;){for(O=S;O=O[fe];)if(E?O.nodeName.toLowerCase()===ut:O.nodeType===1)return!1;te=fe=u==="only"&&!te&&"nextSibling"}return!0}if(te=[C?Q.firstChild:Q.lastChild],C&&ft){for(O=Q,$=O[z]||(O[z]={}),_=$[O.uniqueID]||($[O.uniqueID]={}),M=_[u]||[],ee=M[0]===se&&M[1],le=ee&&M[2],O=ee&&Q.childNodes[ee];O=++ee&&O&&O[fe]||(le=ee=0)||te.pop();)if(O.nodeType===1&&++le&&O===S){_[u]=[se,ee,le];break}}else if(ft&&(O=S,$=O[z]||(O[z]={}),_=$[O.uniqueID]||($[O.uniqueID]={}),M=_[u]||[],ee=M[0]===se&&M[1],le=ee),le===!1)for(;(O=++ee&&O&&O[fe]||(le=ee=0)||te.pop())&&!((E?O.nodeName.toLowerCase()===ut:O.nodeType===1)&&++le&&(ft&&($=O[z]||(O[z]={}),_=$[O.uniqueID]||($[O.uniqueID]={}),_[u]=[se,le]),O===S)););return le-=p,le===m||le%m==0&&le/m>=0}}},PSEUDO:function(u,l){var d,m=i.pseudos[u]||i.setFilters[u.toLowerCase()]||U.error("unsupported pseudo: "+u);return m[z]?m(l):m.length>1?(d=[u,u,"",l],i.setFilters.hasOwnProperty(u.toLowerCase())?ye(function(p,b){for(var C,E=m(p,l),S=E.length;S--;)C=Me(p,E[S]),p[C]=!(b[C]=E[S])}):function(p){return m(p,0,d)}):m}},pseudos:{not:ye(function(u){var l=[],d=[],m=c(u.replace(mt,"$1"));return m[z]?ye(function(p,b,C,E){for(var S,P=m(p,null,E,[]),q=p.length;q--;)(S=P[q])&&(p[q]=!(b[q]=S))}):function(p,b,C){return l[0]=p,m(l,null,C,d),l[0]=null,!d.pop()}}),has:ye(function(u){return function(l){return U(u,l).length>0}}),contains:ye(function(u){return u=u.replace(Ee,Ne),function(l){return(l.textContent||s(l)).indexOf(u)>-1}}),lang:ye(function(u){return Qi.test(u||"")||U.error("unsupported lang: "+u),u=u.replace(Ee,Ne).toLowerCase(),function(l){var d;do if(d=X?l.lang:l.getAttribute("xml:lang")||l.getAttribute("lang"))return d=d.toLowerCase(),d===u||d.indexOf(u+"-")===0;while((l=l.parentNode)&&l.nodeType===1);return!1}}),target:function(u){var l=e.location&&e.location.hash;return l&&l.slice(1)===u.id},root:function(u){return u===k},focus:function(u){return u===v.activeElement&&(!v.hasFocus||v.hasFocus())&&!!(u.type||u.href||~u.tabIndex)},enabled:Bn(!1),disabled:Bn(!0),checked:function(u){var l=u.nodeName.toLowerCase();return l==="input"&&!!u.checked||l==="option"&&!!u.selected},selected:function(u){return u.parentNode&&u.parentNode.selectedIndex,u.selected===!0},empty:function(u){for(u=u.firstChild;u;u=u.nextSibling)if(u.nodeType<6)return!1;return!0},parent:function(u){return!i.pseudos.empty(u)},header:function(u){return Ki.test(u.nodeName)},input:function(u){return Ji.test(u.nodeName)},button:function(u){var l=u.nodeName.toLowerCase();return l==="input"&&u.type==="button"||l==="button"},text:function(u){var l;return u.nodeName.toLowerCase()==="input"&&u.type==="text"&&((l=u.getAttribute("type"))==null||l.toLowerCase()==="text")},first:Ie(function(){return[0]}),last:Ie(function(u,l){return[l-1]}),eq:Ie(function(u,l,d){return[d<0?d+l:d]}),even:Ie(function(u,l){for(var d=0;d<l;d+=2)u.push(d);return u}),odd:Ie(function(u,l){for(var d=1;d<l;d+=2)u.push(d);return u}),lt:Ie(function(u,l,d){for(var m=d<0?d+l:d>l?l:d;--m>=0;)u.push(m);return u}),gt:Ie(function(u,l,d){for(var m=d<0?d+l:d;++m<l;)u.push(m);return u})}},i.pseudos.nth=i.pseudos.eq;for(t in{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})i.pseudos[t]=tr(t);for(t in{submit:!0,reset:!0})i.pseudos[t]=nr(t);function Fn(){}Fn.prototype=i.filters=i.pseudos,i.setFilters=new Fn,a=U.tokenize=function(u,l){var d,m,p,b,C,E,S,P=ot[u+" "];if(P)return l?0:P.slice(0);for(C=u,E=[],S=i.preFilter;C;){(!d||(m=Ui.exec(C)))&&(m&&(C=C.slice(m[0].length)||C),E.push(p=[])),d=!1,(m=Rn.exec(C))&&(d=m.shift(),p.push({value:d,type:m[0].replace(mt," ")}),C=C.slice(d.length));for(b in i.filter)(m=bt[b].exec(C))&&(!S[b]||(m=S[b](m)))&&(d=m.shift(),p.push({value:d,type:b,matches:m}),C=C.slice(d.length));if(!d)break}return l?C.length:C?U.error(u):ot(u,E).slice(0)};function Ct(u){for(var l=0,d=u.length,m="";l<d;l++)m+=u[l].value;return m}function Tt(u,l,d){var m=l.dir,p=l.next,b=p||m,C=d&&b==="parentNode",E=B++;return l.first?function(S,P,q){for(;S=S[m];)if(S.nodeType===1||C)return u(S,P,q);return!1}:function(S,P,q){var M,_,$,O=[se,E];if(q){for(;S=S[m];)if((S.nodeType===1||C)&&u(S,P,q))return!0}else for(;S=S[m];)if(S.nodeType===1||C)if($=S[z]||(S[z]={}),_=$[S.uniqueID]||($[S.uniqueID]={}),p&&p===S.nodeName.toLowerCase())S=S[m]||S;else{if((M=_[b])&&M[0]===se&&M[1]===E)return O[2]=M[2];if(_[b]=O,O[2]=u(S,P,q))return!0}return!1}}function $t(u){return u.length>1?function(l,d,m){for(var p=u.length;p--;)if(!u[p](l,d,m))return!1;return!0}:u[0]}function ir(u,l,d){for(var m=0,p=l.length;m<p;m++)U(u,l[m],d);return d}function wt(u,l,d,m,p){for(var b,C=[],E=0,S=u.length,P=l!=null;E<S;E++)(b=u[E])&&(!d||d(b,m,p))&&(C.push(b),P&&l.push(E));return C}function Vt(u,l,d,m,p,b){return m&&!m[z]&&(m=Vt(m)),p&&!p[z]&&(p=Vt(p,b)),ye(function(C,E,S,P){var q,M,_,$=[],O=[],ee=E.length,te=C||ir(l||"*",S.nodeType?[S]:S,[]),fe=u&&(C||!l)?wt(te,$,u,S,P):te,Q=d?p||(C?u:ee||m)?[]:E:fe;if(d&&d(fe,Q,S,P),m)for(q=wt(Q,O),m(q,[],S,P),M=q.length;M--;)(_=q[M])&&(Q[O[M]]=!(fe[O[M]]=_));if(C){if(p||u){if(p){for(q=[],M=Q.length;M--;)(_=Q[M])&&q.push(fe[M]=_);p(null,Q=[],q,P)}for(M=Q.length;M--;)(_=Q[M])&&(q=p?Me(C,_):$[M])>-1&&(C[q]=!(E[q]=_))}}else Q=wt(Q===E?Q.splice(ee,Q.length):Q),p?p(null,E,Q,P):De.apply(E,Q)})}function Qt(u){for(var l,d,m,p=u.length,b=i.relative[u[0].type],C=b||i.relative[" "],E=b?1:0,S=Tt(function(M){return M===l},C,!0),P=Tt(function(M){return Me(l,M)>-1},C,!0),q=[function(M,_,$){var O=!b&&($||_!==h)||((l=_).nodeType?S(M,_,$):P(M,_,$));return l=null,O}];E<p;E++)if(d=i.relative[u[E].type])q=[Tt($t(q),d)];else{if(d=i.filter[u[E].type].apply(null,u[E].matches),d[z]){for(m=++E;m<p&&!i.relative[u[m].type];m++);return Vt(E>1&&$t(q),E>1&&Ct(u.slice(0,E-1).concat({value:u[E-2].type===" "?"*":""})).replace(mt,"$1"),d,E<m&&Qt(u.slice(E,m)),m<p&&Qt(u=u.slice(m)),m<p&&Ct(u))}q.push(d)}return $t(q)}function rr(u,l){var d=l.length>0,m=u.length>0,p=function(b,C,E,S,P){var q,M,_,$=0,O="0",ee=b&&[],te=[],fe=h,Q=b||m&&i.find.TAG("*",P),ut=se+=fe==null?1:Math.random()||.1,ft=Q.length;for(P&&(h=C==v||C||P);O!==ft&&(q=Q[O])!=null;O++){if(m&&q){for(M=0,!C&&q.ownerDocument!=v&&(g(q),E=!X);_=u[M++];)if(_(q,C||v,E)){S.push(q);break}P&&(se=ut)}d&&((q=!_&&q)&&$--,b&&ee.push(q))}if($+=O,d&&O!==$){for(M=0;_=l[M++];)_(ee,te,C,E);if(b){if($>0)for(;O--;)ee[O]||te[O]||(te[O]=Ae.call(S));te=wt(te)}De.apply(S,te),P&&!b&&te.length>0&&$+l.length>1&&U.uniqueSort(S)}return P&&(se=ut,h=fe),ee};return d?ye(p):p}return c=U.compile=function(u,l){var d,m=[],p=[],b=vt[u+" "];if(!b){for(l||(l=a(u)),d=l.length;d--;)b=Qt(l[d]),b[z]?m.push(b):p.push(b);b=vt(u,rr(p,m)),b.selector=u}return b},f=U.select=function(u,l,d,m){var p,b,C,E,S,P=typeof u=="function"&&u,q=!m&&a(u=P.selector||u);if(d=d||[],q.length===1){if(b=q[0]=q[0].slice(0),b.length>2&&(C=b[0]).type==="ID"&&l.nodeType===9&&X&&i.relative[b[1].type]){if(l=(i.find.ID(C.matches[0].replace(Ee,Ne),l)||[])[0],l)P&&(l=l.parentNode);else return d;u=u.slice(b.shift().value.length)}for(p=bt.needsContext.test(u)?0:b.length;p--&&(C=b[p],!i.relative[E=C.type]);)if((S=i.find[E])&&(m=S(C.matches[0].replace(Ee,Ne),Yt.test(b[0].type)&&Ut(l.parentNode)||l))){if(b.splice(p,1),u=m.length&&Ct(b),!u)return De.apply(d,m),d;break}}return(P||c(u,q))(m,l,!X,d,!l||Yt.test(u)&&Ut(l.parentNode)||l),d},n.sortStable=z.split("").sort(Pe).join("")===z,n.detectDuplicates=!!x,g(),n.sortDetached=he(function(u){return u.compareDocumentPosition(v.createElement("fieldset"))&1}),he(function(u){return u.innerHTML="<a href='#'></a>",u.firstChild.getAttribute("href")==="#"})||zt("type|href|height|width",function(u,l,d){if(!d)return u.getAttribute(l,l.toLowerCase()==="type"?1:2)}),(!n.attributes||!he(function(u){return u.innerHTML="<input/>",u.firstChild.setAttribute("value",""),u.firstChild.getAttribute("value")===""}))&&zt("value",function(u,l,d){if(!d&&u.nodeName.toLowerCase()==="input")return u.defaultValue}),he(function(u){return u.getAttribute("disabled")==null})||zt(Bt,function(u,l,d){var m;if(!d)return u[l]===!0?l.toLowerCase():(m=u.getAttributeNode(l))&&m.specified?m.value:null}),U}(N);r.find=ke,r.expr=ke.selectors,r.expr[":"]=r.expr.pseudos,r.uniqueSort=r.unique=ke.uniqueSort,r.text=ke.getText,r.isXMLDoc=ke.isXML,r.contains=ke.contains,r.escapeSelector=ke.escape;var Fe=function(e,t,n){for(var i=[],s=n!==void 0;(e=e[t])&&e.nodeType!==9;)if(e.nodeType===1){if(s&&r(e).is(n))break;i.push(e)}return i},Gt=function(e,t){for(var n=[];e;e=e.nextSibling)e.nodeType===1&&e!==t&&n.push(e);return n},Jt=r.expr.match.needsContext;function ce(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}var Kt=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function St(e,t,n){return A(t)?r.grep(e,function(i,s){return!!t.call(i,s,i)!==n}):t.nodeType?r.grep(e,function(i){return i===t!==n}):typeof t!="string"?r.grep(e,function(i){return We.call(t,i)>-1!==n}):r.filter(t,e,n)}r.filter=function(e,t,n){var i=t[0];return n&&(e=":not("+e+")"),t.length===1&&i.nodeType===1?r.find.matchesSelector(i,e)?[i]:[]:r.find.matches(e,r.grep(t,function(s){return s.nodeType===1}))},r.fn.extend({find:function(e){var t,n,i=this.length,s=this;if(typeof e!="string")return this.pushStack(r(e).filter(function(){for(t=0;t<i;t++)if(r.contains(s[t],this))return!0}));for(n=this.pushStack([]),t=0;t<i;t++)r.find(e,s[t],n);return i>1?r.uniqueSort(n):n},filter:function(e){return this.pushStack(St(this,e||[],!1))},not:function(e){return this.pushStack(St(this,e||[],!0))},is:function(e){return!!St(this,typeof e=="string"&&Jt.test(e)?r(e):e||[],!1).length}});var Zt,Vn=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/,Qn=r.fn.init=function(e,t,n){var i,s;if(!e)return this;if(n=n||Zt,typeof e=="string")if(e[0]==="<"&&e[e.length-1]===">"&&e.length>=3?i=[null,e,null]:i=Vn.exec(e),i&&(i[1]||!t))if(i[1]){if(t=t instanceof r?t[0]:t,r.merge(this,r.parseHTML(i[1],t&&t.nodeType?t.ownerDocument||t:T,!0)),Kt.test(i[1])&&r.isPlainObject(t))for(i in t)A(this[i])?this[i](t[i]):this.attr(i,t[i]);return this}else return s=T.getElementById(i[2]),s&&(this[0]=s,this.length=1),this;else return!t||t.jquery?(t||n).find(e):this.constructor(t).find(e);else{if(e.nodeType)return this[0]=e,this.length=1,this;if(A(e))return n.ready!==void 0?n.ready(e):e(r)}return r.makeArray(e,this)};Qn.prototype=r.fn,Zt=r(T);var Gn=/^(?:parents|prev(?:Until|All))/,Jn={children:!0,contents:!0,next:!0,prev:!0};r.fn.extend({has:function(e){var t=r(e,this),n=t.length;return this.filter(function(){for(var i=0;i<n;i++)if(r.contains(this,t[i]))return!0})},closest:function(e,t){var n,i=0,s=this.length,o=[],a=typeof e!="string"&&r(e);if(!Jt.test(e)){for(;i<s;i++)for(n=this[i];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(a?a.index(n)>-1:n.nodeType===1&&r.find.matchesSelector(n,e))){o.push(n);break}}return this.pushStack(o.length>1?r.uniqueSort(o):o)},index:function(e){return e?typeof e=="string"?We.call(r(e),this[0]):We.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(r.uniqueSort(r.merge(this.get(),r(e,t))))},addBack:function(e){return this.add(e==null?this.prevObject:this.prevObject.filter(e))}});function en(e,t){for(;(e=e[t])&&e.nodeType!==1;);return e}r.each({parent:function(e){var t=e.parentNode;return t&&t.nodeType!==11?t:null},parents:function(e){return Fe(e,"parentNode")},parentsUntil:function(e,t,n){return Fe(e,"parentNode",n)},next:function(e){return en(e,"nextSibling")},prev:function(e){return en(e,"previousSibling")},nextAll:function(e){return Fe(e,"nextSibling")},prevAll:function(e){return Fe(e,"previousSibling")},nextUntil:function(e,t,n){return Fe(e,"nextSibling",n)},prevUntil:function(e,t,n){return Fe(e,"previousSibling",n)},siblings:function(e){return Gt((e.parentNode||{}).firstChild,e)},children:function(e){return Gt(e.firstChild)},contents:function(e){return e.contentDocument!=null&&Je(e.contentDocument)?e.contentDocument:(ce(e,"template")&&(e=e.content||e),r.merge([],e.childNodes))}},function(e,t){r.fn[e]=function(n,i){var s=r.map(this,t,n);return e.slice(-5)!=="Until"&&(i=n),i&&typeof i=="string"&&(s=r.filter(i,s)),this.length>1&&(Jn[e]||r.uniqueSort(s),Gn.test(e)&&s.reverse()),this.pushStack(s)}});var be=/[^\x20\t\r\n\f]+/g;function Kn(e){var t={};return r.each(e.match(be)||[],function(n,i){t[i]=!0}),t}r.Callbacks=function(e){e=typeof e=="string"?Kn(e):r.extend({},e);var t,n,i,s,o=[],a=[],c=-1,f=function(){for(s=s||e.once,i=t=!0;a.length;c=-1)for(n=a.shift();++c<o.length;)o[c].apply(n[0],n[1])===!1&&e.stopOnFalse&&(c=o.length,n=!1);e.memory||(n=!1),t=!1,s&&(n?o=[]:o="")},h={add:function(){return o&&(n&&!t&&(c=o.length-1,a.push(n)),function y(x){r.each(x,function(g,v){A(v)?(!e.unique||!h.has(v))&&o.push(v):v&&v.length&&me(v)!=="string"&&y(v)})}(arguments),n&&!t&&f()),this},remove:function(){return r.each(arguments,function(y,x){for(var g;(g=r.inArray(x,o,g))>-1;)o.splice(g,1),g<=c&&c--}),this},has:function(y){return y?r.inArray(y,o)>-1:o.length>0},empty:function(){return o&&(o=[]),this},disable:function(){return s=a=[],o=n="",this},disabled:function(){return!o},lock:function(){return s=a=[],!n&&!t&&(o=n=""),this},locked:function(){return!!s},fireWith:function(y,x){return s||(x=x||[],x=[y,x.slice?x.slice():x],a.push(x),t||f()),this},fire:function(){return h.fireWith(this,arguments),this},fired:function(){return!!i}};return h};function Ye(e){return e}function lt(e){throw e}function tn(e,t,n,i){var s;try{e&&A(s=e.promise)?s.call(e).done(t).fail(n):e&&A(s=e.then)?s.call(e,t,n):t.apply(void 0,[e].slice(i))}catch(o){n.apply(void 0,[o])}}r.extend({Deferred:function(e){var t=[["notify","progress",r.Callbacks("memory"),r.Callbacks("memory"),2],["resolve","done",r.Callbacks("once memory"),r.Callbacks("once memory"),0,"resolved"],["reject","fail",r.Callbacks("once memory"),r.Callbacks("once memory"),1,"rejected"]],n="pending",i={state:function(){return n},always:function(){return s.done(arguments).fail(arguments),this},catch:function(o){return i.then(null,o)},pipe:function(){var o=arguments;return r.Deferred(function(a){r.each(t,function(c,f){var h=A(o[f[4]])&&o[f[4]];s[f[1]](function(){var y=h&&h.apply(this,arguments);y&&A(y.promise)?y.promise().progress(a.notify).done(a.resolve).fail(a.reject):a[f[0]+"With"](this,h?[y]:arguments)})}),o=null}).promise()},then:function(o,a,c){var f=0;function h(y,x,g,v){return function(){var k=this,X=arguments,j=function(){var J,ae;if(!(y<f)){if(J=g.apply(k,X),J===x.promise())throw new TypeError("Thenable self-resolution");ae=J&&(typeof J=="object"||typeof J=="function")&&J.then,A(ae)?v?ae.call(J,h(f,x,Ye,v),h(f,x,lt,v)):(f++,ae.call(J,h(f,x,Ye,v),h(f,x,lt,v),h(f,x,Ye,x.notifyWith))):(g!==Ye&&(k=void 0,X=[J]),(v||x.resolveWith)(k,X))}},Z=v?j:function(){try{j()}catch(J){r.Deferred.exceptionHook&&r.Deferred.exceptionHook(J,Z.stackTrace),y+1>=f&&(g!==lt&&(k=void 0,X=[J]),x.rejectWith(k,X))}};y?Z():(r.Deferred.getStackHook&&(Z.stackTrace=r.Deferred.getStackHook()),N.setTimeout(Z))}}return r.Deferred(function(y){t[0][3].add(h(0,y,A(c)?c:Ye,y.notifyWith)),t[1][3].add(h(0,y,A(o)?o:Ye)),t[2][3].add(h(0,y,A(a)?a:lt))}).promise()},promise:function(o){return o!=null?r.extend(o,i):i}},s={};return r.each(t,function(o,a){var c=a[2],f=a[5];i[a[1]]=c.add,f&&c.add(function(){n=f},t[3-o][2].disable,t[3-o][3].disable,t[0][2].lock,t[0][3].lock),c.add(a[3].fire),s[a[0]]=function(){return s[a[0]+"With"](this===s?void 0:this,arguments),this},s[a[0]+"With"]=c.fireWith}),i.promise(s),e&&e.call(s,s),s},when:function(e){var t=arguments.length,n=t,i=Array(n),s=ve.call(arguments),o=r.Deferred(),a=function(c){return function(f){i[c]=this,s[c]=arguments.length>1?ve.call(arguments):f,--t||o.resolveWith(i,s)}};if(t<=1&&(tn(e,o.done(a(n)).resolve,o.reject,!t),o.state()==="pending"||A(s[n]&&s[n].then)))return o.then();for(;n--;)tn(s[n],a(n),o.reject);return o.promise()}});var Zn=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;r.Deferred.exceptionHook=function(e,t){N.console&&N.console.warn&&e&&Zn.test(e.name)&&N.console.warn("jQuery.Deferred exception: "+e.message,e.stack,t)},r.readyException=function(e){N.setTimeout(function(){throw e})};var At=r.Deferred();r.fn.ready=function(e){return At.then(e).catch(function(t){r.readyException(t)}),this},r.extend({isReady:!1,readyWait:1,ready:function(e){(e===!0?--r.readyWait:r.isReady)||(r.isReady=!0,!(e!==!0&&--r.readyWait>0)&&At.resolveWith(T,[r]))}}),r.ready.then=At.then;function ct(){T.removeEventListener("DOMContentLoaded",ct),N.removeEventListener("load",ct),r.ready()}T.readyState==="complete"||T.readyState!=="loading"&&!T.documentElement.doScroll?N.setTimeout(r.ready):(T.addEventListener("DOMContentLoaded",ct),N.addEventListener("load",ct));var Te=function(e,t,n,i,s,o,a){var c=0,f=e.length,h=n==null;if(me(n)==="object"){s=!0;for(c in n)Te(e,t,c,n[c],!0,o,a)}else if(i!==void 0&&(s=!0,A(i)||(a=!0),h&&(a?(t.call(e,i),t=null):(h=t,t=function(y,x,g){return h.call(r(y),g)})),t))for(;c<f;c++)t(e[c],n,a?i:i.call(e[c],c,t(e[c],n)));return s?e:h?t.call(e):f?t(e[0],n):o},ei=/^-ms-/,ti=/-([a-z])/g;function ni(e,t){return t.toUpperCase()}function xe(e){return e.replace(ei,"ms-").replace(ti,ni)}var Ke=function(e){return e.nodeType===1||e.nodeType===9||!+e.nodeType};function Ze(){this.expando=r.expando+Ze.uid++}Ze.uid=1,Ze.prototype={cache:function(e){var t=e[this.expando];return t||(t={},Ke(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var i,s=this.cache(e);if(typeof t=="string")s[xe(t)]=n;else for(i in t)s[xe(i)]=t[i];return s},get:function(e,t){return t===void 0?this.cache(e):e[this.expando]&&e[this.expando][xe(t)]},access:function(e,t,n){return t===void 0||t&&typeof t=="string"&&n===void 0?this.get(e,t):(this.set(e,t,n),n!==void 0?n:t)},remove:function(e,t){var n,i=e[this.expando];if(i!==void 0){if(t!==void 0)for(Array.isArray(t)?t=t.map(xe):(t=xe(t),t=t in i?[t]:t.match(be)||[]),n=t.length;n--;)delete i[t[n]];(t===void 0||r.isEmptyObject(i))&&(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){var t=e[this.expando];return t!==void 0&&!r.isEmptyObject(t)}};var H=new Ze,ne=new Ze,ii=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,ri=/[A-Z]/g;function si(e){return e==="true"?!0:e==="false"?!1:e==="null"?null:e===+e+""?+e:ii.test(e)?JSON.parse(e):e}function nn(e,t,n){var i;if(n===void 0&&e.nodeType===1)if(i="data-"+t.replace(ri,"-$&").toLowerCase(),n=e.getAttribute(i),typeof n=="string"){try{n=si(n)}catch(s){}ne.set(e,t,n)}else n=void 0;return n}r.extend({hasData:function(e){return ne.hasData(e)||H.hasData(e)},data:function(e,t,n){return ne.access(e,t,n)},removeData:function(e,t){ne.remove(e,t)},_data:function(e,t,n){return H.access(e,t,n)},_removeData:function(e,t){H.remove(e,t)}}),r.fn.extend({data:function(e,t){var n,i,s,o=this[0],a=o&&o.attributes;if(e===void 0){if(this.length&&(s=ne.get(o),o.nodeType===1&&!H.get(o,"hasDataAttrs"))){for(n=a.length;n--;)a[n]&&(i=a[n].name,i.indexOf("data-")===0&&(i=xe(i.slice(5)),nn(o,i,s[i])));H.set(o,"hasDataAttrs",!0)}return s}return typeof e=="object"?this.each(function(){ne.set(this,e)}):Te(this,function(c){var f;if(o&&c===void 0)return f=ne.get(o,e),f!==void 0||(f=nn(o,e),f!==void 0)?f:void 0;this.each(function(){ne.set(this,e,c)})},null,t,arguments.length>1,null,!0)},removeData:function(e){return this.each(function(){ne.remove(this,e)})}}),r.extend({queue:function(e,t,n){var i;if(e)return t=(t||"fx")+"queue",i=H.get(e,t),n&&(!i||Array.isArray(n)?i=H.access(e,t,r.makeArray(n)):i.push(n)),i||[]},dequeue:function(e,t){t=t||"fx";var n=r.queue(e,t),i=n.length,s=n.shift(),o=r._queueHooks(e,t),a=function(){r.dequeue(e,t)};s==="inprogress"&&(s=n.shift(),i--),s&&(t==="fx"&&n.unshift("inprogress"),delete o.stop,s.call(e,a,o)),!i&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return H.get(e,n)||H.access(e,n,{empty:r.Callbacks("once memory").add(function(){H.remove(e,[t+"queue",n])})})}}),r.fn.extend({queue:function(e,t){var n=2;return typeof e!="string"&&(t=e,e="fx",n--),arguments.length<n?r.queue(this[0],e):t===void 0?this:this.each(function(){var i=r.queue(this,e,t);r._queueHooks(this,e),e==="fx"&&i[0]!=="inprogress"&&r.dequeue(this,e)})},dequeue:function(e){return this.each(function(){r.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,i=1,s=r.Deferred(),o=this,a=this.length,c=function(){--i||s.resolveWith(o,[o])};for(typeof e!="string"&&(t=e,e=void 0),e=e||"fx";a--;)n=H.get(o[a],e+"queueHooks"),n&&n.empty&&(i++,n.empty.add(c));return c(),s.promise(t)}});var rn=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,et=new RegExp("^(?:([+-])=|)("+rn+")([a-z%]*)$","i"),we=["Top","Right","Bottom","Left"],qe=T.documentElement,ze=function(e){return r.contains(e.ownerDocument,e)},oi={composed:!0};qe.getRootNode&&(ze=function(e){return r.contains(e.ownerDocument,e)||e.getRootNode(oi)===e.ownerDocument});var dt=function(e,t){return e=t||e,e.style.display==="none"||e.style.display===""&&ze(e)&&r.css(e,"display")==="none"};function sn(e,t,n,i){var s,o,a=20,c=i?function(){return i.cur()}:function(){return r.css(e,t,"")},f=c(),h=n&&n[3]||(r.cssNumber[t]?"":"px"),y=e.nodeType&&(r.cssNumber[t]||h!=="px"&&+f)&&et.exec(r.css(e,t));if(y&&y[3]!==h){for(f=f/2,h=h||y[3],y=+f||1;a--;)r.style(e,t,y+h),(1-o)*(1-(o=c()/f||.5))<=0&&(a=0),y=y/o;y=y*2,r.style(e,t,y+h),n=n||[]}return n&&(y=+y||+f||0,s=n[1]?y+(n[1]+1)*n[2]:+n[2],i&&(i.unit=h,i.start=y,i.end=s)),s}var on={};function ai(e){var t,n=e.ownerDocument,i=e.nodeName,s=on[i];return s||(t=n.body.appendChild(n.createElement(i)),s=r.css(t,"display"),t.parentNode.removeChild(t),s==="none"&&(s="block"),on[i]=s,s)}function Ue(e,t){for(var n,i,s=[],o=0,a=e.length;o<a;o++)i=e[o],!!i.style&&(n=i.style.display,t?(n==="none"&&(s[o]=H.get(i,"display")||null,s[o]||(i.style.display="")),i.style.display===""&&dt(i)&&(s[o]=ai(i))):n!=="none"&&(s[o]="none",H.set(i,"display",n)));for(o=0;o<a;o++)s[o]!=null&&(e[o].style.display=s[o]);return e}r.fn.extend({show:function(){return Ue(this,!0)},hide:function(){return Ue(this)},toggle:function(e){return typeof e=="boolean"?e?this.show():this.hide():this.each(function(){dt(this)?r(this).show():r(this).hide()})}});var tt=/^(?:checkbox|radio)$/i,an=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,un=/^$|^module$|\/(?:java|ecma)script/i;(function(){var e=T.createDocumentFragment(),t=e.appendChild(T.createElement("div")),n=T.createElement("input");n.setAttribute("type","radio"),n.setAttribute("checked","checked"),n.setAttribute("name","t"),t.appendChild(n),D.checkClone=t.cloneNode(!0).cloneNode(!0).lastChild.checked,t.innerHTML="<textarea>x</textarea>",D.noCloneChecked=!!t.cloneNode(!0).lastChild.defaultValue,t.innerHTML="<option></option>",D.option=!!t.lastChild})();var de={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};de.tbody=de.tfoot=de.colgroup=de.caption=de.thead,de.th=de.td,D.option||(de.optgroup=de.option=[1,"<select multiple='multiple'>","</select>"]);function ie(e,t){var n;return typeof e.getElementsByTagName!="undefined"?n=e.getElementsByTagName(t||"*"):typeof e.querySelectorAll!="undefined"?n=e.querySelectorAll(t||"*"):n=[],t===void 0||t&&ce(e,t)?r.merge([e],n):n}function Dt(e,t){for(var n=0,i=e.length;n<i;n++)H.set(e[n],"globalEval",!t||H.get(t[n],"globalEval"))}var ui=/<|&#?\w+;/;function fn(e,t,n,i,s){for(var o,a,c,f,h,y,x=t.createDocumentFragment(),g=[],v=0,k=e.length;v<k;v++)if(o=e[v],o||o===0)if(me(o)==="object")r.merge(g,o.nodeType?[o]:o);else if(!ui.test(o))g.push(t.createTextNode(o));else{for(a=a||x.appendChild(t.createElement("div")),c=(an.exec(o)||["",""])[1].toLowerCase(),f=de[c]||de._default,a.innerHTML=f[1]+r.htmlPrefilter(o)+f[2],y=f[0];y--;)a=a.lastChild;r.merge(g,a.childNodes),a=x.firstChild,a.textContent=""}for(x.textContent="",v=0;o=g[v++];){if(i&&r.inArray(o,i)>-1){s&&s.push(o);continue}if(h=ze(o),a=ie(x.appendChild(o),"script"),h&&Dt(a),n)for(y=0;o=a[y++];)un.test(o.type||"")&&n.push(o)}return x}var ln=/^([^.]*)(?:\.(.+)|)/;function $e(){return!0}function Ve(){return!1}function fi(e,t){return e===li()==(t==="focus")}function li(){try{return T.activeElement}catch(e){}}function jt(e,t,n,i,s,o){var a,c;if(typeof t=="object"){typeof n!="string"&&(i=i||n,n=void 0);for(c in t)jt(e,c,n,i,t[c],o);return e}if(i==null&&s==null?(s=n,i=n=void 0):s==null&&(typeof n=="string"?(s=i,i=void 0):(s=i,i=n,n=void 0)),s===!1)s=Ve;else if(!s)return e;return o===1&&(a=s,s=function(f){return r().off(f),a.apply(this,arguments)},s.guid=a.guid||(a.guid=r.guid++)),e.each(function(){r.event.add(this,t,s,i,n)})}r.event={global:{},add:function(e,t,n,i,s){var o,a,c,f,h,y,x,g,v,k,X,j=H.get(e);if(!!Ke(e))for(n.handler&&(o=n,n=o.handler,s=o.selector),s&&r.find.matchesSelector(qe,s),n.guid||(n.guid=r.guid++),(f=j.events)||(f=j.events=Object.create(null)),(a=j.handle)||(a=j.handle=function(Z){return typeof r!="undefined"&&r.event.triggered!==Z.type?r.event.dispatch.apply(e,arguments):void 0}),t=(t||"").match(be)||[""],h=t.length;h--;)c=ln.exec(t[h])||[],v=X=c[1],k=(c[2]||"").split(".").sort(),!!v&&(x=r.event.special[v]||{},v=(s?x.delegateType:x.bindType)||v,x=r.event.special[v]||{},y=r.extend({type:v,origType:X,data:i,handler:n,guid:n.guid,selector:s,needsContext:s&&r.expr.match.needsContext.test(s),namespace:k.join(".")},o),(g=f[v])||(g=f[v]=[],g.delegateCount=0,(!x.setup||x.setup.call(e,i,k,a)===!1)&&e.addEventListener&&e.addEventListener(v,a)),x.add&&(x.add.call(e,y),y.handler.guid||(y.handler.guid=n.guid)),s?g.splice(g.delegateCount++,0,y):g.push(y),r.event.global[v]=!0)},remove:function(e,t,n,i,s){var o,a,c,f,h,y,x,g,v,k,X,j=H.hasData(e)&&H.get(e);if(!(!j||!(f=j.events))){for(t=(t||"").match(be)||[""],h=t.length;h--;){if(c=ln.exec(t[h])||[],v=X=c[1],k=(c[2]||"").split(".").sort(),!v){for(v in f)r.event.remove(e,v+t[h],n,i,!0);continue}for(x=r.event.special[v]||{},v=(i?x.delegateType:x.bindType)||v,g=f[v]||[],c=c[2]&&new RegExp("(^|\\.)"+k.join("\\.(?:.*\\.|)")+"(\\.|$)"),a=o=g.length;o--;)y=g[o],(s||X===y.origType)&&(!n||n.guid===y.guid)&&(!c||c.test(y.namespace))&&(!i||i===y.selector||i==="**"&&y.selector)&&(g.splice(o,1),y.selector&&g.delegateCount--,x.remove&&x.remove.call(e,y));a&&!g.length&&((!x.teardown||x.teardown.call(e,k,j.handle)===!1)&&r.removeEvent(e,v,j.handle),delete f[v])}r.isEmptyObject(f)&&H.remove(e,"handle events")}},dispatch:function(e){var t,n,i,s,o,a,c=new Array(arguments.length),f=r.event.fix(e),h=(H.get(this,"events")||Object.create(null))[f.type]||[],y=r.event.special[f.type]||{};for(c[0]=f,t=1;t<arguments.length;t++)c[t]=arguments[t];if(f.delegateTarget=this,!(y.preDispatch&&y.preDispatch.call(this,f)===!1)){for(a=r.event.handlers.call(this,f,h),t=0;(s=a[t++])&&!f.isPropagationStopped();)for(f.currentTarget=s.elem,n=0;(o=s.handlers[n++])&&!f.isImmediatePropagationStopped();)(!f.rnamespace||o.namespace===!1||f.rnamespace.test(o.namespace))&&(f.handleObj=o,f.data=o.data,i=((r.event.special[o.origType]||{}).handle||o.handler).apply(s.elem,c),i!==void 0&&(f.result=i)===!1&&(f.preventDefault(),f.stopPropagation()));return y.postDispatch&&y.postDispatch.call(this,f),f.result}},handlers:function(e,t){var n,i,s,o,a,c=[],f=t.delegateCount,h=e.target;if(f&&h.nodeType&&!(e.type==="click"&&e.button>=1)){for(;h!==this;h=h.parentNode||this)if(h.nodeType===1&&!(e.type==="click"&&h.disabled===!0)){for(o=[],a={},n=0;n<f;n++)i=t[n],s=i.selector+" ",a[s]===void 0&&(a[s]=i.needsContext?r(s,this).index(h)>-1:r.find(s,this,null,[h]).length),a[s]&&o.push(i);o.length&&c.push({elem:h,handlers:o})}}return h=this,f<t.length&&c.push({elem:h,handlers:t.slice(f)}),c},addProp:function(e,t){Object.defineProperty(r.Event.prototype,e,{enumerable:!0,configurable:!0,get:A(t)?function(){if(this.originalEvent)return t(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[e]},set:function(n){Object.defineProperty(this,e,{enumerable:!0,configurable:!0,writable:!0,value:n})}})},fix:function(e){return e[r.expando]?e:new r.Event(e)},special:{load:{noBubble:!0},click:{setup:function(e){var t=this||e;return tt.test(t.type)&&t.click&&ce(t,"input")&&pt(t,"click",$e),!1},trigger:function(e){var t=this||e;return tt.test(t.type)&&t.click&&ce(t,"input")&&pt(t,"click"),!0},_default:function(e){var t=e.target;return tt.test(t.type)&&t.click&&ce(t,"input")&&H.get(t,"click")||ce(t,"a")}},beforeunload:{postDispatch:function(e){e.result!==void 0&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}};function pt(e,t,n){if(!n){H.get(e,t)===void 0&&r.event.add(e,t,$e);return}H.set(e,t,!1),r.event.add(e,t,{namespace:!1,handler:function(i){var s,o,a=H.get(this,t);if(i.isTrigger&1&&this[t]){if(a.length)(r.event.special[t]||{}).delegateType&&i.stopPropagation();else if(a=ve.call(arguments),H.set(this,t,a),s=n(this,t),this[t](),o=H.get(this,t),a!==o||s?H.set(this,t,!1):o={},a!==o)return i.stopImmediatePropagation(),i.preventDefault(),o&&o.value}else a.length&&(H.set(this,t,{value:r.event.trigger(r.extend(a[0],r.Event.prototype),a.slice(1),this)}),i.stopImmediatePropagation())}})}r.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},r.Event=function(e,t){if(!(this instanceof r.Event))return new r.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||e.defaultPrevented===void 0&&e.returnValue===!1?$e:Ve,this.target=e.target&&e.target.nodeType===3?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&r.extend(this,t),this.timeStamp=e&&e.timeStamp||Date.now(),this[r.expando]=!0},r.Event.prototype={constructor:r.Event,isDefaultPrevented:Ve,isPropagationStopped:Ve,isImmediatePropagationStopped:Ve,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=$e,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=$e,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=$e,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},r.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},r.event.addProp),r.each({focus:"focusin",blur:"focusout"},function(e,t){r.event.special[e]={setup:function(){return pt(this,e,fi),!1},trigger:function(){return pt(this,e),!0},_default:function(n){return H.get(n.target,e)},delegateType:t}}),r.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,t){r.event.special[e]={delegateType:t,bindType:t,handle:function(n){var i,s=this,o=n.relatedTarget,a=n.handleObj;return(!o||o!==s&&!r.contains(s,o))&&(n.type=a.origType,i=a.handler.apply(this,arguments),n.type=t),i}}}),r.fn.extend({on:function(e,t,n,i){return jt(this,e,t,n,i)},one:function(e,t,n,i){return jt(this,e,t,n,i,1)},off:function(e,t,n){var i,s;if(e&&e.preventDefault&&e.handleObj)return i=e.handleObj,r(e.delegateTarget).off(i.namespace?i.origType+"."+i.namespace:i.origType,i.selector,i.handler),this;if(typeof e=="object"){for(s in e)this.off(s,t,e[s]);return this}return(t===!1||typeof t=="function")&&(n=t,t=void 0),n===!1&&(n=Ve),this.each(function(){r.event.remove(this,e,n,t)})}});var ci=/<script|<style|<link/i,di=/checked\s*(?:[^=]|=\s*.checked.)/i,pi=/^\s*<!\[CDATA\[|\]\]>\s*$/g;function cn(e,t){return ce(e,"table")&&ce(t.nodeType!==11?t:t.firstChild,"tr")&&r(e).children("tbody")[0]||e}function hi(e){return e.type=(e.getAttribute("type")!==null)+"/"+e.type,e}function gi(e){return(e.type||"").slice(0,5)==="true/"?e.type=e.type.slice(5):e.removeAttribute("type"),e}function dn(e,t){var n,i,s,o,a,c,f;if(t.nodeType===1){if(H.hasData(e)&&(o=H.get(e),f=o.events,f)){H.remove(t,"handle events");for(s in f)for(n=0,i=f[s].length;n<i;n++)r.event.add(t,s,f[s][n])}ne.hasData(e)&&(a=ne.access(e),c=r.extend({},a),ne.set(t,c))}}function yi(e,t){var n=t.nodeName.toLowerCase();n==="input"&&tt.test(e.type)?t.checked=e.checked:(n==="input"||n==="textarea")&&(t.defaultValue=e.defaultValue)}function Qe(e,t,n,i){t=Xe(t);var s,o,a,c,f,h,y=0,x=e.length,g=x-1,v=t[0],k=A(v);if(k||x>1&&typeof v=="string"&&!D.checkClone&&di.test(v))return e.each(function(X){var j=e.eq(X);k&&(t[0]=v.call(this,X,j.html())),Qe(j,t,n,i)});if(x&&(s=fn(t,e[0].ownerDocument,!1,e,i),o=s.firstChild,s.childNodes.length===1&&(s=o),o||i)){for(a=r.map(ie(s,"script"),hi),c=a.length;y<x;y++)f=s,y!==g&&(f=r.clone(f,!0,!0),c&&r.merge(a,ie(f,"script"))),n.call(e[y],f,y);if(c)for(h=a[a.length-1].ownerDocument,r.map(a,gi),y=0;y<c;y++)f=a[y],un.test(f.type||"")&&!H.access(f,"globalEval")&&r.contains(h,f)&&(f.src&&(f.type||"").toLowerCase()!=="module"?r._evalUrl&&!f.noModule&&r._evalUrl(f.src,{nonce:f.nonce||f.getAttribute("nonce")},h):Ce(f.textContent.replace(pi,""),f,h))}return e}function pn(e,t,n){for(var i,s=t?r.filter(t,e):e,o=0;(i=s[o])!=null;o++)!n&&i.nodeType===1&&r.cleanData(ie(i)),i.parentNode&&(n&&ze(i)&&Dt(ie(i,"script")),i.parentNode.removeChild(i));return e}r.extend({htmlPrefilter:function(e){return e},clone:function(e,t,n){var i,s,o,a,c=e.cloneNode(!0),f=ze(e);if(!D.noCloneChecked&&(e.nodeType===1||e.nodeType===11)&&!r.isXMLDoc(e))for(a=ie(c),o=ie(e),i=0,s=o.length;i<s;i++)yi(o[i],a[i]);if(t)if(n)for(o=o||ie(e),a=a||ie(c),i=0,s=o.length;i<s;i++)dn(o[i],a[i]);else dn(e,c);return a=ie(c,"script"),a.length>0&&Dt(a,!f&&ie(e,"script")),c},cleanData:function(e){for(var t,n,i,s=r.event.special,o=0;(n=e[o])!==void 0;o++)if(Ke(n)){if(t=n[H.expando]){if(t.events)for(i in t.events)s[i]?r.event.remove(n,i):r.removeEvent(n,i,t.handle);n[H.expando]=void 0}n[ne.expando]&&(n[ne.expando]=void 0)}}}),r.fn.extend({detach:function(e){return pn(this,e,!0)},remove:function(e){return pn(this,e)},text:function(e){return Te(this,function(t){return t===void 0?r.text(this):this.empty().each(function(){(this.nodeType===1||this.nodeType===11||this.nodeType===9)&&(this.textContent=t)})},null,e,arguments.length)},append:function(){return Qe(this,arguments,function(e){if(this.nodeType===1||this.nodeType===11||this.nodeType===9){var t=cn(this,e);t.appendChild(e)}})},prepend:function(){return Qe(this,arguments,function(e){if(this.nodeType===1||this.nodeType===11||this.nodeType===9){var t=cn(this,e);t.insertBefore(e,t.firstChild)}})},before:function(){return Qe(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return Qe(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;(e=this[t])!=null;t++)e.nodeType===1&&(r.cleanData(ie(e,!1)),e.textContent="");return this},clone:function(e,t){return e=e??!1,t=t??e,this.map(function(){return r.clone(this,e,t)})},html:function(e){return Te(this,function(t){var n=this[0]||{},i=0,s=this.length;if(t===void 0&&n.nodeType===1)return n.innerHTML;if(typeof t=="string"&&!ci.test(t)&&!de[(an.exec(t)||["",""])[1].toLowerCase()]){t=r.htmlPrefilter(t);try{for(;i<s;i++)n=this[i]||{},n.nodeType===1&&(r.cleanData(ie(n,!1)),n.innerHTML=t);n=0}catch(o){}}n&&this.empty().append(t)},null,e,arguments.length)},replaceWith:function(){var e=[];return Qe(this,arguments,function(t){var n=this.parentNode;r.inArray(this,e)<0&&(r.cleanData(ie(this)),n&&n.replaceChild(t,this))},e)}}),r.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,t){r.fn[e]=function(n){for(var i,s=[],o=r(n),a=o.length-1,c=0;c<=a;c++)i=c===a?this:this.clone(!0),r(o[c])[t](i),_e.apply(s,i.get());return this.pushStack(s)}});var kt=new RegExp("^("+rn+")(?!px)[a-z%]+$","i"),qt=/^--/,ht=function(e){var t=e.ownerDocument.defaultView;return(!t||!t.opener)&&(t=N),t.getComputedStyle(e)},hn=function(e,t,n){var i,s,o={};for(s in t)o[s]=e.style[s],e.style[s]=t[s];i=n.call(e);for(s in t)e.style[s]=o[s];return i},vi=new RegExp(we.join("|"),"i"),gn="[\\x20\\t\\r\\n\\f]",mi=new RegExp("^"+gn+"+|((?:^|[^\\\\])(?:\\\\.)*)"+gn+"+$","g");(function(){function e(){if(!!h){f.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",h.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",qe.appendChild(f).appendChild(h);var y=N.getComputedStyle(h);n=y.top!=="1%",c=t(y.marginLeft)===12,h.style.right="60%",o=t(y.right)===36,i=t(y.width)===36,h.style.position="absolute",s=t(h.offsetWidth/3)===12,qe.removeChild(f),h=null}}function t(y){return Math.round(parseFloat(y))}var n,i,s,o,a,c,f=T.createElement("div"),h=T.createElement("div");!h.style||(h.style.backgroundClip="content-box",h.cloneNode(!0).style.backgroundClip="",D.clearCloneStyle=h.style.backgroundClip==="content-box",r.extend(D,{boxSizingReliable:function(){return e(),i},pixelBoxStyles:function(){return e(),o},pixelPosition:function(){return e(),n},reliableMarginLeft:function(){return e(),c},scrollboxSize:function(){return e(),s},reliableTrDimensions:function(){var y,x,g,v;return a==null&&(y=T.createElement("table"),x=T.createElement("tr"),g=T.createElement("div"),y.style.cssText="position:absolute;left:-11111px;border-collapse:separate",x.style.cssText="border:1px solid",x.style.height="1px",g.style.height="9px",g.style.display="block",qe.appendChild(y).appendChild(x).appendChild(g),v=N.getComputedStyle(x),a=parseInt(v.height,10)+parseInt(v.borderTopWidth,10)+parseInt(v.borderBottomWidth,10)===x.offsetHeight,qe.removeChild(y)),a}}))})();function nt(e,t,n){var i,s,o,a,c=qt.test(t),f=e.style;return n=n||ht(e),n&&(a=n.getPropertyValue(t)||n[t],c&&a&&(a=a.replace(mi,"$1")||void 0),a===""&&!ze(e)&&(a=r.style(e,t)),!D.pixelBoxStyles()&&kt.test(a)&&vi.test(t)&&(i=f.width,s=f.minWidth,o=f.maxWidth,f.minWidth=f.maxWidth=f.width=a,a=n.width,f.width=i,f.minWidth=s,f.maxWidth=o)),a!==void 0?a+"":a}function yn(e,t){return{get:function(){if(e()){delete this.get;return}return(this.get=t).apply(this,arguments)}}}var vn=["Webkit","Moz","ms"],mn=T.createElement("div").style,bn={};function bi(e){for(var t=e[0].toUpperCase()+e.slice(1),n=vn.length;n--;)if(e=vn[n]+t,e in mn)return e}function Lt(e){var t=r.cssProps[e]||bn[e];return t||(e in mn?e:bn[e]=bi(e)||e)}var xi=/^(none|table(?!-c[ea]).+)/,Ci={position:"absolute",visibility:"hidden",display:"block"},xn={letterSpacing:"0",fontWeight:"400"};function Cn(e,t,n){var i=et.exec(t);return i?Math.max(0,i[2]-(n||0))+(i[3]||"px"):t}function Ht(e,t,n,i,s,o){var a=t==="width"?1:0,c=0,f=0;if(n===(i?"border":"content"))return 0;for(;a<4;a+=2)n==="margin"&&(f+=r.css(e,n+we[a],!0,s)),i?(n==="content"&&(f-=r.css(e,"padding"+we[a],!0,s)),n!=="margin"&&(f-=r.css(e,"border"+we[a]+"Width",!0,s))):(f+=r.css(e,"padding"+we[a],!0,s),n!=="padding"?f+=r.css(e,"border"+we[a]+"Width",!0,s):c+=r.css(e,"border"+we[a]+"Width",!0,s));return!i&&o>=0&&(f+=Math.max(0,Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-o-f-c-.5))||0),f}function Tn(e,t,n){var i=ht(e),s=!D.boxSizingReliable()||n,o=s&&r.css(e,"boxSizing",!1,i)==="border-box",a=o,c=nt(e,t,i),f="offset"+t[0].toUpperCase()+t.slice(1);if(kt.test(c)){if(!n)return c;c="auto"}return(!D.boxSizingReliable()&&o||!D.reliableTrDimensions()&&ce(e,"tr")||c==="auto"||!parseFloat(c)&&r.css(e,"display",!1,i)==="inline")&&e.getClientRects().length&&(o=r.css(e,"boxSizing",!1,i)==="border-box",a=f in e,a&&(c=e[f])),c=parseFloat(c)||0,c+Ht(e,t,n||(o?"border":"content"),a,i,c)+"px"}r.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=nt(e,"opacity");return n===""?"1":n}}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{},style:function(e,t,n,i){if(!(!e||e.nodeType===3||e.nodeType===8||!e.style)){var s,o,a,c=xe(t),f=qt.test(t),h=e.style;if(f||(t=Lt(c)),a=r.cssHooks[t]||r.cssHooks[c],n!==void 0){if(o=typeof n,o==="string"&&(s=et.exec(n))&&s[1]&&(n=sn(e,t,s),o="number"),n==null||n!==n)return;o==="number"&&!f&&(n+=s&&s[3]||(r.cssNumber[c]?"":"px")),!D.clearCloneStyle&&n===""&&t.indexOf("background")===0&&(h[t]="inherit"),(!a||!("set"in a)||(n=a.set(e,n,i))!==void 0)&&(f?h.setProperty(t,n):h[t]=n)}else return a&&"get"in a&&(s=a.get(e,!1,i))!==void 0?s:h[t]}},css:function(e,t,n,i){var s,o,a,c=xe(t),f=qt.test(t);return f||(t=Lt(c)),a=r.cssHooks[t]||r.cssHooks[c],a&&"get"in a&&(s=a.get(e,!0,n)),s===void 0&&(s=nt(e,t,i)),s==="normal"&&t in xn&&(s=xn[t]),n===""||n?(o=parseFloat(s),n===!0||isFinite(o)?o||0:s):s}}),r.each(["height","width"],function(e,t){r.cssHooks[t]={get:function(n,i,s){if(i)return xi.test(r.css(n,"display"))&&(!n.getClientRects().length||!n.getBoundingClientRect().width)?hn(n,Ci,function(){return Tn(n,t,s)}):Tn(n,t,s)},set:function(n,i,s){var o,a=ht(n),c=!D.scrollboxSize()&&a.position==="absolute",f=c||s,h=f&&r.css(n,"boxSizing",!1,a)==="border-box",y=s?Ht(n,t,s,h,a):0;return h&&c&&(y-=Math.ceil(n["offset"+t[0].toUpperCase()+t.slice(1)]-parseFloat(a[t])-Ht(n,t,"border",!1,a)-.5)),y&&(o=et.exec(i))&&(o[3]||"px")!=="px"&&(n.style[t]=i,i=r.css(n,t)),Cn(n,i,y)}}}),r.cssHooks.marginLeft=yn(D.reliableMarginLeft,function(e,t){if(t)return(parseFloat(nt(e,"marginLeft"))||e.getBoundingClientRect().left-hn(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+"px"}),r.each({margin:"",padding:"",border:"Width"},function(e,t){r.cssHooks[e+t]={expand:function(n){for(var i=0,s={},o=typeof n=="string"?n.split(" "):[n];i<4;i++)s[e+we[i]+t]=o[i]||o[i-2]||o[0];return s}},e!=="margin"&&(r.cssHooks[e+t].set=Cn)}),r.fn.extend({css:function(e,t){return Te(this,function(n,i,s){var o,a,c={},f=0;if(Array.isArray(i)){for(o=ht(n),a=i.length;f<a;f++)c[i[f]]=r.css(n,i[f],!1,o);return c}return s!==void 0?r.style(n,i,s):r.css(n,i)},e,t,arguments.length>1)}});function re(e,t,n,i,s){return new re.prototype.init(e,t,n,i,s)}r.Tween=re,re.prototype={constructor:re,init:function(e,t,n,i,s,o){this.elem=e,this.prop=n,this.easing=s||r.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=i,this.unit=o||(r.cssNumber[n]?"":"px")},cur:function(){var e=re.propHooks[this.prop];return e&&e.get?e.get(this):re.propHooks._default.get(this)},run:function(e){var t,n=re.propHooks[this.prop];return this.options.duration?this.pos=t=r.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):re.propHooks._default.set(this),this}},re.prototype.init.prototype=re.prototype,re.propHooks={_default:{get:function(e){var t;return e.elem.nodeType!==1||e.elem[e.prop]!=null&&e.elem.style[e.prop]==null?e.elem[e.prop]:(t=r.css(e.elem,e.prop,""),!t||t==="auto"?0:t)},set:function(e){r.fx.step[e.prop]?r.fx.step[e.prop](e):e.elem.nodeType===1&&(r.cssHooks[e.prop]||e.elem.style[Lt(e.prop)]!=null)?r.style(e.elem,e.prop,e.now+e.unit):e.elem[e.prop]=e.now}}},re.propHooks.scrollTop=re.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},r.easing={linear:function(e){return e},swing:function(e){return .5-Math.cos(e*Math.PI)/2},_default:"swing"},r.fx=re.prototype.init,r.fx.step={};var Ge,gt,Ti=/^(?:toggle|show|hide)$/,wi=/queueHooks$/;function Pt(){gt&&(T.hidden===!1&&N.requestAnimationFrame?N.requestAnimationFrame(Pt):N.setTimeout(Pt,r.fx.interval),r.fx.tick())}function wn(){return N.setTimeout(function(){Ge=void 0}),Ge=Date.now()}function yt(e,t){var n,i=0,s={height:e};for(t=t?1:0;i<4;i+=2-t)n=we[i],s["margin"+n]=s["padding"+n]=e;return t&&(s.opacity=s.width=e),s}function En(e,t,n){for(var i,s=(ge.tweeners[t]||[]).concat(ge.tweeners["*"]),o=0,a=s.length;o<a;o++)if(i=s[o].call(n,t,e))return i}function Ei(e,t,n){var i,s,o,a,c,f,h,y,x="width"in t||"height"in t,g=this,v={},k=e.style,X=e.nodeType&&dt(e),j=H.get(e,"fxshow");n.queue||(a=r._queueHooks(e,"fx"),a.unqueued==null&&(a.unqueued=0,c=a.empty.fire,a.empty.fire=function(){a.unqueued||c()}),a.unqueued++,g.always(function(){g.always(function(){a.unqueued--,r.queue(e,"fx").length||a.empty.fire()})}));for(i in t)if(s=t[i],Ti.test(s)){if(delete t[i],o=o||s==="toggle",s===(X?"hide":"show"))if(s==="show"&&j&&j[i]!==void 0)X=!0;else continue;v[i]=j&&j[i]||r.style(e,i)}if(f=!r.isEmptyObject(t),!(!f&&r.isEmptyObject(v))){x&&e.nodeType===1&&(n.overflow=[k.overflow,k.overflowX,k.overflowY],h=j&&j.display,h==null&&(h=H.get(e,"display")),y=r.css(e,"display"),y==="none"&&(h?y=h:(Ue([e],!0),h=e.style.display||h,y=r.css(e,"display"),Ue([e]))),(y==="inline"||y==="inline-block"&&h!=null)&&r.css(e,"float")==="none"&&(f||(g.done(function(){k.display=h}),h==null&&(y=k.display,h=y==="none"?"":y)),k.display="inline-block")),n.overflow&&(k.overflow="hidden",g.always(function(){k.overflow=n.overflow[0],k.overflowX=n.overflow[1],k.overflowY=n.overflow[2]})),f=!1;for(i in v)f||(j?"hidden"in j&&(X=j.hidden):j=H.access(e,"fxshow",{display:h}),o&&(j.hidden=!X),X&&Ue([e],!0),g.done(function(){X||Ue([e]),H.remove(e,"fxshow");for(i in v)r.style(e,i,v[i])})),f=En(X?j[i]:0,i,g),i in j||(j[i]=f.start,X&&(f.end=f.start,f.start=0))}}function Ni(e,t){var n,i,s,o,a;for(n in e)if(i=xe(n),s=t[i],o=e[n],Array.isArray(o)&&(s=o[1],o=e[n]=o[0]),n!==i&&(e[i]=o,delete e[n]),a=r.cssHooks[i],a&&"expand"in a){o=a.expand(o),delete e[i];for(n in o)n in e||(e[n]=o[n],t[n]=s)}else t[i]=s}function ge(e,t,n){var i,s,o=0,a=ge.prefilters.length,c=r.Deferred().always(function(){delete f.elem}),f=function(){if(s)return!1;for(var x=Ge||wn(),g=Math.max(0,h.startTime+h.duration-x),v=g/h.duration||0,k=1-v,X=0,j=h.tweens.length;X<j;X++)h.tweens[X].run(k);return c.notifyWith(e,[h,k,g]),k<1&&j?g:(j||c.notifyWith(e,[h,1,0]),c.resolveWith(e,[h]),!1)},h=c.promise({elem:e,props:r.extend({},t),opts:r.extend(!0,{specialEasing:{},easing:r.easing._default},n),originalProperties:t,originalOptions:n,startTime:Ge||wn(),duration:n.duration,tweens:[],createTween:function(x,g){var v=r.Tween(e,h.opts,x,g,h.opts.specialEasing[x]||h.opts.easing);return h.tweens.push(v),v},stop:function(x){var g=0,v=x?h.tweens.length:0;if(s)return this;for(s=!0;g<v;g++)h.tweens[g].run(1);return x?(c.notifyWith(e,[h,1,0]),c.resolveWith(e,[h,x])):c.rejectWith(e,[h,x]),this}}),y=h.props;for(Ni(y,h.opts.specialEasing);o<a;o++)if(i=ge.prefilters[o].call(h,e,y,h.opts),i)return A(i.stop)&&(r._queueHooks(h.elem,h.opts.queue).stop=i.stop.bind(i)),i;return r.map(y,En,h),A(h.opts.start)&&h.opts.start.call(e,h),h.progress(h.opts.progress).done(h.opts.done,h.opts.complete).fail(h.opts.fail).always(h.opts.always),r.fx.timer(r.extend(f,{elem:e,anim:h,queue:h.opts.queue})),h}r.Animation=r.extend(ge,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return sn(n.elem,e,et.exec(t),n),n}]},tweener:function(e,t){A(e)?(t=e,e=["*"]):e=e.match(be);for(var n,i=0,s=e.length;i<s;i++)n=e[i],ge.tweeners[n]=ge.tweeners[n]||[],ge.tweeners[n].unshift(t)},prefilters:[Ei],prefilter:function(e,t){t?ge.prefilters.unshift(e):ge.prefilters.push(e)}}),r.speed=function(e,t,n){var i=e&&typeof e=="object"?r.extend({},e):{complete:n||!n&&t||A(e)&&e,duration:e,easing:n&&t||t&&!A(t)&&t};return r.fx.off?i.duration=0:typeof i.duration!="number"&&(i.duration in r.fx.speeds?i.duration=r.fx.speeds[i.duration]:i.duration=r.fx.speeds._default),(i.queue==null||i.queue===!0)&&(i.queue="fx"),i.old=i.complete,i.complete=function(){A(i.old)&&i.old.call(this),i.queue&&r.dequeue(this,i.queue)},i},r.fn.extend({fadeTo:function(e,t,n,i){return this.filter(dt).css("opacity",0).show().end().animate({opacity:t},e,n,i)},animate:function(e,t,n,i){var s=r.isEmptyObject(e),o=r.speed(t,n,i),a=function(){var c=ge(this,r.extend({},e),o);(s||H.get(this,"finish"))&&c.stop(!0)};return a.finish=a,s||o.queue===!1?this.each(a):this.queue(o.queue,a)},stop:function(e,t,n){var i=function(s){var o=s.stop;delete s.stop,o(n)};return typeof e!="string"&&(n=t,t=e,e=void 0),t&&this.queue(e||"fx",[]),this.each(function(){var s=!0,o=e!=null&&e+"queueHooks",a=r.timers,c=H.get(this);if(o)c[o]&&c[o].stop&&i(c[o]);else for(o in c)c[o]&&c[o].stop&&wi.test(o)&&i(c[o]);for(o=a.length;o--;)a[o].elem===this&&(e==null||a[o].queue===e)&&(a[o].anim.stop(n),s=!1,a.splice(o,1));(s||!n)&&r.dequeue(this,e)})},finish:function(e){return e!==!1&&(e=e||"fx"),this.each(function(){var t,n=H.get(this),i=n[e+"queue"],s=n[e+"queueHooks"],o=r.timers,a=i?i.length:0;for(n.finish=!0,r.queue(this,e,[]),s&&s.stop&&s.stop.call(this,!0),t=o.length;t--;)o[t].elem===this&&o[t].queue===e&&(o[t].anim.stop(!0),o.splice(t,1));for(t=0;t<a;t++)i[t]&&i[t].finish&&i[t].finish.call(this);delete n.finish})}}),r.each(["toggle","show","hide"],function(e,t){var n=r.fn[t];r.fn[t]=function(i,s,o){return i==null||typeof i=="boolean"?n.apply(this,arguments):this.animate(yt(t,!0),i,s,o)}}),r.each({slideDown:yt("show"),slideUp:yt("hide"),slideToggle:yt("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,t){r.fn[e]=function(n,i,s){return this.animate(t,n,i,s)}}),r.timers=[],r.fx.tick=function(){var e,t=0,n=r.timers;for(Ge=Date.now();t<n.length;t++)e=n[t],!e()&&n[t]===e&&n.splice(t--,1);n.length||r.fx.stop(),Ge=void 0},r.fx.timer=function(e){r.timers.push(e),r.fx.start()},r.fx.interval=13,r.fx.start=function(){gt||(gt=!0,Pt())},r.fx.stop=function(){gt=null},r.fx.speeds={slow:600,fast:200,_default:400},r.fn.delay=function(e,t){return e=r.fx&&r.fx.speeds[e]||e,t=t||"fx",this.queue(t,function(n,i){var s=N.setTimeout(n,e);i.stop=function(){N.clearTimeout(s)}})},function(){var e=T.createElement("input"),t=T.createElement("select"),n=t.appendChild(T.createElement("option"));e.type="checkbox",D.checkOn=e.value!=="",D.optSelected=n.selected,e=T.createElement("input"),e.value="t",e.type="radio",D.radioValue=e.value==="t"}();var Nn,it=r.expr.attrHandle;r.fn.extend({attr:function(e,t){return Te(this,r.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each(function(){r.removeAttr(this,e)})}}),r.extend({attr:function(e,t,n){var i,s,o=e.nodeType;if(!(o===3||o===8||o===2)){if(typeof e.getAttribute=="undefined")return r.prop(e,t,n);if((o!==1||!r.isXMLDoc(e))&&(s=r.attrHooks[t.toLowerCase()]||(r.expr.match.bool.test(t)?Nn:void 0)),n!==void 0){if(n===null){r.removeAttr(e,t);return}return s&&"set"in s&&(i=s.set(e,n,t))!==void 0?i:(e.setAttribute(t,n+""),n)}return s&&"get"in s&&(i=s.get(e,t))!==null?i:(i=r.find.attr(e,t),i??void 0)}},attrHooks:{type:{set:function(e,t){if(!D.radioValue&&t==="radio"&&ce(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},removeAttr:function(e,t){var n,i=0,s=t&&t.match(be);if(s&&e.nodeType===1)for(;n=s[i++];)e.removeAttribute(n)}}),Nn={set:function(e,t,n){return t===!1?r.removeAttr(e,n):e.setAttribute(n,n),n}},r.each(r.expr.match.bool.source.match(/\w+/g),function(e,t){var n=it[t]||r.find.attr;it[t]=function(i,s,o){var a,c,f=s.toLowerCase();return o||(c=it[f],it[f]=a,a=n(i,s,o)!=null?f:null,it[f]=c),a}});var Si=/^(?:input|select|textarea|button)$/i,Ai=/^(?:a|area)$/i;r.fn.extend({prop:function(e,t){return Te(this,r.prop,e,t,arguments.length>1)},removeProp:function(e){return this.each(function(){delete this[r.propFix[e]||e]})}}),r.extend({prop:function(e,t,n){var i,s,o=e.nodeType;if(!(o===3||o===8||o===2))return(o!==1||!r.isXMLDoc(e))&&(t=r.propFix[t]||t,s=r.propHooks[t]),n!==void 0?s&&"set"in s&&(i=s.set(e,n,t))!==void 0?i:e[t]=n:s&&"get"in s&&(i=s.get(e,t))!==null?i:e[t]},propHooks:{tabIndex:{get:function(e){var t=r.find.attr(e,"tabindex");return t?parseInt(t,10):Si.test(e.nodeName)||Ai.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),D.optSelected||(r.propHooks.selected={get:function(e){var t=e.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null},set:function(e){var t=e.parentNode;t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex)}}),r.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){r.propFix[this.toLowerCase()]=this});function Le(e){var t=e.match(be)||[];return t.join(" ")}function He(e){return e.getAttribute&&e.getAttribute("class")||""}function Ot(e){return Array.isArray(e)?e:typeof e=="string"?e.match(be)||[]:[]}r.fn.extend({addClass:function(e){var t,n,i,s,o,a;return A(e)?this.each(function(c){r(this).addClass(e.call(this,c,He(this)))}):(t=Ot(e),t.length?this.each(function(){if(i=He(this),n=this.nodeType===1&&" "+Le(i)+" ",n){for(o=0;o<t.length;o++)s=t[o],n.indexOf(" "+s+" ")<0&&(n+=s+" ");a=Le(n),i!==a&&this.setAttribute("class",a)}}):this)},removeClass:function(e){var t,n,i,s,o,a;return A(e)?this.each(function(c){r(this).removeClass(e.call(this,c,He(this)))}):arguments.length?(t=Ot(e),t.length?this.each(function(){if(i=He(this),n=this.nodeType===1&&" "+Le(i)+" ",n){for(o=0;o<t.length;o++)for(s=t[o];n.indexOf(" "+s+" ")>-1;)n=n.replace(" "+s+" "," ");a=Le(n),i!==a&&this.setAttribute("class",a)}}):this):this.attr("class","")},toggleClass:function(e,t){var n,i,s,o,a=typeof e,c=a==="string"||Array.isArray(e);return A(e)?this.each(function(f){r(this).toggleClass(e.call(this,f,He(this),t),t)}):typeof t=="boolean"&&c?t?this.addClass(e):this.removeClass(e):(n=Ot(e),this.each(function(){if(c)for(o=r(this),s=0;s<n.length;s++)i=n[s],o.hasClass(i)?o.removeClass(i):o.addClass(i);else(e===void 0||a==="boolean")&&(i=He(this),i&&H.set(this,"__className__",i),this.setAttribute&&this.setAttribute("class",i||e===!1?"":H.get(this,"__className__")||""))}))},hasClass:function(e){var t,n,i=0;for(t=" "+e+" ";n=this[i++];)if(n.nodeType===1&&(" "+Le(He(n))+" ").indexOf(t)>-1)return!0;return!1}});var Di=/\r/g;r.fn.extend({val:function(e){var t,n,i,s=this[0];return arguments.length?(i=A(e),this.each(function(o){var a;this.nodeType===1&&(i?a=e.call(this,o,r(this).val()):a=e,a==null?a="":typeof a=="number"?a+="":Array.isArray(a)&&(a=r.map(a,function(c){return c==null?"":c+""})),t=r.valHooks[this.type]||r.valHooks[this.nodeName.toLowerCase()],(!t||!("set"in t)||t.set(this,a,"value")===void 0)&&(this.value=a))})):s?(t=r.valHooks[s.type]||r.valHooks[s.nodeName.toLowerCase()],t&&"get"in t&&(n=t.get(s,"value"))!==void 0?n:(n=s.value,typeof n=="string"?n.replace(Di,""):n??"")):void 0}}),r.extend({valHooks:{option:{get:function(e){var t=r.find.attr(e,"value");return t??Le(r.text(e))}},select:{get:function(e){var t,n,i,s=e.options,o=e.selectedIndex,a=e.type==="select-one",c=a?null:[],f=a?o+1:s.length;for(o<0?i=f:i=a?o:0;i<f;i++)if(n=s[i],(n.selected||i===o)&&!n.disabled&&(!n.parentNode.disabled||!ce(n.parentNode,"optgroup"))){if(t=r(n).val(),a)return t;c.push(t)}return c},set:function(e,t){for(var n,i,s=e.options,o=r.makeArray(t),a=s.length;a--;)i=s[a],(i.selected=r.inArray(r.valHooks.option.get(i),o)>-1)&&(n=!0);return n||(e.selectedIndex=-1),o}}}}),r.each(["radio","checkbox"],function(){r.valHooks[this]={set:function(e,t){if(Array.isArray(t))return e.checked=r.inArray(r(e).val(),t)>-1}},D.checkOn||(r.valHooks[this].get=function(e){return e.getAttribute("value")===null?"on":e.value})}),D.focusin="onfocusin"in N;var Sn=/^(?:focusinfocus|focusoutblur)$/,An=function(e){e.stopPropagation()};r.extend(r.event,{trigger:function(e,t,n,i){var s,o,a,c,f,h,y,x,g=[n||T],v=L.call(e,"type")?e.type:e,k=L.call(e,"namespace")?e.namespace.split("."):[];if(o=x=a=n=n||T,!(n.nodeType===3||n.nodeType===8)&&!Sn.test(v+r.event.triggered)&&(v.indexOf(".")>-1&&(k=v.split("."),v=k.shift(),k.sort()),f=v.indexOf(":")<0&&"on"+v,e=e[r.expando]?e:new r.Event(v,typeof e=="object"&&e),e.isTrigger=i?2:3,e.namespace=k.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+k.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=n),t=t==null?[e]:r.makeArray(t,[e]),y=r.event.special[v]||{},!(!i&&y.trigger&&y.trigger.apply(n,t)===!1))){if(!i&&!y.noBubble&&!R(n)){for(c=y.delegateType||v,Sn.test(c+v)||(o=o.parentNode);o;o=o.parentNode)g.push(o),a=o;a===(n.ownerDocument||T)&&g.push(a.defaultView||a.parentWindow||N)}for(s=0;(o=g[s++])&&!e.isPropagationStopped();)x=o,e.type=s>1?c:y.bindType||v,h=(H.get(o,"events")||Object.create(null))[e.type]&&H.get(o,"handle"),h&&h.apply(o,t),h=f&&o[f],h&&h.apply&&Ke(o)&&(e.result=h.apply(o,t),e.result===!1&&e.preventDefault());return e.type=v,!i&&!e.isDefaultPrevented()&&(!y._default||y._default.apply(g.pop(),t)===!1)&&Ke(n)&&f&&A(n[v])&&!R(n)&&(a=n[f],a&&(n[f]=null),r.event.triggered=v,e.isPropagationStopped()&&x.addEventListener(v,An),n[v](),e.isPropagationStopped()&&x.removeEventListener(v,An),r.event.triggered=void 0,a&&(n[f]=a)),e.result}},simulate:function(e,t,n){var i=r.extend(new r.Event,n,{type:e,isSimulated:!0});r.event.trigger(i,null,t)}}),r.fn.extend({trigger:function(e,t){return this.each(function(){r.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];if(n)return r.event.trigger(e,t,n,!0)}}),D.focusin||r.each({focus:"focusin",blur:"focusout"},function(e,t){var n=function(i){r.event.simulate(t,i.target,r.event.fix(i))};r.event.special[t]={setup:function(){var i=this.ownerDocument||this.document||this,s=H.access(i,t);s||i.addEventListener(e,n,!0),H.access(i,t,(s||0)+1)},teardown:function(){var i=this.ownerDocument||this.document||this,s=H.access(i,t)-1;s?H.access(i,t,s):(i.removeEventListener(e,n,!0),H.remove(i,t))}}});var rt=N.location,Dn={guid:Date.now()},Mt=/\?/;r.parseXML=function(e){var t,n;if(!e||typeof e!="string")return null;try{t=new N.DOMParser().parseFromString(e,"text/xml")}catch(i){}return n=t&&t.getElementsByTagName("parsererror")[0],(!t||n)&&r.error("Invalid XML: "+(n?r.map(n.childNodes,function(i){return i.textContent}).join(`
`):e)),t};var ji=/\[\]$/,jn=/\r?\n/g,ki=/^(?:submit|button|image|reset|file)$/i,qi=/^(?:input|select|textarea|keygen)/i;function Rt(e,t,n,i){var s;if(Array.isArray(t))r.each(t,function(o,a){n||ji.test(e)?i(e,a):Rt(e+"["+(typeof a=="object"&&a!=null?o:"")+"]",a,n,i)});else if(!n&&me(t)==="object")for(s in t)Rt(e+"["+s+"]",t[s],n,i);else i(e,t)}r.param=function(e,t){var n,i=[],s=function(o,a){var c=A(a)?a():a;i[i.length]=encodeURIComponent(o)+"="+encodeURIComponent(c??"")};if(e==null)return"";if(Array.isArray(e)||e.jquery&&!r.isPlainObject(e))r.each(e,function(){s(this.name,this.value)});else for(n in e)Rt(n,e[n],t,s);return i.join("&")},r.fn.extend({serialize:function(){return r.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=r.prop(this,"elements");return e?r.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!r(this).is(":disabled")&&qi.test(this.nodeName)&&!ki.test(e)&&(this.checked||!tt.test(e))}).map(function(e,t){var n=r(this).val();return n==null?null:Array.isArray(n)?r.map(n,function(i){return{name:t.name,value:i.replace(jn,`\r
`)}}):{name:t.name,value:n.replace(jn,`\r
`)}}).get()}});var Li=/%20/g,Hi=/#.*$/,Pi=/([?&])_=[^&]*/,Oi=/^(.*?):[ \t]*([^\r\n]*)$/mg,Mi=/^(?:about|app|app-storage|.+-extension|file|res|widget):$/,Ri=/^(?:GET|HEAD)$/,Ii=/^\/\//,kn={},It={},qn="*/".concat("*"),Xt=T.createElement("a");Xt.href=rt.href;function Ln(e){return function(t,n){typeof t!="string"&&(n=t,t="*");var i,s=0,o=t.toLowerCase().match(be)||[];if(A(n))for(;i=o[s++];)i[0]==="+"?(i=i.slice(1)||"*",(e[i]=e[i]||[]).unshift(n)):(e[i]=e[i]||[]).push(n)}}function Hn(e,t,n,i){var s={},o=e===It;function a(c){var f;return s[c]=!0,r.each(e[c]||[],function(h,y){var x=y(t,n,i);if(typeof x=="string"&&!o&&!s[x])return t.dataTypes.unshift(x),a(x),!1;if(o)return!(f=x)}),f}return a(t.dataTypes[0])||!s["*"]&&a("*")}function _t(e,t){var n,i,s=r.ajaxSettings.flatOptions||{};for(n in t)t[n]!==void 0&&((s[n]?e:i||(i={}))[n]=t[n]);return i&&r.extend(!0,e,i),e}function Xi(e,t,n){for(var i,s,o,a,c=e.contents,f=e.dataTypes;f[0]==="*";)f.shift(),i===void 0&&(i=e.mimeType||t.getResponseHeader("Content-Type"));if(i){for(s in c)if(c[s]&&c[s].test(i)){f.unshift(s);break}}if(f[0]in n)o=f[0];else{for(s in n){if(!f[0]||e.converters[s+" "+f[0]]){o=s;break}a||(a=s)}o=o||a}if(o)return o!==f[0]&&f.unshift(o),n[o]}function _i(e,t,n,i){var s,o,a,c,f,h={},y=e.dataTypes.slice();if(y[1])for(a in e.converters)h[a.toLowerCase()]=e.converters[a];for(o=y.shift();o;)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!f&&i&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),f=o,o=y.shift(),o){if(o==="*")o=f;else if(f!=="*"&&f!==o){if(a=h[f+" "+o]||h["* "+o],!a){for(s in h)if(c=s.split(" "),c[1]===o&&(a=h[f+" "+c[0]]||h["* "+c[0]],a)){a===!0?a=h[s]:h[s]!==!0&&(o=c[0],y.unshift(c[1]));break}}if(a!==!0)if(a&&e.throws)t=a(t);else try{t=a(t)}catch(x){return{state:"parsererror",error:a?x:"No conversion from "+f+" to "+o}}}}return{state:"success",data:t}}r.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:rt.href,type:"GET",isLocal:Mi.test(rt.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":qn,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":r.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?_t(_t(e,r.ajaxSettings),t):_t(r.ajaxSettings,e)},ajaxPrefilter:Ln(kn),ajaxTransport:Ln(It),ajax:function(e,t){typeof e=="object"&&(t=e,e=void 0),t=t||{};var n,i,s,o,a,c,f,h,y,x,g=r.ajaxSetup({},t),v=g.context||g,k=g.context&&(v.nodeType||v.jquery)?r(v):r.event,X=r.Deferred(),j=r.Callbacks("once memory"),Z=g.statusCode||{},J={},ae={},z="canceled",I={readyState:0,getResponseHeader:function(B){var G;if(f){if(!o)for(o={};G=Oi.exec(s);)o[G[1].toLowerCase()+" "]=(o[G[1].toLowerCase()+" "]||[]).concat(G[2]);G=o[B.toLowerCase()+" "]}return G==null?null:G.join(", ")},getAllResponseHeaders:function(){return f?s:null},setRequestHeader:function(B,G){return f==null&&(B=ae[B.toLowerCase()]=ae[B.toLowerCase()]||B,J[B]=G),this},overrideMimeType:function(B){return f==null&&(g.mimeType=B),this},statusCode:function(B){var G;if(B)if(f)I.always(B[I.status]);else for(G in B)Z[G]=[Z[G],B[G]];return this},abort:function(B){var G=B||z;return n&&n.abort(G),se(0,G),this}};if(X.promise(I),g.url=((e||g.url||rt.href)+"").replace(Ii,rt.protocol+"//"),g.type=t.method||t.type||g.method||g.type,g.dataTypes=(g.dataType||"*").toLowerCase().match(be)||[""],g.crossDomain==null){c=T.createElement("a");try{c.href=g.url,c.href=c.href,g.crossDomain=Xt.protocol+"//"+Xt.host!=c.protocol+"//"+c.host}catch(B){g.crossDomain=!0}}if(g.data&&g.processData&&typeof g.data!="string"&&(g.data=r.param(g.data,g.traditional)),Hn(kn,g,t,I),f)return I;h=r.event&&g.global,h&&r.active++==0&&r.event.trigger("ajaxStart"),g.type=g.type.toUpperCase(),g.hasContent=!Ri.test(g.type),i=g.url.replace(Hi,""),g.hasContent?g.data&&g.processData&&(g.contentType||"").indexOf("application/x-www-form-urlencoded")===0&&(g.data=g.data.replace(Li,"+")):(x=g.url.slice(i.length),g.data&&(g.processData||typeof g.data=="string")&&(i+=(Mt.test(i)?"&":"?")+g.data,delete g.data),g.cache===!1&&(i=i.replace(Pi,"$1"),x=(Mt.test(i)?"&":"?")+"_="+Dn.guid+++x),g.url=i+x),g.ifModified&&(r.lastModified[i]&&I.setRequestHeader("If-Modified-Since",r.lastModified[i]),r.etag[i]&&I.setRequestHeader("If-None-Match",r.etag[i])),(g.data&&g.hasContent&&g.contentType!==!1||t.contentType)&&I.setRequestHeader("Content-Type",g.contentType),I.setRequestHeader("Accept",g.dataTypes[0]&&g.accepts[g.dataTypes[0]]?g.accepts[g.dataTypes[0]]+(g.dataTypes[0]!=="*"?", "+qn+"; q=0.01":""):g.accepts["*"]);for(y in g.headers)I.setRequestHeader(y,g.headers[y]);if(g.beforeSend&&(g.beforeSend.call(v,I,g)===!1||f))return I.abort();if(z="abort",j.add(g.complete),I.done(g.success),I.fail(g.error),n=Hn(It,g,t,I),!n)se(-1,"No Transport");else{if(I.readyState=1,h&&k.trigger("ajaxSend",[I,g]),f)return I;g.async&&g.timeout>0&&(a=N.setTimeout(function(){I.abort("timeout")},g.timeout));try{f=!1,n.send(J,se)}catch(B){if(f)throw B;se(-1,B)}}function se(B,G,ot,vt){var ue,Pe,Oe,oe,Ae,pe=G;f||(f=!0,a&&N.clearTimeout(a),n=void 0,s=vt||"",I.readyState=B>0?4:0,ue=B>=200&&B<300||B===304,ot&&(oe=Xi(g,I,ot)),!ue&&r.inArray("script",g.dataTypes)>-1&&r.inArray("json",g.dataTypes)<0&&(g.converters["text script"]=function(){}),oe=_i(g,oe,I,ue),ue?(g.ifModified&&(Ae=I.getResponseHeader("Last-Modified"),Ae&&(r.lastModified[i]=Ae),Ae=I.getResponseHeader("etag"),Ae&&(r.etag[i]=Ae)),B===204||g.type==="HEAD"?pe="nocontent":B===304?pe="notmodified":(pe=oe.state,Pe=oe.data,Oe=oe.error,ue=!Oe)):(Oe=pe,(B||!pe)&&(pe="error",B<0&&(B=0))),I.status=B,I.statusText=(G||pe)+"",ue?X.resolveWith(v,[Pe,pe,I]):X.rejectWith(v,[I,pe,Oe]),I.statusCode(Z),Z=void 0,h&&k.trigger(ue?"ajaxSuccess":"ajaxError",[I,g,ue?Pe:Oe]),j.fireWith(v,[I,pe]),h&&(k.trigger("ajaxComplete",[I,g]),--r.active||r.event.trigger("ajaxStop")))}return I},getJSON:function(e,t,n){return r.get(e,t,n,"json")},getScript:function(e,t){return r.get(e,void 0,t,"script")}}),r.each(["get","post"],function(e,t){r[t]=function(n,i,s,o){return A(i)&&(o=o||s,s=i,i=void 0),r.ajax(r.extend({url:n,type:t,dataType:o,data:i,success:s},r.isPlainObject(n)&&n))}}),r.ajaxPrefilter(function(e){var t;for(t in e.headers)t.toLowerCase()==="content-type"&&(e.contentType=e.headers[t]||"")}),r._evalUrl=function(e,t,n){return r.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(i){r.globalEval(i,t,n)}})},r.fn.extend({wrapAll:function(e){var t;return this[0]&&(A(e)&&(e=e.call(this[0])),t=r(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){for(var n=this;n.firstElementChild;)n=n.firstElementChild;return n}).append(this)),this},wrapInner:function(e){return A(e)?this.each(function(t){r(this).wrapInner(e.call(this,t))}):this.each(function(){var t=r(this),n=t.contents();n.length?n.wrapAll(e):t.append(e)})},wrap:function(e){var t=A(e);return this.each(function(n){r(this).wrapAll(t?e.call(this,n):e)})},unwrap:function(e){return this.parent(e).not("body").each(function(){r(this).replaceWith(this.childNodes)}),this}}),r.expr.pseudos.hidden=function(e){return!r.expr.pseudos.visible(e)},r.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},r.ajaxSettings.xhr=function(){try{return new N.XMLHttpRequest}catch(e){}};var Wi={0:200,1223:204},st=r.ajaxSettings.xhr();D.cors=!!st&&"withCredentials"in st,D.ajax=st=!!st,r.ajaxTransport(function(e){var t,n;if(D.cors||st&&!e.crossDomain)return{send:function(i,s){var o,a=e.xhr();if(a.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(o in e.xhrFields)a[o]=e.xhrFields[o];e.mimeType&&a.overrideMimeType&&a.overrideMimeType(e.mimeType),!e.crossDomain&&!i["X-Requested-With"]&&(i["X-Requested-With"]="XMLHttpRequest");for(o in i)a.setRequestHeader(o,i[o]);t=function(c){return function(){t&&(t=n=a.onload=a.onerror=a.onabort=a.ontimeout=a.onreadystatechange=null,c==="abort"?a.abort():c==="error"?typeof a.status!="number"?s(0,"error"):s(a.status,a.statusText):s(Wi[a.status]||a.status,a.statusText,(a.responseType||"text")!=="text"||typeof a.responseText!="string"?{binary:a.response}:{text:a.responseText},a.getAllResponseHeaders()))}},a.onload=t(),n=a.onerror=a.ontimeout=t("error"),a.onabort!==void 0?a.onabort=n:a.onreadystatechange=function(){a.readyState===4&&N.setTimeout(function(){t&&n()})},t=t("abort");try{a.send(e.hasContent&&e.data||null)}catch(c){if(t)throw c}},abort:function(){t&&t()}}}),r.ajaxPrefilter(function(e){e.crossDomain&&(e.contents.script=!1)}),r.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return r.globalEval(e),e}}}),r.ajaxPrefilter("script",function(e){e.cache===void 0&&(e.cache=!1),e.crossDomain&&(e.type="GET")}),r.ajaxTransport("script",function(e){if(e.crossDomain||e.scriptAttrs){var t,n;return{send:function(i,s){t=r("<script>").attr(e.scriptAttrs||{}).prop({charset:e.scriptCharset,src:e.url}).on("load error",n=function(o){t.remove(),n=null,o&&s(o.type==="error"?404:200,o.type)}),T.head.appendChild(t[0])},abort:function(){n&&n()}}}});var Pn=[],Wt=/(=)\?(?=&|$)|\?\?/;r.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=Pn.pop()||r.expando+"_"+Dn.guid++;return this[e]=!0,e}}),r.ajaxPrefilter("json jsonp",function(e,t,n){var i,s,o,a=e.jsonp!==!1&&(Wt.test(e.url)?"url":typeof e.data=="string"&&(e.contentType||"").indexOf("application/x-www-form-urlencoded")===0&&Wt.test(e.data)&&"data");if(a||e.dataTypes[0]==="jsonp")return i=e.jsonpCallback=A(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,a?e[a]=e[a].replace(Wt,"$1"+i):e.jsonp!==!1&&(e.url+=(Mt.test(e.url)?"&":"?")+e.jsonp+"="+i),e.converters["script json"]=function(){return o||r.error(i+" was not called"),o[0]},e.dataTypes[0]="json",s=N[i],N[i]=function(){o=arguments},n.always(function(){s===void 0?r(N).removeProp(i):N[i]=s,e[i]&&(e.jsonpCallback=t.jsonpCallback,Pn.push(i)),o&&A(s)&&s(o[0]),o=s=void 0}),"script"}),D.createHTMLDocument=function(){var e=T.implementation.createHTMLDocument("").body;return e.innerHTML="<form></form><form></form>",e.childNodes.length===2}(),r.parseHTML=function(e,t,n){if(typeof e!="string")return[];typeof t=="boolean"&&(n=t,t=!1);var i,s,o;return t||(D.createHTMLDocument?(t=T.implementation.createHTMLDocument(""),i=t.createElement("base"),i.href=T.location.href,t.head.appendChild(i)):t=T),s=Kt.exec(e),o=!n&&[],s?[t.createElement(s[1])]:(s=fn([e],t,o),o&&o.length&&r(o).remove(),r.merge([],s.childNodes))},r.fn.load=function(e,t,n){var i,s,o,a=this,c=e.indexOf(" ");return c>-1&&(i=Le(e.slice(c)),e=e.slice(0,c)),A(t)?(n=t,t=void 0):t&&typeof t=="object"&&(s="POST"),a.length>0&&r.ajax({url:e,type:s||"GET",dataType:"html",data:t}).done(function(f){o=arguments,a.html(i?r("<div>").append(r.parseHTML(f)).find(i):f)}).always(n&&function(f,h){a.each(function(){n.apply(this,o||[f.responseText,h,f])})}),this},r.expr.pseudos.animated=function(e){return r.grep(r.timers,function(t){return e===t.elem}).length},r.offset={setOffset:function(e,t,n){var i,s,o,a,c,f,h,y=r.css(e,"position"),x=r(e),g={};y==="static"&&(e.style.position="relative"),c=x.offset(),o=r.css(e,"top"),f=r.css(e,"left"),h=(y==="absolute"||y==="fixed")&&(o+f).indexOf("auto")>-1,h?(i=x.position(),a=i.top,s=i.left):(a=parseFloat(o)||0,s=parseFloat(f)||0),A(t)&&(t=t.call(e,n,r.extend({},c))),t.top!=null&&(g.top=t.top-c.top+a),t.left!=null&&(g.left=t.left-c.left+s),"using"in t?t.using.call(e,g):x.css(g)}},r.fn.extend({offset:function(e){if(arguments.length)return e===void 0?this:this.each(function(s){r.offset.setOffset(this,e,s)});var t,n,i=this[0];if(!!i)return i.getClientRects().length?(t=i.getBoundingClientRect(),n=i.ownerDocument.defaultView,{top:t.top+n.pageYOffset,left:t.left+n.pageXOffset}):{top:0,left:0}},position:function(){if(!!this[0]){var e,t,n,i=this[0],s={top:0,left:0};if(r.css(i,"position")==="fixed")t=i.getBoundingClientRect();else{for(t=this.offset(),n=i.ownerDocument,e=i.offsetParent||n.documentElement;e&&(e===n.body||e===n.documentElement)&&r.css(e,"position")==="static";)e=e.parentNode;e&&e!==i&&e.nodeType===1&&(s=r(e).offset(),s.top+=r.css(e,"borderTopWidth",!0),s.left+=r.css(e,"borderLeftWidth",!0))}return{top:t.top-s.top-r.css(i,"marginTop",!0),left:t.left-s.left-r.css(i,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent;e&&r.css(e,"position")==="static";)e=e.offsetParent;return e||qe})}}),r.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(e,t){var n=t==="pageYOffset";r.fn[e]=function(i){return Te(this,function(s,o,a){var c;if(R(s)?c=s:s.nodeType===9&&(c=s.defaultView),a===void 0)return c?c[t]:s[o];c?c.scrollTo(n?c.pageXOffset:a,n?a:c.pageYOffset):s[o]=a},e,i,arguments.length)}}),r.each(["top","left"],function(e,t){r.cssHooks[t]=yn(D.pixelPosition,function(n,i){if(i)return i=nt(n,t),kt.test(i)?r(n).position()[t]+"px":i})}),r.each({Height:"height",Width:"width"},function(e,t){r.each({padding:"inner"+e,content:t,"":"outer"+e},function(n,i){r.fn[i]=function(s,o){var a=arguments.length&&(n||typeof s!="boolean"),c=n||(s===!0||o===!0?"margin":"border");return Te(this,function(f,h,y){var x;return R(f)?i.indexOf("outer")===0?f["inner"+e]:f.document.documentElement["client"+e]:f.nodeType===9?(x=f.documentElement,Math.max(f.body["scroll"+e],x["scroll"+e],f.body["offset"+e],x["offset"+e],x["client"+e])):y===void 0?r.css(f,h,c):r.style(f,h,y,c)},t,a?s:void 0,a)}})}),r.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){r.fn[t]=function(n){return this.on(t,n)}}),r.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,i){return this.on(t,e,n,i)},undelegate:function(e,t,n){return arguments.length===1?this.off(e,"**"):this.off(t,e||"**",n)},hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}}),r.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(e,t){r.fn[t]=function(n,i){return arguments.length>0?this.on(t,null,n,i):this.trigger(t)}});var Bi=/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g;r.proxy=function(e,t){var n,i,s;if(typeof t=="string"&&(n=e[t],t=e,e=n),!!A(e))return i=ve.call(arguments,2),s=function(){return e.apply(t||this,i.concat(ve.call(arguments)))},s.guid=e.guid=e.guid||r.guid++,s},r.holdReady=function(e){e?r.readyWait++:r.ready(!0)},r.isArray=Array.isArray,r.parseJSON=JSON.parse,r.nodeName=ce,r.isFunction=A,r.isWindow=R,r.camelCase=xe,r.type=me,r.now=Date.now,r.isNumeric=function(e){var t=r.type(e);return(t==="number"||t==="string")&&!isNaN(e-parseFloat(e))},r.trim=function(e){return e==null?"":(e+"").replace(Bi,"$1")},typeof define=="function"&&define.amd&&define("jquery",[],function(){return r});var Fi=N.jQuery,Yi=N.$;return r.noConflict=function(e){return N.$===r&&(N.$=Yi),e&&N.jQuery===r&&(N.jQuery=Fi),r},typeof K=="undefined"&&(N.jQuery=N.$=r),r})});var $n=Yn(()=>{(function(N,K,V,Je){var ve="ontouchstart"in V,Xe=V.dir=="rtl",_e=function(){var w=V.createElement("div"),L=V.documentElement;if(!("pointerEvents"in w.style))return!1;w.style.pointerEvents="auto",w.style.pointerEvents="x",L.appendChild(w);var Y=K.getComputedStyle&&K.getComputedStyle(w,"").pointerEvents==="auto";return L.removeChild(w),!!Y}(),We={listNodeName:"ol",itemNodeName:"li",rootClass:"dd",listClass:"dd-list",itemClass:"dd-item",dragClass:"dd-dragel",handleClass:"dd-handle",collapsedClass:"dd-collapsed",placeClass:"dd-placeholder",noDragClass:"dd-nodrag",emptyClass:"dd-empty",expandBtnHTML:'<button data-action="expand" type="button">Expand</button>',collapseBtnHTML:'<button data-action="collapse" type="button">Collapse</button>',group:0,maxDepth:5,threshold:20};function je(w,L){this.w=N(V),this.el=N(w),this.options=N.extend({},We,L),this.init()}je.prototype={init:function(){var w=this;w.reset(),w.el.data("nestable-group",this.options.group),w.placeEl=N('<div class="'+w.options.placeClass+'"/>'),N.each(this.el.find(w.options.itemNodeName),function(D,A){w.setParent(N(A))}),w.el.on("click","button",function(D){if(!w.dragEl){var A=N(D.currentTarget),R=A.data("action"),T=A.parent(w.options.itemNodeName);R==="collapse"&&w.collapseItem(T),R==="expand"&&w.expandItem(T)}});var L=function(D){var A=N(D.target);if(!A.hasClass(w.options.handleClass)){if(A.closest("."+w.options.noDragClass).length)return;A=A.closest("."+w.options.handleClass)}!A.length||w.dragEl||(w.isTouch=/^touch/.test(D.type),!(w.isTouch&&D.touches.length!==1)&&(D.preventDefault(),w.dragStart(D.touches?D.touches[0]:D)))},Y=function(D){w.dragEl&&(D.preventDefault(),w.dragMove(D.touches?D.touches[0]:D))},W=function(D){w.dragEl&&(D.preventDefault(),w.dragStop(D.touches?D.touches[0]:D))};ve&&(w.el[0].addEventListener("touchstart",L,!1),K.addEventListener("touchmove",Y,!1),K.addEventListener("touchend",W,!1),K.addEventListener("touchcancel",W,!1)),w.el.on("mousedown",L),w.w.on("mousemove",Y),w.w.on("mouseup",W)},serialize:function(){var w,L=0,Y=this;return step=function(W,D){var A=[],R=W.children(Y.options.itemNodeName);return R.each(function(){var T=N(this),Se=N.extend({},T.data()),Ce=T.children(Y.options.listNodeName);Ce.length&&(Se.children=step(Ce,D+1)),A.push(Se)}),A},w=step(Y.el.find(Y.options.listNodeName).first(),L),w},serialise:function(){return this.serialize()},reset:function(){this.mouse={offsetX:0,offsetY:0,startX:0,startY:0,lastX:0,lastY:0,nowX:0,nowY:0,distX:0,distY:0,dirAx:0,dirX:0,dirY:0,lastDirX:0,lastDirY:0,distAxX:0,distAxY:0},this.isTouch=!1,this.moving=!1,this.dragEl=null,this.dragRootEl=null,this.dragDepth=0,this.hasNewRoot=!1,this.pointEl=null},expandItem:function(w){w.removeClass(this.options.collapsedClass),w.children('[data-action="expand"]').hide(),w.children('[data-action="collapse"]').show(),w.children(this.options.listNodeName).show()},collapseItem:function(w){var L=w.children(this.options.listNodeName);L.length&&(w.addClass(this.options.collapsedClass),w.children('[data-action="collapse"]').hide(),w.children('[data-action="expand"]').show(),w.children(this.options.listNodeName).hide())},expandAll:function(){var w=this;w.el.find(w.options.itemNodeName).each(function(){w.expandItem(N(this))})},collapseAll:function(){var w=this;w.el.find(w.options.itemNodeName).each(function(){w.collapseItem(N(this))})},setParent:function(w){w.children(this.options.listNodeName).length&&(w.prepend(N(this.options.expandBtnHTML)),w.prepend(N(this.options.collapseBtnHTML))),w.children('[data-action="expand"]').hide()},unsetParent:function(w){w.removeClass(this.options.collapsedClass),w.children("[data-action]").remove(),w.children(this.options.listNodeName).remove()},dragStart:function(w){var L=this.mouse,Y=N(w.target),W=Y.closest(this.options.itemNodeName);this.placeEl.css("height",W.height()),L.offsetX=w.offsetX!==Je?w.offsetX:w.pageX-Y.offset().left,L.offsetY=w.offsetY!==Je?w.offsetY:w.pageY-Y.offset().top,L.startX=L.lastX=w.pageX,L.startY=L.lastY=w.pageY,this.dragRootEl=this.el,this.dragEl=N(V.createElement(this.options.listNodeName)).addClass(this.options.listClass+" "+this.options.dragClass),this.dragEl.css("width",W.width()),W.after(this.placeEl),W[0].parentNode.removeChild(W[0]),W.appendTo(this.dragEl),N(V.body).append(this.dragEl),Xe?this.dragEl.css({right:N(V).width()-w.pageX-L.offsetX,top:w.pageY-L.offsetY}):this.dragEl.css({left:w.pageX-L.offsetX,top:w.pageY-L.offsetY});var D,A,R=this.dragEl.find(this.options.itemNodeName);for(D=0;D<R.length;D++)A=N(R[D]).parents(this.options.listNodeName).length,A>this.dragDepth&&(this.dragDepth=A)},dragStop:function(w){var L=this.dragEl.children(this.options.itemNodeName).first();L[0].parentNode.removeChild(L[0]),this.placeEl.replaceWith(L),this.dragEl.remove(),this.el.trigger("change"),this.hasNewRoot&&this.dragRootEl.trigger("change"),this.reset()},dragMove:function(w){var L,Y,W,D,A,R=this.options,T=this.mouse;Xe?this.dragEl.css({right:N(K).width()-w.pageX-T.offsetX,top:w.pageY-T.offsetY}):this.dragEl.css({left:w.pageX-T.offsetX,top:w.pageY-T.offsetY}),T.lastX=T.nowX,T.lastY=T.nowY,T.nowX=w.pageX,T.nowY=w.pageY,T.distX=T.nowX-T.lastX,T.distY=T.nowY-T.lastY,T.lastDirX=T.dirX,T.lastDirY=T.dirY,T.dirX=T.distX===0?0:T.distX>0?1:-1,T.dirY=T.distY===0?0:T.distY>0?1:-1;var Se=Math.abs(T.distX)>Math.abs(T.distY)?1:0;if(!T.moving){T.dirAx=Se,T.moving=!0;return}T.dirAx!==Se?(T.distAxX=0,T.distAxY=0):(T.distAxX+=Math.abs(T.distX),T.dirX!==0&&T.dirX!==T.lastDirX&&(T.distAxX=0),T.distAxY+=Math.abs(T.distY),T.dirY!==0&&T.dirY!==T.lastDirY&&(T.distAxY=0)),T.dirAx=Se,Xe?T.dirAx&&T.distAxX<=R.threshold&&(T.distAxX=0,W=this.placeEl.prev(R.itemNodeName),T.distX<0&&W.length&&!W.hasClass(R.collapsedClass)&&(L=W.find(R.listNodeName).last(),A=this.placeEl.parents(R.listNodeName).length,A+this.dragDepth>=R.maxDepth&&(L.length?(L=W.children(R.listNodeName).last(),L.append(this.placeEl)):(L=N("<"+R.listNodeName+"/>").addClass(R.listClass),L.append(this.placeEl),W.append(L),this.setParent(W)))),T.distX>0&&(D=this.placeEl.next(R.itemNodeName),D.length||(Y=this.placeEl.parent(),this.placeEl.closest(R.itemNodeName).after(this.placeEl),Y.children().length||this.unsetParent(Y.parent())))):T.dirAx&&T.distAxX>=R.threshold&&(T.distAxX=0,W=this.placeEl.prev(R.itemNodeName),T.distX>0&&W.length&&!W.hasClass(R.collapsedClass)&&(L=W.find(R.listNodeName).last(),A=this.placeEl.parents(R.listNodeName).length,A+this.dragDepth>=R.maxDepth&&(L.length?(L=W.children(R.listNodeName).last(),L.append(this.placeEl)):(L=N("<"+R.listNodeName+"/>").addClass(R.listClass),L.append(this.placeEl),W.append(L),this.setParent(W)))),T.distX<0&&(D=this.placeEl.next(R.itemNodeName),D.length||(Y=this.placeEl.parent(),this.placeEl.closest(R.itemNodeName).after(this.placeEl),Y.children().length||this.unsetParent(Y.parent()))));var Ce=!1;if(_e||(this.dragEl[0].style.visibility="hidden"),this.pointEl=N(V.elementFromPoint(w.pageX-V.body.scrollLeft,w.pageY-(K.pageYOffset||V.documentElement.scrollTop))),_e||(this.dragEl[0].style.visibility="visible"),this.pointEl.hasClass(R.handleClass)&&(this.pointEl=this.pointEl.parent(R.itemNodeName)),this.pointEl.hasClass(R.emptyClass))Ce=!0;else if(!this.pointEl.length||!this.pointEl.hasClass(R.itemClass))return;var me=this.pointEl.closest("."+R.rootClass),Be=this.dragRootEl.data("nestable-id")!==me.data("nestable-id");if(!T.dirAx||Be||Ce){if(Be&&R.group!==me.data("nestable-group")||(A=this.dragDepth-1+this.pointEl.parents(R.listNodeName).length,A>R.maxDepth))return;var r=w.pageY<this.pointEl.offset().top+this.pointEl.height()/2;Y=this.placeEl.parent(),Ce?(L=N(V.createElement(R.listNodeName)).addClass(R.listClass),L.append(this.placeEl),this.pointEl.replaceWith(L)):r?this.pointEl.before(this.placeEl):this.pointEl.after(this.placeEl),Y.children().length||this.unsetParent(Y.parent()),this.dragRootEl.find(R.itemNodeName).length||this.dragRootEl.append('<div class="'+R.emptyClass+'"/>'),Be&&(this.dragRootEl=me,this.hasNewRoot=this.el[0]!==this.dragRootEl[0])}}},N.fn.nestable=function(w){var L=this,Y=this;return L.each(function(){var W=N(this).data("nestable");W?typeof w=="string"&&typeof W[w]=="function"&&(Y=W[w]()):(N(this).data("nestable",new je(this,w)),N(this).data("nestable-id",new Date().getTime()))}),Y||L}})(window.jQuery||window.Zepto,window,document)});var ur=Un()(window);$n();})();
/*!
 * Nestable jQuery Plugin - Copyright (c) 2012 David Bushell - http://dbushell.com/
 * Dual-licensed under the BSD or MIT licenses
 */
/*!
 * jQuery JavaScript Library v3.6.4
 * https://jquery.com/
 *
 * Includes Sizzle.js
 * https://sizzlejs.com/
 *
 * Copyright OpenJS Foundation and other contributors
 * Released under the MIT license
 * https://jquery.org/license
 *
 * Date: 2023-03-08T15:28Z
 */

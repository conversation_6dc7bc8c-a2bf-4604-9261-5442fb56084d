/*!
 * lightgallery | 2.7.2 | September 20th 2023
 * http://www.lightgalleryjs.com/
 * Copyright (c) 2020 Sachin Neravath;
 * @license GPLv3
 *//*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var T=function(){return T=Object.assign||function(t){for(var e,i=1,s=arguments.length;i<s;i++){e=arguments[i];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])}return t},T.apply(this,arguments)};function Z(){for(var o=0,t=0,e=arguments.length;t<e;t++)o+=arguments[t].length;for(var i=Array(o),s=0,t=0;t<e;t++)for(var r=arguments[t],n=0,a=r.length;n<a;n++,s++)i[s]=r[n];return i}var v={afterAppendSlide:"lgAfterAppendSlide",init:"lgInit",hasVideo:"lgHasVideo",containerResize:"lgContainerResize",updateSlides:"lgUpdateSlides",afterAppendSubHtml:"lgAfterAppendSubHtml",beforeOpen:"lgBeforeOpen",afterOpen:"lgAfterOpen",slideItemLoad:"lgSlideItemLoad",beforeSlide:"lgBeforeSlide",afterSlide:"lgAfterSlide",posterClick:"lgPosterClick",dragStart:"lgDragStart",dragMove:"lgDragMove",dragEnd:"lgDragEnd",beforeNextSlide:"lgBeforeNextSlide",beforePrevSlide:"lgBeforePrevSlide",beforeClose:"lgBeforeClose",afterClose:"lgAfterClose",rotateLeft:"lgRotateLeft",rotateRight:"lgRotateRight",flipHorizontal:"lgFlipHorizontal",flipVertical:"lgFlipVertical",autoplay:"lgAutoplay",autoplayStart:"lgAutoplayStart",autoplayStop:"lgAutoplayStop"},q={mode:"lg-slide",easing:"ease",speed:400,licenseKey:"0000-0000-000-0000",height:"100%",width:"100%",addClass:"",startClass:"lg-start-zoom",backdropDuration:300,container:"",startAnimationDuration:400,zoomFromOrigin:!0,hideBarsDelay:0,showBarsAfter:1e4,slideDelay:0,supportLegacyBrowser:!0,allowMediaOverlap:!1,videoMaxSize:"1280-720",loadYouTubePoster:!0,defaultCaptionHeight:0,ariaLabelledby:"",ariaDescribedby:"",resetScrollPosition:!0,hideScrollbar:!1,closable:!0,swipeToClose:!0,closeOnTap:!0,showCloseIcon:!0,showMaximizeIcon:!1,loop:!0,escKey:!0,keyPress:!0,trapFocus:!0,controls:!0,slideEndAnimation:!0,hideControlOnEnd:!1,mousewheel:!1,getCaptionFromTitleOrAlt:!0,appendSubHtmlTo:".lg-sub-html",subHtmlSelectorRelative:!1,preload:2,numberOfSlideItemsInDom:10,selector:"",selectWithin:"",nextHtml:"",prevHtml:"",index:0,iframeWidth:"100%",iframeHeight:"100%",iframeMaxWidth:"100%",iframeMaxHeight:"100%",download:!0,counter:!0,appendCounterTo:".lg-toolbar",swipeThreshold:50,enableSwipe:!0,enableDrag:!0,dynamic:!1,dynamicEl:[],extraProps:[],exThumbImage:"",isMobile:void 0,mobileSettings:{controls:!1,showCloseIcon:!1,download:!1},plugins:[],strings:{closeGallery:"Close gallery",toggleMaximize:"Toggle maximize",previousSlide:"Previous slide",nextSlide:"Next slide",download:"Download",playVideo:"Play video",mediaLoadingFailed:"Oops... Failed to load content..."}};function U(){(function(){if(typeof window.CustomEvent=="function")return!1;function o(t,e){e=e||{bubbles:!1,cancelable:!1,detail:null};var i=document.createEvent("CustomEvent");return i.initCustomEvent(t,e.bubbles,e.cancelable,e.detail),i}window.CustomEvent=o})(),function(){Element.prototype.matches||(Element.prototype.matches=Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector)}()}var V=function(){function o(t){return this.cssVenderPrefixes=["TransitionDuration","TransitionTimingFunction","Transform","Transition"],this.selector=this._getSelector(t),this.firstElement=this._getFirstEl(),this}return o.generateUUID=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(t){var e=Math.random()*16|0,i=t=="x"?e:e&3|8;return i.toString(16)})},o.prototype._getSelector=function(t,e){if(e===void 0&&(e=document),typeof t!="string")return t;e=e||document;var i=t.substring(0,1);return i==="#"?e.querySelector(t):e.querySelectorAll(t)},o.prototype._each=function(t){return this.selector?(this.selector.length!==void 0?[].forEach.call(this.selector,t):t(this.selector,0),this):this},o.prototype._setCssVendorPrefix=function(t,e,i){var s=e.replace(/-([a-z])/gi,function(r,n){return n.toUpperCase()});this.cssVenderPrefixes.indexOf(s)!==-1?(t.style[s.charAt(0).toLowerCase()+s.slice(1)]=i,t.style["webkit"+s]=i,t.style["moz"+s]=i,t.style["ms"+s]=i,t.style["o"+s]=i):t.style[s]=i},o.prototype._getFirstEl=function(){return this.selector&&this.selector.length!==void 0?this.selector[0]:this.selector},o.prototype.isEventMatched=function(t,e){var i=e.split(".");return t.split(".").filter(function(s){return s}).every(function(s){return i.indexOf(s)!==-1})},o.prototype.attr=function(t,e){return e===void 0?this.firstElement?this.firstElement.getAttribute(t):"":(this._each(function(i){i.setAttribute(t,e)}),this)},o.prototype.find=function(t){return c(this._getSelector(t,this.selector))},o.prototype.first=function(){return this.selector&&this.selector.length!==void 0?c(this.selector[0]):c(this.selector)},o.prototype.eq=function(t){return c(this.selector[t])},o.prototype.parent=function(){return c(this.selector.parentElement)},o.prototype.get=function(){return this._getFirstEl()},o.prototype.removeAttr=function(t){var e=t.split(" ");return this._each(function(i){e.forEach(function(s){return i.removeAttribute(s)})}),this},o.prototype.wrap=function(t){if(!this.firstElement)return this;var e=document.createElement("div");return e.className=t,this.firstElement.parentNode.insertBefore(e,this.firstElement),this.firstElement.parentNode.removeChild(this.firstElement),e.appendChild(this.firstElement),this},o.prototype.addClass=function(t){return t===void 0&&(t=""),this._each(function(e){t.split(" ").forEach(function(i){i&&e.classList.add(i)})}),this},o.prototype.removeClass=function(t){return this._each(function(e){t.split(" ").forEach(function(i){i&&e.classList.remove(i)})}),this},o.prototype.hasClass=function(t){return this.firstElement?this.firstElement.classList.contains(t):!1},o.prototype.hasAttribute=function(t){return this.firstElement?this.firstElement.hasAttribute(t):!1},o.prototype.toggleClass=function(t){return this.firstElement?(this.hasClass(t)?this.removeClass(t):this.addClass(t),this):this},o.prototype.css=function(t,e){var i=this;return this._each(function(s){i._setCssVendorPrefix(s,t,e)}),this},o.prototype.on=function(t,e){var i=this;return this.selector?(t.split(" ").forEach(function(s){Array.isArray(o.eventListeners[s])||(o.eventListeners[s]=[]),o.eventListeners[s].push(e),i.selector.addEventListener(s.split(".")[0],e)}),this):this},o.prototype.once=function(t,e){var i=this;return this.on(t,function(){i.off(t),e(t)}),this},o.prototype.off=function(t){var e=this;return this.selector?(Object.keys(o.eventListeners).forEach(function(i){e.isEventMatched(t,i)&&(o.eventListeners[i].forEach(function(s){e.selector.removeEventListener(i.split(".")[0],s)}),o.eventListeners[i]=[])}),this):this},o.prototype.trigger=function(t,e){if(!this.firstElement)return this;var i=new CustomEvent(t.split(".")[0],{detail:e||null});return this.firstElement.dispatchEvent(i),this},o.prototype.load=function(t){var e=this;return fetch(t).then(function(i){return i.text()}).then(function(i){e.selector.innerHTML=i}),this},o.prototype.html=function(t){return t===void 0?this.firstElement?this.firstElement.innerHTML:"":(this._each(function(e){e.innerHTML=t}),this)},o.prototype.append=function(t){return this._each(function(e){typeof t=="string"?e.insertAdjacentHTML("beforeend",t):e.appendChild(t)}),this},o.prototype.prepend=function(t){return this._each(function(e){e.insertAdjacentHTML("afterbegin",t)}),this},o.prototype.remove=function(){return this._each(function(t){t.parentNode.removeChild(t)}),this},o.prototype.empty=function(){return this._each(function(t){t.innerHTML=""}),this},o.prototype.scrollTop=function(t){return t!==void 0?(document.body.scrollTop=t,document.documentElement.scrollTop=t,this):window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0},o.prototype.scrollLeft=function(t){return t!==void 0?(document.body.scrollLeft=t,document.documentElement.scrollLeft=t,this):window.pageXOffset||document.documentElement.scrollLeft||document.body.scrollLeft||0},o.prototype.offset=function(){if(!this.firstElement)return{left:0,top:0};var t=this.firstElement.getBoundingClientRect(),e=c("body").style().marginLeft;return{left:t.left-parseFloat(e)+this.scrollLeft(),top:t.top+this.scrollTop()}},o.prototype.style=function(){return this.firstElement?this.firstElement.currentStyle||window.getComputedStyle(this.firstElement):{}},o.prototype.width=function(){var t=this.style();return this.firstElement.clientWidth-parseFloat(t.paddingLeft)-parseFloat(t.paddingRight)},o.prototype.height=function(){var t=this.style();return this.firstElement.clientHeight-parseFloat(t.paddingTop)-parseFloat(t.paddingBottom)},o.eventListeners={},o}();function c(o){return U(),new V(o)}var K=["src","sources","subHtml","subHtmlUrl","html","video","poster","slideName","responsive","srcset","sizes","iframe","downloadUrl","download","width","facebookShareUrl","tweetText","iframeTitle","twitterShareUrl","pinterestShareUrl","pinterestText","fbHtml","disqusIdentifier","disqusUrl"];function Q(o){return o==="href"?"src":(o=o.replace("data-",""),o=o.charAt(0).toLowerCase()+o.slice(1),o=o.replace(/-([a-z])/g,function(t){return t[1].toUpperCase()}),o)}var S={getSize:function(o,t,e,i){e===void 0&&(e=0);var s=c(o),r=s.attr("data-lg-size")||i;if(!!r){var n=r.split(",");if(n[1])for(var a=window.innerWidth,l=0;l<n.length;l++){var h=n[l],u=parseInt(h.split("-")[2],10);if(u>a){r=h;break}l===n.length-1&&(r=h)}var d=r.split("-"),g=parseInt(d[0],10),m=parseInt(d[1],10),f=t.width(),b=t.height()-e,y=Math.min(f,g),p=Math.min(b,m),C=Math.min(y/g,p/m);return{width:g*C,height:m*C}}},getTransform:function(o,t,e,i,s){if(!!s){var r=c(o).find("img").first();if(!!r.get()){var n=t.get().getBoundingClientRect(),a=n.width,l=t.height()-(e+i),h=r.width(),u=r.height(),d=r.style(),g=(a-h)/2-r.offset().left+(parseFloat(d.paddingLeft)||0)+(parseFloat(d.borderLeft)||0)+c(window).scrollLeft()+n.left,m=(l-u)/2-r.offset().top+(parseFloat(d.paddingTop)||0)+(parseFloat(d.borderTop)||0)+c(window).scrollTop()+e,f=h/s.width,b=u/s.height,y="translate3d("+(g*=-1)+"px, "+(m*=-1)+"px, 0) scale3d("+f+", "+b+", 1)";return y}}},getIframeMarkup:function(o,t,e,i,s,r){var n=r?'title="'+r+'"':"";return'<div class="lg-video-cont lg-has-iframe" style="width:'+o+"; max-width:"+e+"; height: "+t+"; max-height:"+i+`">
                    <iframe class="lg-object" frameborder="0" `+n+' src="'+s+`"  allowfullscreen="true"></iframe>
                </div>`},getImgMarkup:function(o,t,e,i,s,r){var n=i?'srcset="'+i+'"':"",a=s?'sizes="'+s+'"':"",l="<img "+e+" "+n+"  "+a+' class="lg-object lg-image" data-index="'+o+'" src="'+t+'" />',h="";if(r){var u=typeof r=="string"?JSON.parse(r):r;h=u.map(function(d){var g="";return Object.keys(d).forEach(function(m){g+=" "+m+'="'+d[m]+'"'}),"<source "+g+"></source>"})}return""+h+l},getResponsiveSrc:function(o){for(var t=[],e=[],i="",s=0;s<o.length;s++){var r=o[s].split(" ");r[0]===""&&r.splice(0,1),e.push(r[0]),t.push(r[1])}for(var n=window.innerWidth,a=0;a<t.length;a++)if(parseInt(t[a],10)>n){i=e[a];break}return i},isImageLoaded:function(o){return!(!o||!o.complete||o.naturalWidth===0)},getVideoPosterMarkup:function(o,t,e,i,s){var r="";return s&&s.youtube?r="lg-has-youtube":s&&s.vimeo?r="lg-has-vimeo":r="lg-has-html5",'<div class="lg-video-cont '+r+'" style="'+e+`">
                <div class="lg-video-play-button">
                <svg
                    viewBox="0 0 20 20"
                    preserveAspectRatio="xMidYMid"
                    focusable="false"
                    aria-labelledby="`+i+`"
                    role="img"
                    class="lg-video-play-icon"
                >
                    <title>`+i+`</title>
                    <polygon class="lg-video-play-icon-inner" points="1,0 20,10 1,20"></polygon>
                </svg>
                <svg class="lg-video-play-icon-bg" viewBox="0 0 50 50" focusable="false">
                    <circle cx="50%" cy="50%" r="20"></circle></svg>
                <svg class="lg-video-play-icon-circle" viewBox="0 0 50 50" focusable="false">
                    <circle cx="50%" cy="50%" r="20"></circle>
                </svg>
            </div>
            `+(t||"")+`
            <img class="lg-object lg-video-poster" src="`+o+`" />
        </div>`},getFocusableElements:function(o){var t=o.querySelectorAll('a[href]:not([disabled]), button:not([disabled]), textarea:not([disabled]), input[type="text"]:not([disabled]), input[type="radio"]:not([disabled]), input[type="checkbox"]:not([disabled]), select:not([disabled])'),e=[].filter.call(t,function(i){var s=window.getComputedStyle(i);return s.display!=="none"&&s.visibility!=="hidden"});return e},getDynamicOptions:function(o,t,e,i){var s=[],r=Z(K,t);return[].forEach.call(o,function(n){for(var a={},l=0;l<n.attributes.length;l++){var h=n.attributes[l];if(h.specified){var u=Q(h.name),d="";r.indexOf(u)>-1&&(d=u),d&&(a[d]=h.value)}}var g=c(n),m=g.find("img").first().attr("alt"),f=g.attr("title"),b=i?g.attr(i):g.find("img").first().attr("src");a.thumb=b,e&&!a.subHtml&&(a.subHtml=f||m||""),a.alt=m||f||"",s.push(a)}),s},isMobile:function(){return/iPhone|iPad|iPod|Android/i.test(navigator.userAgent)},isVideo:function(o,t,e){if(!o){if(t)return{html5:!0};console.error("lightGallery :- data-src is not provided on slide item "+(e+1)+". Please make sure the selector property is properly configured. More info - https://www.lightgalleryjs.com/demos/html-markup/");return}var i=o.match(/\/\/(?:www\.)?youtu(?:\.be|be\.com|be-nocookie\.com)\/(?:watch\?v=|embed\/)?([a-z0-9\-\_\%]+)([\&|?][\S]*)*/i),s=o.match(/\/\/(?:www\.)?(?:player\.)?vimeo.com\/(?:video\/)?([0-9a-z\-_]+)(.*)?/i),r=o.match(/https?:\/\/(.+)?(wistia\.com|wi\.st)\/(medias|embed)\/([0-9a-z\-_]+)(.*)/);if(i)return{youtube:i};if(s)return{vimeo:s};if(r)return{wistia:r}}},H=0,J=function(){function o(t,e){if(this.lgOpened=!1,this.index=0,this.plugins=[],this.lGalleryOn=!1,this.lgBusy=!1,this.currentItemsInDom=[],this.prevScrollTop=0,this.bodyPaddingRight=0,this.isDummyImageRemoved=!1,this.dragOrSwipeEnabled=!1,this.mediaContainerPosition={top:0,bottom:0},!t)return this;if(H++,this.lgId=H,this.el=t,this.LGel=c(t),this.generateSettings(e),this.buildModules(),this.settings.dynamic&&this.settings.dynamicEl!==void 0&&!Array.isArray(this.settings.dynamicEl))throw"When using dynamic mode, you must also define dynamicEl as an Array.";return this.galleryItems=this.getItems(),this.normalizeSettings(),this.init(),this.validateLicense(),this}return o.prototype.generateSettings=function(t){if(this.settings=T(T({},q),t),this.settings.isMobile&&typeof this.settings.isMobile=="function"?this.settings.isMobile():S.isMobile()){var e=T(T({},this.settings.mobileSettings),this.settings.mobileSettings);this.settings=T(T({},this.settings),e)}},o.prototype.normalizeSettings=function(){this.settings.slideEndAnimation&&(this.settings.hideControlOnEnd=!1),this.settings.closable||(this.settings.swipeToClose=!1),this.zoomFromOrigin=this.settings.zoomFromOrigin,this.settings.dynamic&&(this.zoomFromOrigin=!1),this.settings.container||(this.settings.container=document.body),this.settings.preload=Math.min(this.settings.preload,this.galleryItems.length)},o.prototype.init=function(){var t=this;this.addSlideVideoInfo(this.galleryItems),this.buildStructure(),this.LGel.trigger(v.init,{instance:this}),this.settings.keyPress&&this.keyPress(),setTimeout(function(){t.enableDrag(),t.enableSwipe(),t.triggerPosterClick()},50),this.arrow(),this.settings.mousewheel&&this.mousewheel(),this.settings.dynamic||this.openGalleryOnItemClick()},o.prototype.openGalleryOnItemClick=function(){for(var t=this,e=function(r){var n=i.items[r],a=c(n),l=V.generateUUID();a.attr("data-lg-id",l).on("click.lgcustom-item-"+l,function(h){h.preventDefault();var u=t.settings.index||r;t.openGallery(u,n)})},i=this,s=0;s<this.items.length;s++)e(s)},o.prototype.buildModules=function(){var t=this;this.settings.plugins.forEach(function(e){t.plugins.push(new e(t,c))})},o.prototype.validateLicense=function(){this.settings.licenseKey?this.settings.licenseKey==="0000-0000-000-0000"&&console.warn("lightGallery: "+this.settings.licenseKey+" license key is not valid for production use"):console.error("Please provide a valid license key")},o.prototype.getSlideItem=function(t){return c(this.getSlideItemId(t))},o.prototype.getSlideItemId=function(t){return"#lg-item-"+this.lgId+"-"+t},o.prototype.getIdName=function(t){return t+"-"+this.lgId},o.prototype.getElementById=function(t){return c("#"+this.getIdName(t))},o.prototype.manageSingleSlideClassName=function(){this.galleryItems.length<2?this.outer.addClass("lg-single-item"):this.outer.removeClass("lg-single-item")},o.prototype.buildStructure=function(){var t=this,e=this.$container&&this.$container.get();if(!e){var i="",s="";this.settings.controls&&(i='<button type="button" id="'+this.getIdName("lg-prev")+'" aria-label="'+this.settings.strings.previousSlide+'" class="lg-prev lg-icon"> '+this.settings.prevHtml+` </button>
                <button type="button" id="`+this.getIdName("lg-next")+'" aria-label="'+this.settings.strings.nextSlide+'" class="lg-next lg-icon"> '+this.settings.nextHtml+" </button>"),this.settings.appendSubHtmlTo!==".lg-item"&&(s='<div class="lg-sub-html" role="status" aria-live="polite"></div>');var r="";this.settings.allowMediaOverlap&&(r+="lg-media-overlap ");var n=this.settings.ariaLabelledby?'aria-labelledby="'+this.settings.ariaLabelledby+'"':"",a=this.settings.ariaDescribedby?'aria-describedby="'+this.settings.ariaDescribedby+'"':"",l="lg-container "+this.settings.addClass+" "+(document.body!==this.settings.container?"lg-inline":""),h=this.settings.closable&&this.settings.showCloseIcon?'<button type="button" aria-label="'+this.settings.strings.closeGallery+'" id="'+this.getIdName("lg-close")+'" class="lg-close lg-icon"></button>':"",u=this.settings.showMaximizeIcon?'<button type="button" aria-label="'+this.settings.strings.toggleMaximize+'" id="'+this.getIdName("lg-maximize")+'" class="lg-maximize lg-icon"></button>':"",d=`
        <div class="`+l+'" id="'+this.getIdName("lg-container")+'" tabindex="-1" aria-modal="true" '+n+" "+a+` role="dialog"
        >
            <div id="`+this.getIdName("lg-backdrop")+`" class="lg-backdrop"></div>

            <div id="`+this.getIdName("lg-outer")+'" class="lg-outer lg-use-css3 lg-css3 lg-hide-items '+r+` ">

              <div id="`+this.getIdName("lg-content")+`" class="lg-content">
                <div id="`+this.getIdName("lg-inner")+`" class="lg-inner">
                </div>
                `+i+`
              </div>
                <div id="`+this.getIdName("lg-toolbar")+`" class="lg-toolbar lg-group">
                    `+u+`
                    `+h+`
                    </div>
                    `+(this.settings.appendSubHtmlTo===".lg-outer"?s:"")+`
                <div id="`+this.getIdName("lg-components")+`" class="lg-components">
                    `+(this.settings.appendSubHtmlTo===".lg-sub-html"?s:"")+`
                </div>
            </div>
        </div>
        `;c(this.settings.container).append(d),document.body!==this.settings.container&&c(this.settings.container).css("position","relative"),this.outer=this.getElementById("lg-outer"),this.$lgComponents=this.getElementById("lg-components"),this.$backdrop=this.getElementById("lg-backdrop"),this.$container=this.getElementById("lg-container"),this.$inner=this.getElementById("lg-inner"),this.$content=this.getElementById("lg-content"),this.$toolbar=this.getElementById("lg-toolbar"),this.$backdrop.css("transition-duration",this.settings.backdropDuration+"ms");var g=this.settings.mode+" ";this.manageSingleSlideClassName(),this.settings.enableDrag&&(g+="lg-grab "),this.outer.addClass(g),this.$inner.css("transition-timing-function",this.settings.easing),this.$inner.css("transition-duration",this.settings.speed+"ms"),this.settings.download&&this.$toolbar.append('<a id="'+this.getIdName("lg-download")+'" target="_blank" rel="noopener" aria-label="'+this.settings.strings.download+'" download class="lg-download lg-icon"></a>'),this.counter(),c(window).on("resize.lg.global"+this.lgId+" orientationchange.lg.global"+this.lgId,function(){t.refreshOnResize()}),this.hideBars(),this.manageCloseGallery(),this.toggleMaximize(),this.initModules()}},o.prototype.refreshOnResize=function(){if(this.lgOpened){var t=this.galleryItems[this.index],e=t.__slideVideoInfo;this.mediaContainerPosition=this.getMediaContainerPosition();var i=this.mediaContainerPosition,s=i.top,r=i.bottom;if(this.currentImageSize=S.getSize(this.items[this.index],this.outer,s+r,e&&this.settings.videoMaxSize),e&&this.resizeVideoSlide(this.index,this.currentImageSize),this.zoomFromOrigin&&!this.isDummyImageRemoved){var n=this.getDummyImgStyles(this.currentImageSize);this.outer.find(".lg-current .lg-dummy-img").first().attr("style",n)}this.LGel.trigger(v.containerResize)}},o.prototype.resizeVideoSlide=function(t,e){var i=this.getVideoContStyle(e),s=this.getSlideItem(t);s.find(".lg-video-cont").attr("style",i)},o.prototype.updateSlides=function(t,e){if(this.index>t.length-1&&(this.index=t.length-1),t.length===1&&(this.index=0),!t.length){this.closeGallery();return}var i=this.galleryItems[e].src;this.galleryItems=t,this.updateControls(),this.$inner.empty(),this.currentItemsInDom=[];var s=0;this.galleryItems.some(function(r,n){return r.src===i?(s=n,!0):!1}),this.currentItemsInDom=this.organizeSlideItems(s,-1),this.loadContent(s,!0),this.getSlideItem(s).addClass("lg-current"),this.index=s,this.updateCurrentCounter(s),this.LGel.trigger(v.updateSlides)},o.prototype.getItems=function(){if(this.items=[],this.settings.dynamic)return this.settings.dynamicEl||[];if(this.settings.selector==="this")this.items.push(this.el);else if(this.settings.selector)if(typeof this.settings.selector=="string")if(this.settings.selectWithin){var t=c(this.settings.selectWithin);this.items=t.find(this.settings.selector).get()}else this.items=this.el.querySelectorAll(this.settings.selector);else this.items=this.settings.selector;else this.items=this.el.children;return S.getDynamicOptions(this.items,this.settings.extraProps,this.settings.getCaptionFromTitleOrAlt,this.settings.exThumbImage)},o.prototype.shouldHideScrollbar=function(){return this.settings.hideScrollbar&&document.body===this.settings.container},o.prototype.hideScrollbar=function(){if(!!this.shouldHideScrollbar()){this.bodyPaddingRight=parseFloat(c("body").style().paddingRight);var t=document.documentElement.getBoundingClientRect(),e=window.innerWidth-t.width;c(document.body).css("padding-right",e+this.bodyPaddingRight+"px"),c(document.body).addClass("lg-overlay-open")}},o.prototype.resetScrollBar=function(){!this.shouldHideScrollbar()||(c(document.body).css("padding-right",this.bodyPaddingRight+"px"),c(document.body).removeClass("lg-overlay-open"))},o.prototype.openGallery=function(t,e){var i=this;if(t===void 0&&(t=this.settings.index),!this.lgOpened){this.lgOpened=!0,this.outer.removeClass("lg-hide-items"),this.hideScrollbar(),this.$container.addClass("lg-show");var s=this.getItemsToBeInsertedToDom(t,t);this.currentItemsInDom=s;var r="";s.forEach(function(g){r=r+('<div id="'+g+'" class="lg-item"></div>')}),this.$inner.append(r),this.addHtml(t);var n="";this.mediaContainerPosition=this.getMediaContainerPosition();var a=this.mediaContainerPosition,l=a.top,h=a.bottom;this.settings.allowMediaOverlap||this.setMediaContainerPosition(l,h);var u=this.galleryItems[t].__slideVideoInfo;this.zoomFromOrigin&&e&&(this.currentImageSize=S.getSize(e,this.outer,l+h,u&&this.settings.videoMaxSize),n=S.getTransform(e,this.outer,l,h,this.currentImageSize)),(!this.zoomFromOrigin||!n)&&(this.outer.addClass(this.settings.startClass),this.getSlideItem(t).removeClass("lg-complete"));var d=this.settings.zoomFromOrigin?100:this.settings.backdropDuration;setTimeout(function(){i.outer.addClass("lg-components-open")},d),this.index=t,this.LGel.trigger(v.beforeOpen),this.getSlideItem(t).addClass("lg-current"),this.lGalleryOn=!1,this.prevScrollTop=c(window).scrollTop(),setTimeout(function(){if(i.zoomFromOrigin&&n){var g=i.getSlideItem(t);g.css("transform",n),setTimeout(function(){g.addClass("lg-start-progress lg-start-end-progress").css("transition-duration",i.settings.startAnimationDuration+"ms"),i.outer.addClass("lg-zoom-from-image")}),setTimeout(function(){g.css("transform","translate3d(0, 0, 0)")},100)}setTimeout(function(){i.$backdrop.addClass("in"),i.$container.addClass("lg-show-in")},10),setTimeout(function(){i.settings.trapFocus&&document.body===i.settings.container&&i.trapFocus()},i.settings.backdropDuration+50),(!i.zoomFromOrigin||!n)&&setTimeout(function(){i.outer.addClass("lg-visible")},i.settings.backdropDuration),i.slide(t,!1,!1,!1),i.LGel.trigger(v.afterOpen)}),document.body===this.settings.container&&c("html").addClass("lg-on")}},o.prototype.getMediaContainerPosition=function(){if(this.settings.allowMediaOverlap)return{top:0,bottom:0};var t=this.$toolbar.get().clientHeight||0,e=this.outer.find(".lg-components .lg-sub-html").get(),i=this.settings.defaultCaptionHeight||e&&e.clientHeight||0,s=this.outer.find(".lg-thumb-outer").get(),r=s?s.clientHeight:0,n=r+i;return{top:t,bottom:n}},o.prototype.setMediaContainerPosition=function(t,e){t===void 0&&(t=0),e===void 0&&(e=0),this.$content.css("top",t+"px").css("bottom",e+"px")},o.prototype.hideBars=function(){var t=this;setTimeout(function(){t.outer.removeClass("lg-hide-items"),t.settings.hideBarsDelay>0&&(t.outer.on("mousemove.lg click.lg touchstart.lg",function(){t.outer.removeClass("lg-hide-items"),clearTimeout(t.hideBarTimeout),t.hideBarTimeout=setTimeout(function(){t.outer.addClass("lg-hide-items")},t.settings.hideBarsDelay)}),t.outer.trigger("mousemove.lg"))},this.settings.showBarsAfter)},o.prototype.initPictureFill=function(t){if(this.settings.supportLegacyBrowser)try{picturefill({elements:[t.get()]})}catch{console.warn("lightGallery :- If you want srcset or picture tag to be supported for older browser please include picturefil javascript library in your document.")}},o.prototype.counter=function(){if(this.settings.counter){var t=`<div class="lg-counter" role="status" aria-live="polite">
                <span id="`+this.getIdName("lg-counter-current")+'" class="lg-counter-current">'+(this.index+1)+` </span> /
                <span id="`+this.getIdName("lg-counter-all")+'" class="lg-counter-all">'+this.galleryItems.length+" </span></div>";this.outer.find(this.settings.appendCounterTo).append(t)}},o.prototype.addHtml=function(t){var e,i;if(this.galleryItems[t].subHtmlUrl?i=this.galleryItems[t].subHtmlUrl:e=this.galleryItems[t].subHtml,!i)if(e){var s=e.substring(0,1);(s==="."||s==="#")&&(this.settings.subHtmlSelectorRelative&&!this.settings.dynamic?e=c(this.items).eq(t).find(e).first().html():e=c(e).first().html())}else e="";if(this.settings.appendSubHtmlTo!==".lg-item")i?this.outer.find(".lg-sub-html").load(i):this.outer.find(".lg-sub-html").html(e);else{var r=c(this.getSlideItemId(t));i?r.load(i):r.append('<div class="lg-sub-html">'+e+"</div>")}typeof e<"u"&&e!==null&&(e===""?this.outer.find(this.settings.appendSubHtmlTo).addClass("lg-empty-html"):this.outer.find(this.settings.appendSubHtmlTo).removeClass("lg-empty-html")),this.LGel.trigger(v.afterAppendSubHtml,{index:t})},o.prototype.preload=function(t){for(var e=1;e<=this.settings.preload&&!(e>=this.galleryItems.length-t);e++)this.loadContent(t+e,!1);for(var i=1;i<=this.settings.preload&&!(t-i<0);i++)this.loadContent(t-i,!1)},o.prototype.getDummyImgStyles=function(t){return t?"width:"+t.width+`px;
                margin-left: -`+t.width/2+`px;
                margin-top: -`+t.height/2+`px;
                height:`+t.height+"px":""},o.prototype.getVideoContStyle=function(t){return t?"width:"+t.width+`px;
                height:`+t.height+"px":""},o.prototype.getDummyImageContent=function(t,e,i){var s;if(this.settings.dynamic||(s=c(this.items).eq(e)),s){var r=void 0;if(this.settings.exThumbImage?r=s.attr(this.settings.exThumbImage):r=s.find("img").first().attr("src"),!r)return"";var n=this.getDummyImgStyles(this.currentImageSize),a="<img "+i+' style="'+n+'" class="lg-dummy-img" src="'+r+'" />';return t.addClass("lg-first-slide"),this.outer.addClass("lg-first-slide-loading"),a}return""},o.prototype.setImgMarkup=function(t,e,i){var s=this.galleryItems[i],r=s.alt,n=s.srcset,a=s.sizes,l=s.sources,h="",u=r?'alt="'+r+'"':"";this.isFirstSlideWithZoomAnimation()?h=this.getDummyImageContent(e,i,u):h=S.getImgMarkup(i,t,u,n,a,l);var d='<picture class="lg-img-wrap"> '+h+"</picture>";e.prepend(d)},o.prototype.onSlideObjectLoad=function(t,e,i,s){var r=t.find(".lg-object").first();S.isImageLoaded(r.get())||e?i():(r.on("load.lg error.lg",function(){i&&i()}),r.on("error.lg",function(){s&&s()}))},o.prototype.onLgObjectLoad=function(t,e,i,s,r,n){var a=this;this.onSlideObjectLoad(t,n,function(){a.triggerSlideItemLoad(t,e,i,s,r)},function(){t.addClass("lg-complete lg-complete_"),t.html('<span class="lg-error-msg">'+a.settings.strings.mediaLoadingFailed+"</span>")})},o.prototype.triggerSlideItemLoad=function(t,e,i,s,r){var n=this,a=this.galleryItems[e],l=r&&this.getSlideType(a)==="video"&&!a.poster?s:0;setTimeout(function(){t.addClass("lg-complete lg-complete_"),n.LGel.trigger(v.slideItemLoad,{index:e,delay:i||0,isFirstSlide:r})},l)},o.prototype.isFirstSlideWithZoomAnimation=function(){return!!(!this.lGalleryOn&&this.zoomFromOrigin&&this.currentImageSize)},o.prototype.addSlideVideoInfo=function(t){var e=this;t.forEach(function(i,s){i.__slideVideoInfo=S.isVideo(i.src,!!i.video,s),i.__slideVideoInfo&&e.settings.loadYouTubePoster&&!i.poster&&i.__slideVideoInfo.youtube&&(i.poster="//img.youtube.com/vi/"+i.__slideVideoInfo.youtube[1]+"/maxresdefault.jpg")})},o.prototype.loadContent=function(t,e){var i=this,s=this.galleryItems[t],r=c(this.getSlideItemId(t)),n=s.poster,a=s.srcset,l=s.sizes,h=s.sources,u=s.src,d=s.video,g=d&&typeof d=="string"?JSON.parse(d):d;if(s.responsive){var m=s.responsive.split(",");u=S.getResponsiveSrc(m)||u}var f=s.__slideVideoInfo,b="",y=!!s.iframe,p=!this.lGalleryOn,C=0;if(p&&(this.zoomFromOrigin&&this.currentImageSize?C=this.settings.startAnimationDuration+10:C=this.settings.backdropDuration+10),!r.hasClass("lg-loaded")){if(f){var O=this.mediaContainerPosition,M=O.top,z=O.bottom,G=S.getSize(this.items[t],this.outer,M+z,f&&this.settings.videoMaxSize);b=this.getVideoContStyle(G)}if(y){var w=S.getIframeMarkup(this.settings.iframeWidth,this.settings.iframeHeight,this.settings.iframeMaxWidth,this.settings.iframeMaxHeight,u,s.iframeTitle);r.prepend(w)}else if(n){var L="",Y=p&&this.zoomFromOrigin&&this.currentImageSize;Y&&(L=this.getDummyImageContent(r,t,""));var w=S.getVideoPosterMarkup(n,L||"",b,this.settings.strings.playVideo,f);r.prepend(w)}else if(f){var w='<div class="lg-video-cont " style="'+b+'"></div>';r.prepend(w)}else if(this.setImgMarkup(u,r,t),a||h){var F=r.find(".lg-object");this.initPictureFill(F)}(n||f)&&this.LGel.trigger(v.hasVideo,{index:t,src:u,html5Video:g,hasPoster:!!n}),this.LGel.trigger(v.afterAppendSlide,{index:t}),this.lGalleryOn&&this.settings.appendSubHtmlTo===".lg-item"&&this.addHtml(t)}var P=0;C&&!c(document.body).hasClass("lg-from-hash")&&(P=C),this.isFirstSlideWithZoomAnimation()&&(setTimeout(function(){r.removeClass("lg-start-end-progress lg-start-progress").removeAttr("style")},this.settings.startAnimationDuration+100),r.hasClass("lg-loaded")||setTimeout(function(){if(i.getSlideType(s)==="image"){var X=s.alt,j=X?'alt="'+X+'"':"";if(r.find(".lg-img-wrap").append(S.getImgMarkup(t,u,j,a,l,s.sources)),a||h){var N=r.find(".lg-object");i.initPictureFill(N)}}(i.getSlideType(s)==="image"||i.getSlideType(s)==="video"&&n)&&(i.onLgObjectLoad(r,t,C,P,!0,!1),i.onSlideObjectLoad(r,!!(f&&f.html5&&!n),function(){i.loadContentOnFirstSlideLoad(t,r,P)},function(){i.loadContentOnFirstSlideLoad(t,r,P)}))},this.settings.startAnimationDuration+100)),r.addClass("lg-loaded"),(!this.isFirstSlideWithZoomAnimation()||this.getSlideType(s)==="video"&&!n)&&this.onLgObjectLoad(r,t,C,P,p,!!(f&&f.html5&&!n)),(!this.zoomFromOrigin||!this.currentImageSize)&&r.hasClass("lg-complete_")&&!this.lGalleryOn&&setTimeout(function(){r.addClass("lg-complete")},this.settings.backdropDuration),this.lGalleryOn=!0,e===!0&&(r.hasClass("lg-complete_")?this.preload(t):r.find(".lg-object").first().on("load.lg error.lg",function(){i.preload(t)}))},o.prototype.loadContentOnFirstSlideLoad=function(t,e,i){var s=this;setTimeout(function(){e.find(".lg-dummy-img").remove(),e.removeClass("lg-first-slide"),s.outer.removeClass("lg-first-slide-loading"),s.isDummyImageRemoved=!0,s.preload(t)},i+300)},o.prototype.getItemsToBeInsertedToDom=function(t,e,i){var s=this;i===void 0&&(i=0);var r=[],n=Math.max(i,3);n=Math.min(n,this.galleryItems.length);var a="lg-item-"+this.lgId+"-"+e;if(this.galleryItems.length<=3)return this.galleryItems.forEach(function(u,d){r.push("lg-item-"+s.lgId+"-"+d)}),r;if(t<(this.galleryItems.length-1)/2){for(var l=t;l>t-n/2&&l>=0;l--)r.push("lg-item-"+this.lgId+"-"+l);for(var h=r.length,l=0;l<n-h;l++)r.push("lg-item-"+this.lgId+"-"+(t+l+1))}else{for(var l=t;l<=this.galleryItems.length-1&&l<t+n/2;l++)r.push("lg-item-"+this.lgId+"-"+l);for(var h=r.length,l=0;l<n-h;l++)r.push("lg-item-"+this.lgId+"-"+(t-l-1))}return this.settings.loop&&(t===this.galleryItems.length-1?r.push("lg-item-"+this.lgId+"-"+0):t===0&&r.push("lg-item-"+this.lgId+"-"+(this.galleryItems.length-1))),r.indexOf(a)===-1&&r.push("lg-item-"+this.lgId+"-"+e),r},o.prototype.organizeSlideItems=function(t,e){var i=this,s=this.getItemsToBeInsertedToDom(t,e,this.settings.numberOfSlideItemsInDom);return s.forEach(function(r){i.currentItemsInDom.indexOf(r)===-1&&i.$inner.append('<div id="'+r+'" class="lg-item"></div>')}),this.currentItemsInDom.forEach(function(r){s.indexOf(r)===-1&&c("#"+r).remove()}),s},o.prototype.getPreviousSlideIndex=function(){var t=0;try{var e=this.outer.find(".lg-current").first().attr("id");t=parseInt(e.split("-")[3])||0}catch{t=0}return t},o.prototype.setDownloadValue=function(t){if(this.settings.download){var e=this.galleryItems[t],i=e.downloadUrl===!1||e.downloadUrl==="false";if(i)this.outer.addClass("lg-hide-download");else{var s=this.getElementById("lg-download");this.outer.removeClass("lg-hide-download"),s.attr("href",e.downloadUrl||e.src),e.download&&s.attr("download",e.download)}}},o.prototype.makeSlideAnimation=function(t,e,i){var s=this;this.lGalleryOn&&i.addClass("lg-slide-progress"),setTimeout(function(){s.outer.addClass("lg-no-trans"),s.outer.find(".lg-item").removeClass("lg-prev-slide lg-next-slide"),t==="prev"?(e.addClass("lg-prev-slide"),i.addClass("lg-next-slide")):(e.addClass("lg-next-slide"),i.addClass("lg-prev-slide")),setTimeout(function(){s.outer.find(".lg-item").removeClass("lg-current"),e.addClass("lg-current"),s.outer.removeClass("lg-no-trans")},50)},this.lGalleryOn?this.settings.slideDelay:0)},o.prototype.slide=function(t,e,i,s){var r=this,n=this.getPreviousSlideIndex();if(this.currentItemsInDom=this.organizeSlideItems(t,n),!(this.lGalleryOn&&n===t)){var a=this.galleryItems.length;if(!this.lgBusy){this.settings.counter&&this.updateCurrentCounter(t);var l=this.getSlideItem(t),h=this.getSlideItem(n),u=this.galleryItems[t],d=u.__slideVideoInfo;if(this.outer.attr("data-lg-slide-type",this.getSlideType(u)),this.setDownloadValue(t),d){var g=this.mediaContainerPosition,m=g.top,f=g.bottom,b=S.getSize(this.items[t],this.outer,m+f,d&&this.settings.videoMaxSize);this.resizeVideoSlide(t,b)}if(this.LGel.trigger(v.beforeSlide,{prevIndex:n,index:t,fromTouch:!!e,fromThumb:!!i}),this.lgBusy=!0,clearTimeout(this.hideBarTimeout),this.arrowDisable(t),s||(t<n?s="prev":t>n&&(s="next")),!e)this.makeSlideAnimation(s,l,h);else{this.outer.find(".lg-item").removeClass("lg-prev-slide lg-current lg-next-slide");var y=void 0,p=void 0;a>2?(y=t-1,p=t+1,(t===0&&n===a-1||t===a-1&&n===0)&&(p=0,y=a-1)):(y=0,p=1),s==="prev"?this.getSlideItem(p).addClass("lg-next-slide"):this.getSlideItem(y).addClass("lg-prev-slide"),l.addClass("lg-current")}this.lGalleryOn?setTimeout(function(){r.loadContent(t,!0),r.settings.appendSubHtmlTo!==".lg-item"&&r.addHtml(t)},this.settings.speed+50+(e?0:this.settings.slideDelay)):this.loadContent(t,!0),setTimeout(function(){r.lgBusy=!1,h.removeClass("lg-slide-progress"),r.LGel.trigger(v.afterSlide,{prevIndex:n,index:t,fromTouch:e,fromThumb:i})},(this.lGalleryOn?this.settings.speed+100:100)+(e?0:this.settings.slideDelay))}this.index=t}},o.prototype.updateCurrentCounter=function(t){this.getElementById("lg-counter-current").html(t+1+"")},o.prototype.updateCounterTotal=function(){this.getElementById("lg-counter-all").html(this.galleryItems.length+"")},o.prototype.getSlideType=function(t){return t.__slideVideoInfo?"video":t.iframe?"iframe":"image"},o.prototype.touchMove=function(t,e,i){var s=e.pageX-t.pageX,r=e.pageY-t.pageY,n=!1;if(this.swipeDirection?n=!0:Math.abs(s)>15?(this.swipeDirection="horizontal",n=!0):Math.abs(r)>15&&(this.swipeDirection="vertical",n=!0),!!n){var a=this.getSlideItem(this.index);if(this.swipeDirection==="horizontal"){i==null||i.preventDefault(),this.outer.addClass("lg-dragging"),this.setTranslate(a,s,0);var l=a.get().offsetWidth,h=l*15/100,u=h-Math.abs(s*10/100);this.setTranslate(this.outer.find(".lg-prev-slide").first(),-l+s-u,0),this.setTranslate(this.outer.find(".lg-next-slide").first(),l+s+u,0)}else if(this.swipeDirection==="vertical"&&this.settings.swipeToClose){i==null||i.preventDefault(),this.$container.addClass("lg-dragging-vertical");var d=1-Math.abs(r)/window.innerHeight;this.$backdrop.css("opacity",d);var g=1-Math.abs(r)/(window.innerWidth*2);this.setTranslate(a,0,r,g,g),Math.abs(r)>100&&this.outer.addClass("lg-hide-items").removeClass("lg-components-open")}}},o.prototype.touchEnd=function(t,e,i){var s=this,r;this.settings.mode!=="lg-slide"&&this.outer.addClass("lg-slide"),setTimeout(function(){s.$container.removeClass("lg-dragging-vertical"),s.outer.removeClass("lg-dragging lg-hide-items").addClass("lg-components-open");var n=!0;if(s.swipeDirection==="horizontal"){r=t.pageX-e.pageX;var a=Math.abs(t.pageX-e.pageX);r<0&&a>s.settings.swipeThreshold?(s.goToNextSlide(!0),n=!1):r>0&&a>s.settings.swipeThreshold&&(s.goToPrevSlide(!0),n=!1)}else if(s.swipeDirection==="vertical")if(r=Math.abs(t.pageY-e.pageY),s.settings.closable&&s.settings.swipeToClose&&r>100){s.closeGallery();return}else s.$backdrop.css("opacity",1);if(s.outer.find(".lg-item").removeAttr("style"),n&&Math.abs(t.pageX-e.pageX)<5){var l=c(i.target);s.isPosterElement(l)&&s.LGel.trigger(v.posterClick)}s.swipeDirection=void 0}),setTimeout(function(){!s.outer.hasClass("lg-dragging")&&s.settings.mode!=="lg-slide"&&s.outer.removeClass("lg-slide")},this.settings.speed+100)},o.prototype.enableSwipe=function(){var t=this,e={},i={},s=!1,r=!1;this.settings.enableSwipe&&(this.$inner.on("touchstart.lg",function(n){t.dragOrSwipeEnabled=!0;var a=t.getSlideItem(t.index);(c(n.target).hasClass("lg-item")||a.get().contains(n.target))&&!t.outer.hasClass("lg-zoomed")&&!t.lgBusy&&n.touches.length===1&&(r=!0,t.touchAction="swipe",t.manageSwipeClass(),e={pageX:n.touches[0].pageX,pageY:n.touches[0].pageY})}),this.$inner.on("touchmove.lg",function(n){r&&t.touchAction==="swipe"&&n.touches.length===1&&(i={pageX:n.touches[0].pageX,pageY:n.touches[0].pageY},t.touchMove(e,i,n),s=!0)}),this.$inner.on("touchend.lg",function(n){if(t.touchAction==="swipe"){if(s)s=!1,t.touchEnd(i,e,n);else if(r){var a=c(n.target);t.isPosterElement(a)&&t.LGel.trigger(v.posterClick)}t.touchAction=void 0,r=!1}}))},o.prototype.enableDrag=function(){var t=this,e={},i={},s=!1,r=!1;this.settings.enableDrag&&(this.outer.on("mousedown.lg",function(n){t.dragOrSwipeEnabled=!0;var a=t.getSlideItem(t.index);(c(n.target).hasClass("lg-item")||a.get().contains(n.target))&&!t.outer.hasClass("lg-zoomed")&&!t.lgBusy&&(n.preventDefault(),t.lgBusy||(t.manageSwipeClass(),e={pageX:n.pageX,pageY:n.pageY},s=!0,t.outer.get().scrollLeft+=1,t.outer.get().scrollLeft-=1,t.outer.removeClass("lg-grab").addClass("lg-grabbing"),t.LGel.trigger(v.dragStart)))}),c(window).on("mousemove.lg.global"+this.lgId,function(n){s&&t.lgOpened&&(r=!0,i={pageX:n.pageX,pageY:n.pageY},t.touchMove(e,i),t.LGel.trigger(v.dragMove))}),c(window).on("mouseup.lg.global"+this.lgId,function(n){if(!!t.lgOpened){var a=c(n.target);r?(r=!1,t.touchEnd(i,e,n),t.LGel.trigger(v.dragEnd)):t.isPosterElement(a)&&t.LGel.trigger(v.posterClick),s&&(s=!1,t.outer.removeClass("lg-grabbing").addClass("lg-grab"))}}))},o.prototype.triggerPosterClick=function(){var t=this;this.$inner.on("click.lg",function(e){!t.dragOrSwipeEnabled&&t.isPosterElement(c(e.target))&&t.LGel.trigger(v.posterClick)})},o.prototype.manageSwipeClass=function(){var t=this.index+1,e=this.index-1;this.settings.loop&&this.galleryItems.length>2&&(this.index===0?e=this.galleryItems.length-1:this.index===this.galleryItems.length-1&&(t=0)),this.outer.find(".lg-item").removeClass("lg-next-slide lg-prev-slide"),e>-1&&this.getSlideItem(e).addClass("lg-prev-slide"),this.getSlideItem(t).addClass("lg-next-slide")},o.prototype.goToNextSlide=function(t){var e=this,i=this.settings.loop;t&&this.galleryItems.length<3&&(i=!1),this.lgBusy||(this.index+1<this.galleryItems.length?(this.index++,this.LGel.trigger(v.beforeNextSlide,{index:this.index}),this.slide(this.index,!!t,!1,"next")):i?(this.index=0,this.LGel.trigger(v.beforeNextSlide,{index:this.index}),this.slide(this.index,!!t,!1,"next")):this.settings.slideEndAnimation&&!t&&(this.outer.addClass("lg-right-end"),setTimeout(function(){e.outer.removeClass("lg-right-end")},400)))},o.prototype.goToPrevSlide=function(t){var e=this,i=this.settings.loop;t&&this.galleryItems.length<3&&(i=!1),this.lgBusy||(this.index>0?(this.index--,this.LGel.trigger(v.beforePrevSlide,{index:this.index,fromTouch:t}),this.slide(this.index,!!t,!1,"prev")):i?(this.index=this.galleryItems.length-1,this.LGel.trigger(v.beforePrevSlide,{index:this.index,fromTouch:t}),this.slide(this.index,!!t,!1,"prev")):this.settings.slideEndAnimation&&!t&&(this.outer.addClass("lg-left-end"),setTimeout(function(){e.outer.removeClass("lg-left-end")},400)))},o.prototype.keyPress=function(){var t=this;c(window).on("keydown.lg.global"+this.lgId,function(e){t.lgOpened&&t.settings.escKey===!0&&e.keyCode===27&&(e.preventDefault(),t.settings.allowMediaOverlap&&t.outer.hasClass("lg-can-toggle")&&t.outer.hasClass("lg-components-open")?t.outer.removeClass("lg-components-open"):t.closeGallery()),t.lgOpened&&t.galleryItems.length>1&&(e.keyCode===37&&(e.preventDefault(),t.goToPrevSlide()),e.keyCode===39&&(e.preventDefault(),t.goToNextSlide()))})},o.prototype.arrow=function(){var t=this;this.getElementById("lg-prev").on("click.lg",function(){t.goToPrevSlide()}),this.getElementById("lg-next").on("click.lg",function(){t.goToNextSlide()})},o.prototype.arrowDisable=function(t){if(!this.settings.loop&&this.settings.hideControlOnEnd){var e=this.getElementById("lg-prev"),i=this.getElementById("lg-next");t+1===this.galleryItems.length?i.attr("disabled","disabled").addClass("disabled"):i.removeAttr("disabled").removeClass("disabled"),t===0?e.attr("disabled","disabled").addClass("disabled"):e.removeAttr("disabled").removeClass("disabled")}},o.prototype.setTranslate=function(t,e,i,s,r){s===void 0&&(s=1),r===void 0&&(r=1),t.css("transform","translate3d("+e+"px, "+i+"px, 0px) scale3d("+s+", "+r+", 1)")},o.prototype.mousewheel=function(){var t=this,e=0;this.outer.on("wheel.lg",function(i){if(!(!i.deltaY||t.galleryItems.length<2)){i.preventDefault();var s=new Date().getTime();s-e<1e3||(e=s,i.deltaY>0?t.goToNextSlide():i.deltaY<0&&t.goToPrevSlide())}})},o.prototype.isSlideElement=function(t){return t.hasClass("lg-outer")||t.hasClass("lg-item")||t.hasClass("lg-img-wrap")},o.prototype.isPosterElement=function(t){var e=this.getSlideItem(this.index).find(".lg-video-play-button").get();return t.hasClass("lg-video-poster")||t.hasClass("lg-video-play-button")||e&&e.contains(t.get())},o.prototype.toggleMaximize=function(){var t=this;this.getElementById("lg-maximize").on("click.lg",function(){t.$container.toggleClass("lg-inline"),t.refreshOnResize()})},o.prototype.invalidateItems=function(){for(var t=0;t<this.items.length;t++){var e=this.items[t],i=c(e);i.off("click.lgcustom-item-"+i.attr("data-lg-id"))}},o.prototype.trapFocus=function(){var t=this;this.$container.get().focus({preventScroll:!0}),c(window).on("keydown.lg.global"+this.lgId,function(e){if(!!t.lgOpened){var i=e.key==="Tab"||e.keyCode===9;if(!!i){var s=S.getFocusableElements(t.$container.get()),r=s[0],n=s[s.length-1];e.shiftKey?document.activeElement===r&&(n.focus(),e.preventDefault()):document.activeElement===n&&(r.focus(),e.preventDefault())}}})},o.prototype.manageCloseGallery=function(){var t=this;if(!!this.settings.closable){var e=!1;this.getElementById("lg-close").on("click.lg",function(){t.closeGallery()}),this.settings.closeOnTap&&(this.outer.on("mousedown.lg",function(i){var s=c(i.target);t.isSlideElement(s)?e=!0:e=!1}),this.outer.on("mousemove.lg",function(){e=!1}),this.outer.on("mouseup.lg",function(i){var s=c(i.target);t.isSlideElement(s)&&e&&(t.outer.hasClass("lg-dragging")||t.closeGallery())}))}},o.prototype.closeGallery=function(t){var e=this;if(!this.lgOpened||!this.settings.closable&&!t)return 0;this.LGel.trigger(v.beforeClose),this.settings.resetScrollPosition&&!this.settings.hideScrollbar&&c(window).scrollTop(this.prevScrollTop);var i=this.items[this.index],s;if(this.zoomFromOrigin&&i){var r=this.mediaContainerPosition,n=r.top,a=r.bottom,l=this.galleryItems[this.index],h=l.__slideVideoInfo,u=l.poster,d=S.getSize(i,this.outer,n+a,h&&u&&this.settings.videoMaxSize);s=S.getTransform(i,this.outer,n,a,d)}this.zoomFromOrigin&&s?(this.outer.addClass("lg-closing lg-zoom-from-image"),this.getSlideItem(this.index).addClass("lg-start-end-progress").css("transition-duration",this.settings.startAnimationDuration+"ms").css("transform",s)):(this.outer.addClass("lg-hide-items"),this.outer.removeClass("lg-zoom-from-image")),this.destroyModules(),this.lGalleryOn=!1,this.isDummyImageRemoved=!1,this.zoomFromOrigin=this.settings.zoomFromOrigin,clearTimeout(this.hideBarTimeout),this.hideBarTimeout=!1,c("html").removeClass("lg-on"),this.outer.removeClass("lg-visible lg-components-open"),this.$backdrop.removeClass("in").css("opacity",0);var g=this.zoomFromOrigin&&s?Math.max(this.settings.startAnimationDuration,this.settings.backdropDuration):this.settings.backdropDuration;return this.$container.removeClass("lg-show-in"),setTimeout(function(){e.zoomFromOrigin&&s&&e.outer.removeClass("lg-zoom-from-image"),e.$container.removeClass("lg-show"),e.resetScrollBar(),e.$backdrop.removeAttr("style").css("transition-duration",e.settings.backdropDuration+"ms"),e.outer.removeClass("lg-closing "+e.settings.startClass),e.getSlideItem(e.index).removeClass("lg-start-end-progress"),e.$inner.empty(),e.lgOpened&&e.LGel.trigger(v.afterClose,{instance:e}),e.$container.get()&&e.$container.get().blur(),e.lgOpened=!1},g+100),g+100},o.prototype.initModules=function(){this.plugins.forEach(function(t){try{t.init()}catch{console.warn("lightGallery:- make sure lightGallery module is properly initiated")}})},o.prototype.destroyModules=function(t){this.plugins.forEach(function(e){try{t?e.destroy():e.closeGallery&&e.closeGallery()}catch{console.warn("lightGallery:- make sure lightGallery module is properly destroyed")}})},o.prototype.refresh=function(t){this.settings.dynamic||this.invalidateItems(),t?this.galleryItems=t:this.galleryItems=this.getItems(),this.updateControls(),this.openGalleryOnItemClick(),this.LGel.trigger(v.updateSlides)},o.prototype.updateControls=function(){this.addSlideVideoInfo(this.galleryItems),this.updateCounterTotal(),this.manageSingleSlideClassName()},o.prototype.destroyGallery=function(){this.destroyModules(!0),this.settings.dynamic||this.invalidateItems(),c(window).off(".lg.global"+this.lgId),this.LGel.off(".lg"),this.$container.remove()},o.prototype.destroy=function(){var t=this.closeGallery(!0);return t?setTimeout(this.destroyGallery.bind(this),t):this.destroyGallery(),t},o}();function R(o,t){return new J(o,t)}/*!
 * lightgallery | 2.7.2 | September 20th 2023
 * http://www.lightgalleryjs.com/
 * Copyright (c) 2020 Sachin Neravath;
 * @license GPLv3
 *//*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var _=function(){return _=Object.assign||function(t){for(var e,i=1,s=arguments.length;i<s;i++){e=arguments[i];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])}return t},_.apply(this,arguments)},tt={thumbnail:!0,animateThumb:!0,currentPagerPosition:"middle",alignThumbnails:"middle",thumbWidth:100,thumbHeight:"80px",thumbMargin:5,appendThumbnailsTo:".lg-components",toggleThumb:!1,enableThumbDrag:!0,enableThumbSwipe:!0,thumbnailSwipeThreshold:10,loadYouTubeThumbnail:!0,youTubeThumbSize:1,thumbnailPluginStrings:{toggleThumbnails:"Toggle thumbnails"}},E={afterAppendSlide:"lgAfterAppendSlide",init:"lgInit",hasVideo:"lgHasVideo",containerResize:"lgContainerResize",updateSlides:"lgUpdateSlides",afterAppendSubHtml:"lgAfterAppendSubHtml",beforeOpen:"lgBeforeOpen",afterOpen:"lgAfterOpen",slideItemLoad:"lgSlideItemLoad",beforeSlide:"lgBeforeSlide",afterSlide:"lgAfterSlide",posterClick:"lgPosterClick",dragStart:"lgDragStart",dragMove:"lgDragMove",dragEnd:"lgDragEnd",beforeNextSlide:"lgBeforeNextSlide",beforePrevSlide:"lgBeforePrevSlide",beforeClose:"lgBeforeClose",afterClose:"lgAfterClose",rotateLeft:"lgRotateLeft",rotateRight:"lgRotateRight",flipHorizontal:"lgFlipHorizontal",flipVertical:"lgFlipVertical",autoplay:"lgAutoplay",autoplayStart:"lgAutoplayStart",autoplayStop:"lgAutoplayStop"},et=function(){function o(t,e){return this.thumbOuterWidth=0,this.thumbTotalWidth=0,this.translateX=0,this.thumbClickable=!1,this.core=t,this.$LG=e,this}return o.prototype.init=function(){this.settings=_(_({},tt),this.core.settings),this.thumbOuterWidth=0,this.thumbTotalWidth=this.core.galleryItems.length*(this.settings.thumbWidth+this.settings.thumbMargin),this.translateX=0,this.setAnimateThumbStyles(),this.core.settings.allowMediaOverlap||(this.settings.toggleThumb=!1),this.settings.thumbnail&&(this.build(),this.settings.animateThumb?(this.settings.enableThumbDrag&&this.enableThumbDrag(),this.settings.enableThumbSwipe&&this.enableThumbSwipe(),this.thumbClickable=!1):this.thumbClickable=!0,this.toggleThumbBar(),this.thumbKeyPress())},o.prototype.build=function(){var t=this;this.setThumbMarkup(),this.manageActiveClassOnSlideChange(),this.$lgThumb.first().on("click.lg touchend.lg",function(e){var i=t.$LG(e.target);!i.hasAttribute("data-lg-item-id")||setTimeout(function(){if(t.thumbClickable&&!t.core.lgBusy){var s=parseInt(i.attr("data-lg-item-id"));t.core.slide(s,!1,!0,!1)}},50)}),this.core.LGel.on(E.beforeSlide+".thumb",function(e){var i=e.detail.index;t.animateThumb(i)}),this.core.LGel.on(E.beforeOpen+".thumb",function(){t.thumbOuterWidth=t.core.outer.get().offsetWidth}),this.core.LGel.on(E.updateSlides+".thumb",function(){t.rebuildThumbnails()}),this.core.LGel.on(E.containerResize+".thumb",function(){!t.core.lgOpened||setTimeout(function(){t.thumbOuterWidth=t.core.outer.get().offsetWidth,t.animateThumb(t.core.index),t.thumbOuterWidth=t.core.outer.get().offsetWidth},50)})},o.prototype.setThumbMarkup=function(){var t="lg-thumb-outer ";this.settings.alignThumbnails&&(t+="lg-thumb-align-"+this.settings.alignThumbnails);var e='<div class="'+t+`">
        <div class="lg-thumb lg-group">
        </div>
        </div>`;this.core.outer.addClass("lg-has-thumb"),this.settings.appendThumbnailsTo===".lg-components"?this.core.$lgComponents.append(e):this.core.outer.append(e),this.$thumbOuter=this.core.outer.find(".lg-thumb-outer").first(),this.$lgThumb=this.core.outer.find(".lg-thumb").first(),this.settings.animateThumb&&this.core.outer.find(".lg-thumb").css("transition-duration",this.core.settings.speed+"ms").css("width",this.thumbTotalWidth+"px").css("position","relative"),this.setThumbItemHtml(this.core.galleryItems)},o.prototype.enableThumbDrag=function(){var t=this,e={cords:{startX:0,endX:0},isMoved:!1,newTranslateX:0,startTime:new Date,endTime:new Date,touchMoveTime:0},i=!1;this.$thumbOuter.addClass("lg-grab"),this.core.outer.find(".lg-thumb").first().on("mousedown.lg.thumb",function(s){t.thumbTotalWidth>t.thumbOuterWidth&&(s.preventDefault(),e.cords.startX=s.pageX,e.startTime=new Date,t.thumbClickable=!1,i=!0,t.core.outer.get().scrollLeft+=1,t.core.outer.get().scrollLeft-=1,t.$thumbOuter.removeClass("lg-grab").addClass("lg-grabbing"))}),this.$LG(window).on("mousemove.lg.thumb.global"+this.core.lgId,function(s){!t.core.lgOpened||i&&(e.cords.endX=s.pageX,e=t.onThumbTouchMove(e))}),this.$LG(window).on("mouseup.lg.thumb.global"+this.core.lgId,function(){!t.core.lgOpened||(e.isMoved?e=t.onThumbTouchEnd(e):t.thumbClickable=!0,i&&(i=!1,t.$thumbOuter.removeClass("lg-grabbing").addClass("lg-grab")))})},o.prototype.enableThumbSwipe=function(){var t=this,e={cords:{startX:0,endX:0},isMoved:!1,newTranslateX:0,startTime:new Date,endTime:new Date,touchMoveTime:0};this.$lgThumb.on("touchstart.lg",function(i){t.thumbTotalWidth>t.thumbOuterWidth&&(i.preventDefault(),e.cords.startX=i.targetTouches[0].pageX,t.thumbClickable=!1,e.startTime=new Date)}),this.$lgThumb.on("touchmove.lg",function(i){t.thumbTotalWidth>t.thumbOuterWidth&&(i.preventDefault(),e.cords.endX=i.targetTouches[0].pageX,e=t.onThumbTouchMove(e))}),this.$lgThumb.on("touchend.lg",function(){e.isMoved?e=t.onThumbTouchEnd(e):t.thumbClickable=!0})},o.prototype.rebuildThumbnails=function(){var t=this;this.$thumbOuter.addClass("lg-rebuilding-thumbnails"),setTimeout(function(){t.thumbTotalWidth=t.core.galleryItems.length*(t.settings.thumbWidth+t.settings.thumbMargin),t.$lgThumb.css("width",t.thumbTotalWidth+"px"),t.$lgThumb.empty(),t.setThumbItemHtml(t.core.galleryItems),t.animateThumb(t.core.index)},50),setTimeout(function(){t.$thumbOuter.removeClass("lg-rebuilding-thumbnails")},200)},o.prototype.setTranslate=function(t){this.$lgThumb.css("transform","translate3d(-"+t+"px, 0px, 0px)")},o.prototype.getPossibleTransformX=function(t){return t>this.thumbTotalWidth-this.thumbOuterWidth&&(t=this.thumbTotalWidth-this.thumbOuterWidth),t<0&&(t=0),t},o.prototype.animateThumb=function(t){if(this.$lgThumb.css("transition-duration",this.core.settings.speed+"ms"),this.settings.animateThumb){var e=0;switch(this.settings.currentPagerPosition){case"left":e=0;break;case"middle":e=this.thumbOuterWidth/2-this.settings.thumbWidth/2;break;case"right":e=this.thumbOuterWidth-this.settings.thumbWidth}this.translateX=(this.settings.thumbWidth+this.settings.thumbMargin)*t-1-e,this.translateX>this.thumbTotalWidth-this.thumbOuterWidth&&(this.translateX=this.thumbTotalWidth-this.thumbOuterWidth),this.translateX<0&&(this.translateX=0),this.setTranslate(this.translateX)}},o.prototype.onThumbTouchMove=function(t){return t.newTranslateX=this.translateX,t.isMoved=!0,t.touchMoveTime=new Date().valueOf(),t.newTranslateX-=t.cords.endX-t.cords.startX,t.newTranslateX=this.getPossibleTransformX(t.newTranslateX),this.setTranslate(t.newTranslateX),this.$thumbOuter.addClass("lg-dragging"),t},o.prototype.onThumbTouchEnd=function(t){t.isMoved=!1,t.endTime=new Date,this.$thumbOuter.removeClass("lg-dragging");var e=t.endTime.valueOf()-t.startTime.valueOf(),i=t.cords.endX-t.cords.startX,s=Math.abs(i)/e;return s>.15&&t.endTime.valueOf()-t.touchMoveTime<30?(s+=1,s>2&&(s+=1),s=s+s*(Math.abs(i)/this.thumbOuterWidth),this.$lgThumb.css("transition-duration",Math.min(s-1,2)+"settings"),i=i*s,this.translateX=this.getPossibleTransformX(this.translateX-i),this.setTranslate(this.translateX)):this.translateX=t.newTranslateX,Math.abs(t.cords.endX-t.cords.startX)<this.settings.thumbnailSwipeThreshold&&(this.thumbClickable=!0),t},o.prototype.getThumbHtml=function(t,e,i){var s=this.core.galleryItems[e].__slideVideoInfo||{},r;s.youtube&&this.settings.loadYouTubeThumbnail?r="//img.youtube.com/vi/"+s.youtube[1]+"/"+this.settings.youTubeThumbSize+".jpg":r=t;var n=i?'alt="'+i+'"':"";return'<div data-lg-item-id="'+e+'" class="lg-thumb-item '+(e===this.core.index?" active":"")+`"
        style="width:`+this.settings.thumbWidth+"px; height: "+this.settings.thumbHeight+`;
            margin-right: `+this.settings.thumbMargin+`px;">
            <img `+n+' data-lg-item-id="'+e+'" src="'+r+`" />
        </div>`},o.prototype.getThumbItemHtml=function(t){for(var e="",i=0;i<t.length;i++)e+=this.getThumbHtml(t[i].thumb,i,t[i].alt);return e},o.prototype.setThumbItemHtml=function(t){var e=this.getThumbItemHtml(t);this.$lgThumb.html(e)},o.prototype.setAnimateThumbStyles=function(){this.settings.animateThumb&&this.core.outer.addClass("lg-animate-thumb")},o.prototype.manageActiveClassOnSlideChange=function(){var t=this;this.core.LGel.on(E.beforeSlide+".thumb",function(e){var i=t.core.outer.find(".lg-thumb-item"),s=e.detail.index;i.removeClass("active"),i.eq(s).addClass("active")})},o.prototype.toggleThumbBar=function(){var t=this;this.settings.toggleThumb&&(this.core.outer.addClass("lg-can-toggle"),this.core.$toolbar.append('<button type="button" aria-label="'+this.settings.thumbnailPluginStrings.toggleThumbnails+'" class="lg-toggle-thumb lg-icon"></button>'),this.core.outer.find(".lg-toggle-thumb").first().on("click.lg",function(){t.core.outer.toggleClass("lg-components-open")}))},o.prototype.thumbKeyPress=function(){var t=this;this.$LG(window).on("keydown.lg.thumb.global"+this.core.lgId,function(e){!t.core.lgOpened||!t.settings.toggleThumb||(e.keyCode===38?(e.preventDefault(),t.core.outer.addClass("lg-components-open")):e.keyCode===40&&(e.preventDefault(),t.core.outer.removeClass("lg-components-open")))})},o.prototype.destroy=function(){this.settings.thumbnail&&(this.$LG(window).off(".lg.thumb.global"+this.core.lgId),this.core.LGel.off(".lg.thumb"),this.core.LGel.off(".thumb"),this.$thumbOuter.remove(),this.core.outer.removeClass("lg-has-thumb"))},o}();/*!
 * lightgallery | 2.7.2 | September 20th 2023
 * http://www.lightgalleryjs.com/
 * Copyright (c) 2020 Sachin Neravath;
 * @license GPLv3
 *//*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var k=function(){return k=Object.assign||function(t){for(var e,i=1,s=arguments.length;i<s;i++){e=arguments[i];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])}return t},k.apply(this,arguments)},it={scale:1,zoom:!0,infiniteZoom:!0,actualSize:!0,showZoomInOutIcons:!1,actualSizeIcons:{zoomIn:"lg-zoom-in",zoomOut:"lg-zoom-out"},enableZoomAfter:300,zoomPluginStrings:{zoomIn:"Zoom in",zoomOut:"Zoom out",viewActualSize:"View actual size"}},I={afterAppendSlide:"lgAfterAppendSlide",init:"lgInit",hasVideo:"lgHasVideo",containerResize:"lgContainerResize",updateSlides:"lgUpdateSlides",afterAppendSubHtml:"lgAfterAppendSubHtml",beforeOpen:"lgBeforeOpen",afterOpen:"lgAfterOpen",slideItemLoad:"lgSlideItemLoad",beforeSlide:"lgBeforeSlide",afterSlide:"lgAfterSlide",posterClick:"lgPosterClick",dragStart:"lgDragStart",dragMove:"lgDragMove",dragEnd:"lgDragEnd",beforeNextSlide:"lgBeforeNextSlide",beforePrevSlide:"lgBeforePrevSlide",beforeClose:"lgBeforeClose",afterClose:"lgAfterClose",rotateLeft:"lgRotateLeft",rotateRight:"lgRotateRight",flipHorizontal:"lgFlipHorizontal",flipVertical:"lgFlipVertical",autoplay:"lgAutoplay",autoplayStart:"lgAutoplayStart",autoplayStop:"lgAutoplayStop"},D=500,st=function(){function o(t,e){return this.core=t,this.$LG=e,this.settings=k(k({},it),this.core.settings),this}return o.prototype.buildTemplates=function(){var t=this.settings.showZoomInOutIcons?'<button id="'+this.core.getIdName("lg-zoom-in")+'" type="button" aria-label="'+this.settings.zoomPluginStrings.zoomIn+'" class="lg-zoom-in lg-icon"></button><button id="'+this.core.getIdName("lg-zoom-out")+'" type="button" aria-label="'+this.settings.zoomPluginStrings.zoomIn+'" class="lg-zoom-out lg-icon"></button>':"";this.settings.actualSize&&(t+='<button id="'+this.core.getIdName("lg-actual-size")+'" type="button" aria-label="'+this.settings.zoomPluginStrings.viewActualSize+'" class="'+this.settings.actualSizeIcons.zoomIn+' lg-icon"></button>'),this.core.outer.addClass("lg-use-transition-for-zoom"),this.core.$toolbar.first().append(t)},o.prototype.enableZoom=function(t){var e=this,i=this.settings.enableZoomAfter+t.detail.delay;this.$LG("body").first().hasClass("lg-from-hash")&&t.detail.delay?i=0:this.$LG("body").first().removeClass("lg-from-hash"),this.zoomableTimeout=setTimeout(function(){!e.isImageSlide(e.core.index)||(e.core.getSlideItem(t.detail.index).addClass("lg-zoomable"),t.detail.index===e.core.index&&e.setZoomEssentials())},i+30)},o.prototype.enableZoomOnSlideItemLoad=function(){this.core.LGel.on(I.slideItemLoad+".zoom",this.enableZoom.bind(this))},o.prototype.getDragCords=function(t){return{x:t.pageX,y:t.pageY}},o.prototype.getSwipeCords=function(t){var e=t.touches[0].pageX,i=t.touches[0].pageY;return{x:e,y:i}},o.prototype.getDragAllowedAxises=function(t,e){var i=this.core.getSlideItem(this.core.index).find(".lg-image").first().get(),s=0,r=0,n=i.getBoundingClientRect();t?(s=i.offsetHeight*t,r=i.offsetWidth*t):e?(s=n.height+e*n.height,r=n.width+e*n.width):(s=n.height,r=n.width);var a=s>this.containerRect.height,l=r>this.containerRect.width;return{allowX:l,allowY:a}},o.prototype.setZoomEssentials=function(){this.containerRect=this.core.$content.get().getBoundingClientRect()},o.prototype.zoomImage=function(t,e,i,s){if(!(Math.abs(e)<=0)){var r=this.containerRect.width/2+this.containerRect.left,n=this.containerRect.height/2+this.containerRect.top+this.scrollTop,a,l;t===1&&(this.positionChanged=!1);var h=this.getDragAllowedAxises(0,e),u=h.allowY,d=h.allowX;this.positionChanged&&(a=this.left/(this.scale-e),l=this.top/(this.scale-e),this.pageX=r-a,this.pageY=n-l,this.positionChanged=!1);var g=this.getPossibleSwipeDragCords(e),m,f,b=r-this.pageX,y=n-this.pageY;if(t-e>1){var p=(t-e)/Math.abs(e);b=(e<0?-b:b)+this.left*(p+(e<0?-1:1)),y=(e<0?-y:y)+this.top*(p+(e<0?-1:1)),m=b/p,f=y/p}else{var p=(t-e)*e;m=b*p,f=y*p}i&&(d?this.isBeyondPossibleLeft(m,g.minX)?m=g.minX:this.isBeyondPossibleRight(m,g.maxX)&&(m=g.maxX):t>1&&(m<g.minX?m=g.minX:m>g.maxX&&(m=g.maxX)),u?this.isBeyondPossibleTop(f,g.minY)?f=g.minY:this.isBeyondPossibleBottom(f,g.maxY)&&(f=g.maxY):t>1&&(f<g.minY?f=g.minY:f>g.maxY&&(f=g.maxY))),this.setZoomStyles({x:m,y:f,scale:t}),this.left=m,this.top=f,s&&this.setZoomImageSize()}},o.prototype.resetImageTranslate=function(t){if(!!this.isImageSlide(t)){var e=this.core.getSlideItem(t).find(".lg-image").first();this.imageReset=!1,e.removeClass("reset-transition reset-transition-y reset-transition-x"),this.core.outer.removeClass("lg-actual-size"),e.css("width","auto").css("height","auto"),setTimeout(function(){e.removeClass("no-transition")},10)}},o.prototype.setZoomImageSize=function(){var t=this,e=this.core.getSlideItem(this.core.index).find(".lg-image").first();setTimeout(function(){var i=t.getCurrentImageActualSizeScale();t.scale>=i&&(e.addClass("no-transition"),t.imageReset=!0)},D),setTimeout(function(){var i=t.getCurrentImageActualSizeScale();if(t.scale>=i){var s=t.getDragAllowedAxises(t.scale);e.css("width",e.get().naturalWidth+"px").css("height",e.get().naturalHeight+"px"),t.core.outer.addClass("lg-actual-size"),s.allowX&&s.allowY?e.addClass("reset-transition"):s.allowX&&!s.allowY?e.addClass("reset-transition-x"):!s.allowX&&s.allowY&&e.addClass("reset-transition-y")}},D+50)},o.prototype.setZoomStyles=function(t){var e=this.core.getSlideItem(this.core.index).find(".lg-img-wrap").first(),i=this.core.getSlideItem(this.core.index).find(".lg-image").first(),s=this.core.outer.find(".lg-current .lg-dummy-img").first();this.scale=t.scale,i.css("transform","scale3d("+t.scale+", "+t.scale+", 1)"),s.css("transform","scale3d("+t.scale+", "+t.scale+", 1)");var r="translate3d("+t.x+"px, "+t.y+"px, 0)";e.css("transform",r)},o.prototype.setActualSize=function(t,e){var i=this;if(!this.zoomInProgress){this.zoomInProgress=!0;var s=this.core.galleryItems[this.core.index];this.resetImageTranslate(t),setTimeout(function(){if(!(!s.src||i.core.outer.hasClass("lg-first-slide-loading"))){var r=i.getCurrentImageActualSizeScale(),n=i.scale;i.core.outer.hasClass("lg-zoomed")?i.scale=1:i.scale=i.getScale(r),i.setPageCords(e),i.beginZoom(i.scale),i.zoomImage(i.scale,i.scale-n,!0,!0)}},50),setTimeout(function(){i.core.outer.removeClass("lg-grabbing").addClass("lg-grab")},60),setTimeout(function(){i.zoomInProgress=!1},D+110)}},o.prototype.getNaturalWidth=function(t){var e=this.core.getSlideItem(t).find(".lg-image").first(),i=this.core.galleryItems[t].width;return i?parseFloat(i):e.get().naturalWidth},o.prototype.getActualSizeScale=function(t,e){var i,s;return t>=e?(i=t/e,s=i||2):s=1,s},o.prototype.getCurrentImageActualSizeScale=function(){var t=this.core.getSlideItem(this.core.index).find(".lg-image").first(),e=t.get().offsetWidth,i=this.getNaturalWidth(this.core.index)||e;return this.getActualSizeScale(i,e)},o.prototype.getPageCords=function(t){var e={};if(t)e.x=t.pageX||t.touches[0].pageX,e.y=t.pageY||t.touches[0].pageY;else{var i=this.core.$content.get().getBoundingClientRect();e.x=i.width/2+i.left,e.y=i.height/2+this.scrollTop+i.top}return e},o.prototype.setPageCords=function(t){var e=this.getPageCords(t);this.pageX=e.x,this.pageY=e.y},o.prototype.manageActualPixelClassNames=function(){var t=this.core.getElementById("lg-actual-size");t.removeClass(this.settings.actualSizeIcons.zoomIn).addClass(this.settings.actualSizeIcons.zoomOut)},o.prototype.beginZoom=function(t){return this.core.outer.removeClass("lg-zoom-drag-transition lg-zoom-dragging"),t>1?(this.core.outer.addClass("lg-zoomed"),this.manageActualPixelClassNames()):this.resetZoom(),t>1},o.prototype.getScale=function(t){var e=this.getCurrentImageActualSizeScale();return t<1?t=1:t>e&&(t=e),t},o.prototype.init=function(){var t=this;if(!!this.settings.zoom){this.buildTemplates(),this.enableZoomOnSlideItemLoad();var e=null;this.core.outer.on("dblclick.lg",function(i){!t.$LG(i.target).hasClass("lg-image")||t.setActualSize(t.core.index,i)}),this.core.outer.on("touchstart.lg",function(i){var s=t.$LG(i.target);i.touches.length===1&&s.hasClass("lg-image")&&(e?(clearTimeout(e),e=null,i.preventDefault(),t.setActualSize(t.core.index,i)):e=setTimeout(function(){e=null},300))}),this.core.LGel.on(I.containerResize+".zoom "+I.rotateRight+".zoom "+I.rotateLeft+".zoom "+I.flipHorizontal+".zoom "+I.flipVertical+".zoom",function(){if(!(!t.core.lgOpened||!t.isImageSlide(t.core.index)||t.core.touchAction)){var i=t.core.getSlideItem(t.core.index).find(".lg-img-wrap").first();t.top=0,t.left=0,t.setZoomEssentials(),t.setZoomSwipeStyles(i,{x:0,y:0}),t.positionChanged=!0}}),this.$LG(window).on("scroll.lg.zoom.global"+this.core.lgId,function(){!t.core.lgOpened||(t.scrollTop=t.$LG(window).scrollTop())}),this.core.getElementById("lg-zoom-out").on("click.lg",function(){if(!!t.isImageSlide(t.core.index)){var i=0;t.imageReset&&(t.resetImageTranslate(t.core.index),i=50),setTimeout(function(){var s=t.scale-t.settings.scale;s<1&&(s=1),t.beginZoom(s),t.zoomImage(s,-t.settings.scale,!0,!t.settings.infiniteZoom)},i)}}),this.core.getElementById("lg-zoom-in").on("click.lg",function(){t.zoomIn()}),this.core.getElementById("lg-actual-size").on("click.lg",function(){t.setActualSize(t.core.index)}),this.core.LGel.on(I.beforeOpen+".zoom",function(){t.core.outer.find(".lg-item").removeClass("lg-zoomable")}),this.core.LGel.on(I.afterOpen+".zoom",function(){t.scrollTop=t.$LG(window).scrollTop(),t.pageX=t.core.outer.width()/2,t.pageY=t.core.outer.height()/2+t.scrollTop,t.scale=1}),this.core.LGel.on(I.afterSlide+".zoom",function(i){var s=i.detail.prevIndex;t.scale=1,t.positionChanged=!1,t.zoomInProgress=!1,t.resetZoom(s),t.resetImageTranslate(s),t.isImageSlide(t.core.index)&&t.setZoomEssentials()}),this.zoomDrag(),this.pinchZoom(),this.zoomSwipe(),this.zoomableTimeout=!1,this.positionChanged=!1,this.zoomInProgress=!1}},o.prototype.zoomIn=function(){if(!!this.isImageSlide(this.core.index)){var t=this.scale+this.settings.scale;this.settings.infiniteZoom||(t=this.getScale(t)),this.beginZoom(t),this.zoomImage(t,Math.min(this.settings.scale,t-this.scale),!0,!this.settings.infiniteZoom)}},o.prototype.resetZoom=function(t){this.core.outer.removeClass("lg-zoomed lg-zoom-drag-transition");var e=this.core.getElementById("lg-actual-size"),i=this.core.getSlideItem(t!==void 0?t:this.core.index);e.removeClass(this.settings.actualSizeIcons.zoomOut).addClass(this.settings.actualSizeIcons.zoomIn),i.find(".lg-img-wrap").first().removeAttr("style"),i.find(".lg-image").first().removeAttr("style"),this.scale=1,this.left=0,this.top=0,this.setPageCords()},o.prototype.getTouchDistance=function(t){return Math.sqrt((t.touches[0].pageX-t.touches[1].pageX)*(t.touches[0].pageX-t.touches[1].pageX)+(t.touches[0].pageY-t.touches[1].pageY)*(t.touches[0].pageY-t.touches[1].pageY))},o.prototype.pinchZoom=function(){var t=this,e=0,i=!1,s=1,r=0,n=this.core.getSlideItem(this.core.index);this.core.outer.on("touchstart.lg",function(a){if(n=t.core.getSlideItem(t.core.index),!!t.isImageSlide(t.core.index)&&a.touches.length===2){if(a.preventDefault(),t.core.outer.hasClass("lg-first-slide-loading"))return;s=t.scale||1,t.core.outer.removeClass("lg-zoom-drag-transition lg-zoom-dragging"),t.setPageCords(a),t.resetImageTranslate(t.core.index),t.core.touchAction="pinch",e=t.getTouchDistance(a)}}),this.core.$inner.on("touchmove.lg",function(a){if(a.touches.length===2&&t.core.touchAction==="pinch"&&(t.$LG(a.target).hasClass("lg-item")||n.get().contains(a.target))){a.preventDefault();var l=t.getTouchDistance(a),h=e-l;if(!i&&Math.abs(h)>5&&(i=!0),i){r=t.scale;var u=Math.max(1,s+-h*.02);t.scale=Math.round((u+Number.EPSILON)*100)/100;var d=t.scale-r;t.zoomImage(t.scale,Math.round((d+Number.EPSILON)*100)/100,!1,!1)}}}),this.core.$inner.on("touchend.lg",function(a){if(t.core.touchAction==="pinch"&&(t.$LG(a.target).hasClass("lg-item")||n.get().contains(a.target))){if(i=!1,e=0,t.scale<=1)t.resetZoom();else{var l=t.getCurrentImageActualSizeScale();if(t.scale>=l){var h=l-t.scale;h===0&&(h=.01),t.zoomImage(l,h,!1,!0)}t.manageActualPixelClassNames(),t.core.outer.addClass("lg-zoomed")}t.core.touchAction=void 0}})},o.prototype.touchendZoom=function(t,e,i,s,r){var n=e.x-t.x,a=e.y-t.y,l=Math.abs(n)/r+1,h=Math.abs(a)/r+1;l>2&&(l+=1),h>2&&(h+=1),n=n*l,a=a*h;var u=this.core.getSlideItem(this.core.index).find(".lg-img-wrap").first(),d={};d.x=this.left+n,d.y=this.top+a;var g=this.getPossibleSwipeDragCords();(Math.abs(n)>15||Math.abs(a)>15)&&(s&&(this.isBeyondPossibleTop(d.y,g.minY)?d.y=g.minY:this.isBeyondPossibleBottom(d.y,g.maxY)&&(d.y=g.maxY)),i&&(this.isBeyondPossibleLeft(d.x,g.minX)?d.x=g.minX:this.isBeyondPossibleRight(d.x,g.maxX)&&(d.x=g.maxX)),s?this.top=d.y:d.y=this.top,i?this.left=d.x:d.x=this.left,this.setZoomSwipeStyles(u,d),this.positionChanged=!0)},o.prototype.getZoomSwipeCords=function(t,e,i,s,r){var n={};if(s){if(n.y=this.top+(e.y-t.y),this.isBeyondPossibleTop(n.y,r.minY)){var a=r.minY-n.y;n.y=r.minY-a/6}else if(this.isBeyondPossibleBottom(n.y,r.maxY)){var l=n.y-r.maxY;n.y=r.maxY+l/6}}else n.y=this.top;if(i){if(n.x=this.left+(e.x-t.x),this.isBeyondPossibleLeft(n.x,r.minX)){var h=r.minX-n.x;n.x=r.minX-h/6}else if(this.isBeyondPossibleRight(n.x,r.maxX)){var u=n.x-r.maxX;n.x=r.maxX+u/6}}else n.x=this.left;return n},o.prototype.isBeyondPossibleLeft=function(t,e){return t>=e},o.prototype.isBeyondPossibleRight=function(t,e){return t<=e},o.prototype.isBeyondPossibleTop=function(t,e){return t>=e},o.prototype.isBeyondPossibleBottom=function(t,e){return t<=e},o.prototype.isImageSlide=function(t){var e=this.core.galleryItems[t];return this.core.getSlideType(e)==="image"},o.prototype.getPossibleSwipeDragCords=function(t){var e=this.core.getSlideItem(this.core.index).find(".lg-image").first(),i=this.core.mediaContainerPosition.bottom,s=e.get().getBoundingClientRect(),r=s.height,n=s.width;t&&(r=r+t*r,n=n+t*n);var a=(r-this.containerRect.height)/2,l=(this.containerRect.height-r)/2+i,h=(n-this.containerRect.width)/2,u=(this.containerRect.width-n)/2,d={minY:a,maxY:l,minX:h,maxX:u};return d},o.prototype.setZoomSwipeStyles=function(t,e){t.css("transform","translate3d("+e.x+"px, "+e.y+"px, 0)")},o.prototype.zoomSwipe=function(){var t=this,e={},i={},s=!1,r=!1,n=!1,a=new Date,l=new Date,h,u,d=this.core.getSlideItem(this.core.index);this.core.$inner.on("touchstart.lg",function(g){if(!!t.isImageSlide(t.core.index)&&(d=t.core.getSlideItem(t.core.index),(t.$LG(g.target).hasClass("lg-item")||d.get().contains(g.target))&&g.touches.length===1&&t.core.outer.hasClass("lg-zoomed"))){g.preventDefault(),a=new Date,t.core.touchAction="zoomSwipe",u=t.core.getSlideItem(t.core.index).find(".lg-img-wrap").first();var m=t.getDragAllowedAxises(0);n=m.allowY,r=m.allowX,(r||n)&&(e=t.getSwipeCords(g)),h=t.getPossibleSwipeDragCords(),t.core.outer.addClass("lg-zoom-dragging lg-zoom-drag-transition")}}),this.core.$inner.on("touchmove.lg",function(g){if(g.touches.length===1&&t.core.touchAction==="zoomSwipe"&&(t.$LG(g.target).hasClass("lg-item")||d.get().contains(g.target))){g.preventDefault(),t.core.touchAction="zoomSwipe",i=t.getSwipeCords(g);var m=t.getZoomSwipeCords(e,i,r,n,h);(Math.abs(i.x-e.x)>15||Math.abs(i.y-e.y)>15)&&(s=!0,t.setZoomSwipeStyles(u,m))}}),this.core.$inner.on("touchend.lg",function(g){if(t.core.touchAction==="zoomSwipe"&&(t.$LG(g.target).hasClass("lg-item")||d.get().contains(g.target))){if(g.preventDefault(),t.core.touchAction=void 0,t.core.outer.removeClass("lg-zoom-dragging"),!s)return;s=!1,l=new Date;var m=l.valueOf()-a.valueOf();t.touchendZoom(e,i,r,n,m)}})},o.prototype.zoomDrag=function(){var t=this,e={},i={},s=!1,r=!1,n=!1,a=!1,l,h,u,d;this.core.outer.on("mousedown.lg.zoom",function(g){if(!!t.isImageSlide(t.core.index)){var m=t.core.getSlideItem(t.core.index);if(t.$LG(g.target).hasClass("lg-item")||m.get().contains(g.target)){l=new Date,d=t.core.getSlideItem(t.core.index).find(".lg-img-wrap").first();var f=t.getDragAllowedAxises(0);a=f.allowY,n=f.allowX,t.core.outer.hasClass("lg-zoomed")&&t.$LG(g.target).hasClass("lg-object")&&(n||a)&&(g.preventDefault(),e=t.getDragCords(g),u=t.getPossibleSwipeDragCords(),s=!0,t.core.outer.removeClass("lg-grab").addClass("lg-grabbing lg-zoom-drag-transition lg-zoom-dragging"))}}}),this.$LG(window).on("mousemove.lg.zoom.global"+this.core.lgId,function(g){if(s){r=!0,i=t.getDragCords(g);var m=t.getZoomSwipeCords(e,i,n,a,u);t.setZoomSwipeStyles(d,m)}}),this.$LG(window).on("mouseup.lg.zoom.global"+this.core.lgId,function(g){if(s){if(h=new Date,s=!1,t.core.outer.removeClass("lg-zoom-dragging"),r&&(e.x!==i.x||e.y!==i.y)){i=t.getDragCords(g);var m=h.valueOf()-l.valueOf();t.touchendZoom(e,i,n,a,m)}r=!1}t.core.outer.removeClass("lg-grabbing").addClass("lg-grab")})},o.prototype.closeGallery=function(){this.resetZoom(),this.zoomInProgress=!1},o.prototype.destroy=function(){this.$LG(window).off(".lg.zoom.global"+this.core.lgId),this.core.LGel.off(".lg.zoom"),this.core.LGel.off(".zoom"),clearTimeout(this.zoomableTimeout),this.zoomableTimeout=!1},o}();/*!
 * lightgallery | 2.7.2 | September 20th 2023
 * http://www.lightgalleryjs.com/
 * Copyright (c) 2020 Sachin Neravath;
 * @license GPLv3
 *//*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var x=function(){return x=Object.assign||function(t){for(var e,i=1,s=arguments.length;i<s;i++){e=arguments[i];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])}return t},x.apply(this,arguments)},ot={autoplayFirstVideo:!0,youTubePlayerParams:!1,vimeoPlayerParams:!1,wistiaPlayerParams:!1,gotoNextSlideOnVideoEnd:!0,autoplayVideoOnSlide:!1,videojs:!1,videojsTheme:"",videojsOptions:{}},A={afterAppendSlide:"lgAfterAppendSlide",init:"lgInit",hasVideo:"lgHasVideo",containerResize:"lgContainerResize",updateSlides:"lgUpdateSlides",afterAppendSubHtml:"lgAfterAppendSubHtml",beforeOpen:"lgBeforeOpen",afterOpen:"lgAfterOpen",slideItemLoad:"lgSlideItemLoad",beforeSlide:"lgBeforeSlide",afterSlide:"lgAfterSlide",posterClick:"lgPosterClick",dragStart:"lgDragStart",dragMove:"lgDragMove",dragEnd:"lgDragEnd",beforeNextSlide:"lgBeforeNextSlide",beforePrevSlide:"lgBeforePrevSlide",beforeClose:"lgBeforeClose",afterClose:"lgAfterClose",rotateLeft:"lgRotateLeft",rotateRight:"lgRotateRight",flipHorizontal:"lgFlipHorizontal",flipVertical:"lgFlipVertical",autoplay:"lgAutoplay",autoplayStart:"lgAutoplayStart",autoplayStop:"lgAutoplayStop"},B=function(o){return Object.keys(o).map(function(t){return encodeURIComponent(t)+"="+encodeURIComponent(o[t])}).join("&")},rt=function(o){var t=o.slice(1).split("&").map(function(e){return e.split("=")}).reduce(function(e,i){var s=i.map(decodeURIComponent),r=s[0],n=s[1];return e[r]=n,e},{});return t},nt=function(o,t){if(!o.youtube)return"";var e=o.youtube[2]?rt(o.youtube[2]):"",i={wmode:"opaque",autoplay:0,mute:1,enablejsapi:1},s=t||{},r=x(x(x({},i),s),e),n="?"+B(r);return n},at=function(o){return o.includes("youtube-nocookie.com")},lt=function(o,t){if(!t||!t.vimeo)return"";var e=t.vimeo[2]||"",i=o&&Object.keys(o).length!==0?"&"+B(o):"",s=t.vimeo[0].split("/").pop()||"",r=s.split("?")[0]||"",n=r.split("#")[0],a=t.vimeo[1]!==n;a&&(e=e.replace("/"+n,"")),e=e[0]=="?"?"&"+e.slice(1):e||"";var l="?autoplay=0&muted=1"+(a?"&h="+n:"")+i+e;return l},W=function(){function o(t){return this.core=t,this.settings=x(x({},ot),this.core.settings),this}return o.prototype.init=function(){var t=this;this.core.LGel.on(A.hasVideo+".video",this.onHasVideo.bind(this)),this.core.LGel.on(A.posterClick+".video",function(){var e=t.core.getSlideItem(t.core.index);t.loadVideoOnPosterClick(e)}),this.core.LGel.on(A.slideItemLoad+".video",this.onSlideItemLoad.bind(this)),this.core.LGel.on(A.beforeSlide+".video",this.onBeforeSlide.bind(this)),this.core.LGel.on(A.afterSlide+".video",this.onAfterSlide.bind(this))},o.prototype.onSlideItemLoad=function(t){var e=this,i=t.detail,s=i.isFirstSlide,r=i.index;this.settings.autoplayFirstVideo&&s&&r===this.core.index&&setTimeout(function(){e.loadAndPlayVideo(r)},200),!s&&this.settings.autoplayVideoOnSlide&&r===this.core.index&&this.loadAndPlayVideo(r)},o.prototype.onHasVideo=function(t){var e=t.detail,i=e.index,s=e.src,r=e.html5Video,n=e.hasPoster;n||(this.appendVideos(this.core.getSlideItem(i),{src:s,addClass:"lg-object",index:i,html5Video:r}),this.gotoNextSlideOnVideoEnd(s,i))},o.prototype.onBeforeSlide=function(t){if(this.core.lGalleryOn){var e=t.detail.prevIndex;this.pauseVideo(e)}},o.prototype.onAfterSlide=function(t){var e=this,i=t.detail,s=i.index,r=i.prevIndex,n=this.core.getSlideItem(s);this.settings.autoplayVideoOnSlide&&s!==r&&n.hasClass("lg-complete")&&setTimeout(function(){e.loadAndPlayVideo(s)},100)},o.prototype.loadAndPlayVideo=function(t){var e=this.core.getSlideItem(t),i=this.core.galleryItems[t];i.poster?this.loadVideoOnPosterClick(e,!0):this.playVideo(t)},o.prototype.playVideo=function(t){this.controlVideo(t,"play")},o.prototype.pauseVideo=function(t){this.controlVideo(t,"pause")},o.prototype.getVideoHtml=function(t,e,i,s){var r="",n=this.core.galleryItems[i].__slideVideoInfo||{},a=this.core.galleryItems[i],l=a.title||a.alt;l=l?'title="'+l+'"':"";var h=`allowtransparency="true"
            frameborder="0"
            scrolling="no"
            allowfullscreen
            mozallowfullscreen
            webkitallowfullscreen
            oallowfullscreen
            msallowfullscreen`;if(n.youtube){var u="lg-youtube"+i,d=nt(n,this.settings.youTubePlayerParams),g=at(t),m=g?"//www.youtube-nocookie.com/":"//www.youtube.com/";r='<iframe allow="autoplay" id='+u+' class="lg-video-object lg-youtube '+e+'" '+l+' src="'+m+"embed/"+(n.youtube[1]+d)+'" '+h+"></iframe>"}else if(n.vimeo){var u="lg-vimeo"+i,f=lt(this.settings.vimeoPlayerParams,n);r='<iframe allow="autoplay" id='+u+' class="lg-video-object lg-vimeo '+e+'" '+l+' src="//player.vimeo.com/video/'+(n.vimeo[1]+f)+'" '+h+"></iframe>"}else if(n.wistia){var b="lg-wistia"+i,f=B(this.settings.wistiaPlayerParams);f=f?"?"+f:"",r='<iframe allow="autoplay" id="'+b+'" src="//fast.wistia.net/embed/iframe/'+(n.wistia[4]+f)+'" '+l+' class="wistia_embed lg-video-object lg-wistia '+e+'" name="wistia_embed" '+h+"></iframe>"}else if(n.html5){for(var y="",p=0;p<s.source.length;p++)y+='<source src="'+s.source[p].src+'" type="'+s.source[p].type+'">';if(s.tracks)for(var C=function(z){var G="",w=s.tracks[z];Object.keys(w||{}).forEach(function(L){G+=L+'="'+w[L]+'" '}),y+="<track "+G+">"},p=0;p<s.tracks.length;p++)C(p);var O="",M=s.attributes||{};Object.keys(M||{}).forEach(function(z){O+=z+'="'+M[z]+'" '}),r='<video class="lg-video-object lg-html5 '+(this.settings.videojs&&this.settings.videojsTheme?this.settings.videojsTheme+" ":"")+" "+(this.settings.videojs?" video-js":"")+'" '+O+`>
                `+y+`
                Your browser does not support HTML5 video.
            </video>`}return r},o.prototype.appendVideos=function(t,e){var i,s=this.getVideoHtml(e.src,e.addClass,e.index,e.html5Video);t.find(".lg-video-cont").append(s);var r=t.find(".lg-video-object").first();if(e.html5Video&&r.on("mousedown.lg.video",function(n){n.stopPropagation()}),this.settings.videojs&&((i=this.core.galleryItems[e.index].__slideVideoInfo)===null||i===void 0?void 0:i.html5))try{return videojs(r.get(),this.settings.videojsOptions)}catch{console.error("lightGallery:- Make sure you have included videojs")}},o.prototype.gotoNextSlideOnVideoEnd=function(t,e){var i=this,s=this.core.getSlideItem(e).find(".lg-video-object").first(),r=this.core.galleryItems[e].__slideVideoInfo||{};if(this.settings.gotoNextSlideOnVideoEnd){if(r.html5)s.on("ended",function(){i.core.goToNextSlide()});else if(r.vimeo)try{new Vimeo.Player(s.get()).on("ended",function(){i.core.goToNextSlide()})}catch{console.error("lightGallery:- Make sure you have included //github.com/vimeo/player.js")}else if(r.wistia)try{window._wq=window._wq||[],window._wq.push({id:s.attr("id"),onReady:function(n){n.bind("end",function(){i.core.goToNextSlide()})}})}catch{console.error("lightGallery:- Make sure you have included //fast.wistia.com/assets/external/E-v1.js")}}},o.prototype.controlVideo=function(t,e){var i=this.core.getSlideItem(t).find(".lg-video-object").first(),s=this.core.galleryItems[t].__slideVideoInfo||{};if(!!i.get()){if(s.youtube)try{i.get().contentWindow.postMessage('{"event":"command","func":"'+e+'Video","args":""}',"*")}catch(r){console.error("lightGallery:- "+r)}else if(s.vimeo)try{new Vimeo.Player(i.get())[e]()}catch{console.error("lightGallery:- Make sure you have included //github.com/vimeo/player.js")}else if(s.html5)if(this.settings.videojs)try{videojs(i.get())[e]()}catch{console.error("lightGallery:- Make sure you have included videojs")}else i.get()[e]();else if(s.wistia)try{window._wq=window._wq||[],window._wq.push({id:i.attr("id"),onReady:function(r){r[e]()}})}catch{console.error("lightGallery:- Make sure you have included //fast.wistia.com/assets/external/E-v1.js")}}},o.prototype.loadVideoOnPosterClick=function(t,e){var i=this;if(t.hasClass("lg-video-loaded"))e&&this.playVideo(this.core.index);else if(t.hasClass("lg-has-video"))this.playVideo(this.core.index);else{t.addClass("lg-has-video");var s=void 0,r=this.core.galleryItems[this.core.index].src,n=this.core.galleryItems[this.core.index].video;n&&(s=typeof n=="string"?JSON.parse(n):n);var a=this.appendVideos(t,{src:r,addClass:"",index:this.core.index,html5Video:s});this.gotoNextSlideOnVideoEnd(r,this.core.index);var l=t.find(".lg-object").first().get();t.find(".lg-video-cont").first().append(l),t.addClass("lg-video-loading"),a&&a.ready(function(){a.on("loadedmetadata",function(){i.onVideoLoadAfterPosterClick(t,i.core.index)})}),t.find(".lg-video-object").first().on("load.lg error.lg loadedmetadata.lg",function(){setTimeout(function(){i.onVideoLoadAfterPosterClick(t,i.core.index)},50)})}},o.prototype.onVideoLoadAfterPosterClick=function(t,e){t.addClass("lg-video-loaded"),this.playVideo(e)},o.prototype.destroy=function(){this.core.LGel.off(".lg.video"),this.core.LGel.off(".video")},o}();window.lightgallery=function(){R(document.getElementById("lightgallery"),{plugins:[st,et,W],speed:500,selector:".item",thumnail:!0,animateThumb:!0,zoomFromOrigin:!0})};window.filmLightGallery=function(){R(document.getElementById("filmLightGallery"),{plugins:[W],speed:500,videojs:!0,selector:".item"})};window.imgZoom=function(){$("#contents-container img").on("click",function(){window.open($(this).attr("src"),"_blank")})};window.$=window.jQuery=require("jquery");window.Popper=require("popper.js");require("bootstrap-v4-rtl");

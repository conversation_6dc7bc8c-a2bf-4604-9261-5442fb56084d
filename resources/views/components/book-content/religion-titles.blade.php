<div wire:key="{{ rand() }}" x-data="{ selected_title: {{ $this->Title?->id ?? 1 }} }">

    <!-- Tab panes -->
    @if (count($this->titles) > 0)

        <div class="content-tabs  row" wire:key="{{ rand() }}">
            @foreach ($this->Titles as $title)
                <div class="col-6 col-lg-3  ">

                    @if ($title->subtitles->count() > 0)
                        <div class=" d-flex title-dropdown h-100" data-toggle="dropdown" data-hover="dropdown" data-delay="1000"
                            data-close-others="false">
                            <button
                                class="btn btn-white tab btn-block mt-0  mb-2  {{ in_array($this->Title?->id ?? 1, $title->subtitles->pluck('id')->toArray()) ? 'active' : '' }}">{{ $title->name }}</button>

                            <div class="dropdown-menu ">
                                <div class="d-flex flex-column justify-content-center">

                                    <div class="dropdown-menu-items">

                                        @foreach ($title->subtitles as $item)
                                            <a wire:loading.attr="disabled" wire:key="{{ rand() }}"
                                                wire:click="changeTitle({{ $item->id }})" type="button"
                                                @click.prevent="selected_title={{ $item->id }}"
                                                class="dropdown-item "
                                                :class="selected_title == {{ $item->id }} ? 'active text-white' : ''">{{ $item->name }}</a>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        </div>
                    @else
                        <button wire:loading.attr="disabled" wire:key="{{ rand() }}"
                            wire:click="changeTitle({{ $title->id }})" type="button"
                            @click.prevent="selected_title={{ $title->id }}"
                            class="btn btn-white tab btn-block mt-0 mb-2  "
                            :class="selected_title == {{ $title->id }} ? 'active' : ''">{{ $title->name }}</button>
                    @endif
                </div>
            @endforeach
        </div>

    @endif
</div>

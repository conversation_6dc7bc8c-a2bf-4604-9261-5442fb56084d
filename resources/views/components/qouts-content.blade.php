<div class="quotations" wire:key="type3_{{ $title_id }}">
    <div class="gap-1 d-flex flex-column">
        @if (count($data) == 0)
            <div class=" d-flex align-items-center justify-content-center"
                style="height: 350px; background-color:white; border-radius:0.5rem;overflow:hidden;">
                <div class="text-center no-item ">
                    <i class="mb-auto fa fa-quote-right fa-3x"></i>
                    <h3>عذراً</h3>
                    <p>لم يتم إضافة أي إقتباسات لهذا العنوان</p>
                </div>
            </div>
        @else
            @foreach ($data as $item)
                <div class="quotation-card ">
                    <div class="card">
                        <h1 class="card-header">
                            <i class="icon fas fa-quote-right"></i>
                            {{ $item['name'] }}
                        </h1>
                        <div class="card-body">
                            <blockquote class="mb-0 blockquote">
                                <div class="collapse" id="show-more-collapse{{ $loop->index }}" aria-expanded="false">
                                    {!! add_highlight_to_text($item['text'], $hightlight??"") !!}
                                </div>
                                <a role="button" class="collapsed show-more" data-toggle="collapse"
                                    href="#show-more-collapse{{ $loop->index }}" aria-expanded="false"
                                    aria-controls="show-more-collapse{{ $loop->index }}"></a>
                            </blockquote>
                        </div>
                    </div>

                </div>
            @endforeach
            @if(!($isModalContent??false))
            @include('components.book-content.prev-next-buttons')
            @endif
        @endif
    </div>
</div>

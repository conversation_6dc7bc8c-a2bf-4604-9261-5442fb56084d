<div wire:key="{{ rand() }}">


    @livewire('components.download-book-content-modal', ['book_id' => $book_id, 'title_id' => $title_id], key($title_id ?? rand()))

    @livewire('components.show-book-content-modal', key(rand()))

    {{-- <div wire:loading.block class="overlay">
        <div class="overlay__inner">
            <div class="overlay__content"><span class="spinner"></span></div>
        </div>
    </div> --}}

    <!-- Nav pills -->
    @if ($title_id == 0)
        <div class="empty">
            @if ($book_cover)
                <img src="{{ url('uploads/' . $book_cover) }}" alt="">
            @else
                <img src="{{ url('images/not-found.svg') }}" alt="" srcset="">
            @endif
            <p>يمكنك تصفح المواضيع من خلال تصفح التصنيفات واختيار مسألة محددة
                من القائمة الموجودة على يمين الصفحة</p>
        </div>
    @else
        @if (!$this->content)
            <div class="text-center error-404">
                <h1>404</h1>
                <p>عذراً .... العنوان الذي تبحث عنه غير موجود </p>
            </div>
        @else
            {{-- @if ($platform_type == 2)
            <ul class="nav nav-tabs services-type ">
                <li class="nav-item flex-fill">
                    <a class="nav-link{{ in_array($content_id, [1, 2, 3, 4]) ? ' active' : '' }}" data-toggle="pill"
       href="#e">الخدمات العلمية </a>
       </li>
    <li class="nav-item flex-fill">
        <a class="nav-link{{ in_array($content_id, [5, 6, 7, 8]) ? ' active' : '' }}" data-toggle="pill" href="#s">خدمات
            اخرى</a>
    </li>
    </ul>

    <!-- Tab panes -->
    <div class="tab-content">
        <div class="tab-pane container fade {{ in_array($content_id, [1, 2, 3, 4]) ? ' active show' : '' }}" id="e">
            <div class="row">
                <div class="col-6 col-lg-3 content-type-item ">
                    <button wire:loading.attr="disabled" wire:click="toggle_content(1)" type="button" class="btn btn-service-type
                        btn-block{{ $content_id == 1 ? ' active' : '' }}">الحديث</button>
                </div>
                {{-- @if ($this->available_services->video_contents > 0) --}}
            {{-- <div class="col-6 col-lg-3 content-type-item ">
            <button wire:loading.attr="disabled" wire:click="toggle_content(2)" type="button"
                class="btn btn-service-type
                        btn-block{{ $content_id == 2 ? ' active' : '' }}">التخريج</button>
            </div> --}}
            {{-- @endif --}}
            {{-- @if ($this->available_services->quotations > 0) --}}

            {{-- <div class="col-6 col-lg-3 content-type-item">
            <button wire:loading.attr="disabled" wire:click="toggle_content(3)" type="button"
                class="btn btn-service-type
                        btn-block{{ $content_id == 3 ? ' active' : '' }}">الدراسة
            والحكم</button>
        </div> --}}
            {{-- @endif
                @if ($this->available_services->refrences > 0) --}}
            {{-- <div class="col-6 col-lg-3 content-type-item ">
            <button wire:loading.attr="disabled" wire:click="toggle_content(4)" type="button"
                class="btn btn-service-type
                        btn-block{{ $content_id == 4 ? ' active' : '' }}">الخلاصة</button>
              </div> --}}
            {{-- @endif --}}

            {{-- </div>
         </div>
        <div class="tab-pane container fade{{ in_array($content_id, [5, 6, 7, 8]) ? ' active show' : '' }}" id="s">
            <div class="row">
        {{-- @if ($this->available_services->goals > 0) --}}
            {{-- <div class="col-6 col-lg-3 content-type-item ">
            <button wire:loading.attr="disabled" wire:click="toggle_content(5)" type="button"
                class="btn btn-service-type
                        btn-block{{ $content_id == 5 ? ' active' : '' }}">طرق
             الحديث</button>
              </div> --}}
            {{-- @endif
                @if ($this->available_services->activities > 0) --}}
            {{-- <div class="col-6 col-lg-3 content-type-item ">
            <button wire:loading.attr="disabled" wire:click="toggle_content(6)" type="button"
                class="btn btn-service-type
                        btn-block{{ $content_id == 6 ? ' active' : '' }}">اقوال
                  النقاد</button> --}}
            {{-- </div> --}}
            {{-- @endif
                @if ($this->available_services->calenders > 0) --}}

            {{-- @endif
                 @if ($this->available_services->others > 0) --}}

            {{-- @endif --}}
            {{-- </div>
                </div>
              </div> --}}
            {{-- @else --}}
            @if (\App\Facades\Panel::currentPanel() == 'curriculums')
                <x-book-content.curriculums-services-types />
            @else
                <x-book-content.services-types />
            @endif
            {{-- @endif --}}
            {{-- if content type is text content --}}
            <div class="contents-container" id="contents-container" itemscope itemtype="http://schema.org/Article">
                <div id='content-wrapper'  itemprop="articleBody">
                    @if ($content_id == 0)
                        <div wire:key="ctype1_{{ $title_id }}">
                            <div class="mt-2 row">
                                <div class="text-right col" wire:ignore>
                                    <a class="toggle_show_index" @click="hideIndex=!hideIndex" x-cloak
                                        x-text="hideIndex?'إظهار العناوين':'إخفاء العناوين'" href="#"></a>

                                </div>
                                <div class="text-left col">

                                    <span> حجم الخط: </span>
                                    <input type="button" class="increase" value=" + ">
                                    <input type="button" class="decrease" value=" - " />

                                </div>

                            </div>


                            <div class="content">
                                @if (empty($this->content))
                                    <div class=" d-flex align-items-center justify-content-center"
                                        style="height: 350px">
                                        <div class="text-center no-item ">
                                            <i class="mb-auto fas fa-pen-alt fa-3x"></i>
                                            <h3>عذراً</h3>
                                            <p>لم يتم إضافة أي محتوى لهذا العنوان</p>
                                        </div>
                                    </div>
                                @else
                                    <div class="content-text-container">
                                        <div class="content-text">
                                            {!! add_highlight_to_text($this->content->text, $hightlight) !!}
                                        </div>

                                        @include('components.book-content.prev-next-buttons')
                                    </div>
                                @endif
                            </div>
                        </div>

                        {{-- if content type is video content --}}
                    @else
                        @if ($this->content->view_type == 1)
                            @include('components.text-content', [
                                'title_id' => $title_id,
                                'data' => $this->content->text,
                            ])
                        @elseif($this->content->view_type == 2)
                            @include('components.video-content', [
                                'title_id' => $title_id,
                                'data' => $this->content->videos,
                            ])
                        @elseif($this->content->view_type == 3)
                            @include('components.qouts-content', [
                                'title_id' => $title_id,
                                'data' => $this->content->qoutations,
                            ])
                        @elseif($this->content->view_type == 4)
                            @include('components.references-content', [
                                'title_id' => $title_id,
                                'data' => $this->content->refrences,
                            ])
                        @elseif($this->content->view_type == 5)
                            @include('components.activities-content', [
                                'title_id' => $title_id,
                                'data' => $this->content->activities,
                            ])
                        @elseif($this->content->view_type == 6)
                            @include('components.calenders-content', [
                                'title_id' => $title_id,
                                'data' => $this->content->calenders,
                            ])
                        @elseif($this->content->view_type == 7)
                            @include('components.texts-content', [
                                'title_id' => $title_id,
                                'data' => $this->content->texts,
                            ])
                        @elseif ($this->content->view_type == 8)
                            @include('components.tafseer-content', [
                                'title_id' => $title_id,
                                'data' => $this->content->data,
                            ])
                        @elseif ($this->content->view_type == 9)
                            @include('components.tadabor-content', [
                                'title_id' => $title_id,
                                'data' => $this->content->data,
                            ])
                        @endif
                    @endif
                </div>
                {{-- @if (in_array($content_id, [])) --}}


                <div class="share">
                    <div class="row">
                        <div class="col-md-6">
                            <span class="title">شارك المحتوى</span>
                        </div>

                        <div class="col-md-6 ">

                            <nav
                                class="social-share d-flex justify-content-md-end justify-content-sm-start align-items-center">

                                <a class="nav-link" target="_blank"
                                    href="https://api.whatsapp.com/send/?text={{ $this->page_url }}"><i
                                        class="fab fa-whatsapp"></i>
                                </a>
                                <a class="nav-link" target="_blank" href="mailto:?&body={{ $this->page_url }}"><i
                                        class="fas fa-envelope"></i></a>
                                <a class="nav-link" target="_blank"
                                    href="https://www.linkedin.com/shareArticle?mini=true&url={{ $this->page_url }}"><i
                                        class="fab fa-linkedin-in"></i></a>
                                <a class="nav-link" target="_blank"
                                    href="https://twitter.com/intent/tweet?url={{ $this->page_url }}"><i
                                        class="fab fa-twitter"></i></a>
                                <a class="nav-link" target="_blank"
                                    href="https://www.facebook.com/sharer/sharer.php?u={{ $this->page_url }}"><i
                                        class="fab fa-facebook-f"></i></a>
                            </nav>
                        </div>
                    </div>
                </div>


                @if ($book_id != 115 && $book_id != 3 && $book_id != 122)
                    @if (!$isCommentSave)
                        <div class="rate" wire:key='rate-{{ $this->content->id }}'>


                            <div class="row">
                                <div class="col-md-6">
                                    <span class="title">شاركنا تقيمك لجودة المحتوى</span>
                                </div>

                                <div class="col-md-6">

                                    <nav
                                        class="d-flex justify-content-md-end justify-content-sm-start align-items-center mx-2 gap-5">
                                        @if ($stars = $this->getRateContentByCookies($this->content->id))

                                            @foreach (range(1, 5) as $star)
                                                <svg wire:ignore xmlns="http://www.w3.org/2000/svg" fill="none"
                                                    viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
                                                    height='20' width='20'
                                                    class=" star {{ $star <= $stars ? 'check' : '' }}">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        d="M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z" />
                                                </svg>
                                            @endforeach
                                        @else
                                            @foreach (range(1, 5) as $star)
                                                <svg wire:ignore xmlns="http://www.w3.org/2000/svg" fill="none"
                                                    viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
                                                    height='20' width='20'
                                                    class="star clickable-star cursor-pointer "
                                                    wire:click='rateContent({{ $star }})'>
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        d="M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z" />
                                                </svg>
                                            @endforeach
                                        @endif

                                    </nav>
                                </div>
                            </div>

                        </div>
                    @endif

                    <div class="comment" wire:key='comment-{{ $this->content->id }}'
                        wire:init='resetValue(["isCommentSave","comment"])'>
                        <div class="">

                            @if ($isCommentSave)
                                <div class=" d-flex align-items-center justify-content-center" style="height: 350px">
                                    <div class="send-successfully text-center ">
                                        <i class="fas fa-envelope-open-text fa-3x  mb-auto"></i>
                                        <h3>شكراً لك</h3>
                                        <p>تم الإرسال بنجاح ...</p>
                                    </div>
                                </div>
                            @else
                                <div class="">
                                    <span class="title">أضف ملاحظة</span>
                                </div>

                                <form wire:submit.prevent='saveComment'>
                                    <div class="form-group">
                                        <textarea wire:model.defer="comment" placeholder="اكتب ملاحظتك هنا ...." class="form-control" rows="5"></textarea>
                                        @error('comment')
                                            <span class="error">{{ $message }}</span>
                                        @enderror

                                    </div>

                                    <div class="text-left">
                                        <button type="submit" class="btn btn-kholasa">إرسال</button>
                                    </div>
                                </form>
                            @endif
                        </div>
                    </div>
                @endif
                {{-- @endif --}}
            </div>
        @endif
    @endif





</div>
@push('scripts')
    <script type="text/javascript">
        if (!String.prototype.replaceLast) {
            String.prototype.replaceLast = function(find, replace) {
                var index = this.lastIndexOf(find);

                if (index >= 0) {
                    return this.substring(0, index) + replace + this.substring(index + find.length);
                }

                return this.toString();
            };
        };

        $(document).ready(function() {
            function iniFontSizeButton() {
                var pattern = /font-size:\d*\d*.*\ /i;
                // Increase Font Size
                $(".increase").click(function() {
                    $('.content-text *').each(function() {
                        var element = $(this);
                        var currentSize = element.css('font-size');
                        var currentSize = parseFloat(currentSize) + 1;
                        var style = element.attr("style") ?? "";
                        var style = style + " ";
                        var style = style.replace(pattern, "");
                        var style = style.replaceLast(";", "");
                        var style = style + ";font-size:" + currentSize + "px !important";
                        element.attr("style", style);
                    });
                    return false;
                });

                // Decrease Font Size
                $(".decrease").click(function() {
                    $('.content-text *').each(function() {
                        var element = $(this);
                        var currentSize = element.css('font-size');
                        var currentSize = parseFloat(currentSize) - 1;
                        var style = element.attr("style") ?? "";
                        var style = style + " ";
                        var style = style.replace(pattern, "");
                        var style = style.replaceLast(";", "");
                        var style = style + ";font-size:" + currentSize + "px !important";
                        element.attr("style", style);
                    });
                    return false;
                });
            }

            function initLinks() {
                $('.content-text a').each(function() {
                    $(this).click(function(e) {
                        if (this.href.includes("{{ config('panels.panels.kholasah.domain').'/book/' }}")|| this.href.includes("{{ config('panels.panels.religions.domain').'/book/' }}") || this.href.includes("{{ config('panels.panels.sahihalsunnah.domain').'/book/' }}") || this.href.includes("{{ config('panels.panels.curriculums.domain').'/book/' }}")) {
                            e.preventDefault();
                            Livewire.emit('loadContent', this.href);
                            $('#show-content').modal('show');
                        }

                    });
                });
            }

            function getDeviceWidth() {
                if (typeof(window.innerWidth) == 'number') {
                    //Non-IE
                    return window.innerWidth;
                } else if (document.documentElement && (document.documentElement.clientWidth || document
                        .documentElement.clientHeight)) {
                    //IE 6+ in 'standards compliant mode'
                    return document.documentElement.clientWidth;
                } else if (document.body && (document.body.clientWidth || document.body.clientHeight)) {
                    //IE 4 compatible
                    return document.body.clientWidth;
                }
                return 0;
            }
            initLinks();
            iniFontSizeButton();

            livewire.on('contentChanged', () => {
                //
                if (getDeviceWidth() <= 768) {
                    document.getElementById('content-wrapper').scrollIntoView({
                        behavior: 'smooth',
                        block: "start"
                    });
                }

            });

            livewire.on('pageChanged', () => {


                $(function() {
                    iniFontSizeButton();
                    initLinks();
                });
                // window.lightGallery.refresh();
                $('.clickable-star').click(function() {
                    // Select the current element and all elements before it
                    var elementsBeforeCurrent = $(this).prevAll().addBack();

                    // Add the desired class to all elements before the clicked element
                    elementsBeforeCurrent.addClass('check');

                    var elementsAfterCurrent = $(this).nextAll();
                    elementsAfterCurrent.removeClass('check');
                });
                //hover
                //     $(".clickable-star").hover(
                //         function() { //hover
                //             // Select the current element and all elements before it
                //             var elementsBeforeCurrent = $(this).prevAll().addBack();

                //             // Add the desired class to all elements before the clicked element
                //             elementsBeforeCurrent.addClass('check');
                //         },
                //         function() { //out
                //             // Select the current element and all elements before it
                //             var elementsBeforeCurrent = $(this).prevAll().addBack();
                //             // Add the desired class to all elements before the clicked element
                //             elementsBeforeCurrent.removeClass('check');
                //         }
                //     );
            });

            $('.clickable-star').click(function() {
                // Select the current element and all elements before it
                var elementsBeforeCurrent = $(this).prevAll().addBack();

                // Add the desired class to all elements before the clicked element
                elementsBeforeCurrent.addClass('check');

                var elementsAfterCurrent = $(this).nextAll();
                elementsAfterCurrent.removeClass('check');
            });

            //hover
            // $(".clickable-star").hover(
            //     function() { //hover
            //         // Select the current element and all elements before it
            //         var elementsBeforeCurrent = $(this).prevAll().addBack();

            //         // Add the desired class to all elements before the clicked element
            //         elementsBeforeCurrent.addClass('check');
            //     },
            //     function() { //out
            //         // Select the current element and all elements before it
            //         var elementsBeforeCurrent = $(this).prevAll().addBack();

            //         // Add the desired class to all elements before the clicked element
            //         elementsBeforeCurrent.removeClass('check');
            //     }
            // );
        });
    </script>
    <script>
        $(function() {
            $('[data-toggle="popover"]').popover();
        });


        document.addEventListener('DOMContentLoaded', function() {

            Livewire.on('contentChanged', () => {
                console.log('element f....');
                window.imgZoom();
            })

            window.lightGallery();
            livewire.on('refreshLightgallery', () => {
                window.imgZoom();
                window.lightGallery();

            });
            livewire.on('pageChanged', () => {
                $(function() {
                    $('[data-toggle="popover"]').popover();
                })
                // window.lightGallery.refresh();
            });
        })
    </script>
@endpush



@pushIf($this->Meta,'meta')
{!! get_seo_meta_tag($this->Meta) !!}
@endpushIf

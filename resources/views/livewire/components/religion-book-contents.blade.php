<div wire:key="{{ rand() }}">



    @php
        $platform_type = cache()->get('platform_type');
    @endphp

    <x-book-content.religion-titles />


    <!-- Nav pills -->
    @if ($title_id == 0)
        <div class="empty">
            @if ($book_cover)
                <img src="{{ url('uploads/' . $book_cover) }}" alt="">
            @else
                <img src="{{ url('images/not-found.svg') }}" alt="" srcset="">
            @endif
            <p>يمكنك تصفح المواضيع من خلال تصفح التصنيفات واختيار مسألة محددة
                من القائمة الموجودة على يمين الصفحة</p>
        </div>
    @else
        @if (!$this->Title)
            <div class="text-center error-404">
                <h1>404</h1>
                <p>عذراً .... العنوان الذي تبحث عنه غير موجود </p>
            </div>
        @else
            <div class="contents-container" id="contents-container">
                <div id='content-wrapper'>
                    @include('components.religion.text-content', [
                        'title_id' => $title_id,
                        'data' => ['text' => $this->Content?->text],
                    ])
                </div>
            </div>
        @endif
    @endif

</div>



</div>
@push('scripts')
    <script type="text/javascript">
        if (!String.prototype.replaceLast) {
            String.prototype.replaceLast = function(find, replace) {
                var index = this.lastIndexOf(find);

                if (index >= 0) {
                    return this.substring(0, index) + replace + this.substring(index + find.length);
                }

                return this.toString();
            };
        };

        $(document).ready(function() {
            function iniFontSizeButton() {
                var pattern = /font-size:\d*\d*.*\ /i;
                // Increase Font Size
                $(".increase").click(function() {
                    $('.content-text *').each(function() {
                        var element = $(this);
                        var currentSize = element.css('font-size');
                        var currentSize = parseFloat(currentSize) + 1;
                        var style = element.attr("style") ?? "";
                        var style = style + " ";
                        var style = style.replace(pattern, "");
                        var style = style.replaceLast(";", "");
                        var style = style + ";font-size:" + currentSize + "px !important";
                        element.attr("style", style);
                    });
                    return false;
                });

                // Decrease Font Size
                $(".decrease").click(function() {
                    $('.content-text *').each(function() {
                        var element = $(this);
                        var currentSize = element.css('font-size');
                        var currentSize = parseFloat(currentSize) - 1;
                        var style = element.attr("style") ?? "";
                        var style = style + " ";
                        var style = style.replace(pattern, "");
                        var style = style.replaceLast(";", "");
                        var style = style + ";font-size:" + currentSize + "px !important";
                        element.attr("style", style);
                    });
                    return false;
                });
            }

            function initLinks() {
                $('.content-text a').each(function() {
                    $(this).click(function(e) {
                        if (this.href.includes("{{ config('panels.panels.kholasah.domain').'/book/' }}")|| this.href.includes("{{ config('panels.panels.religions.domain').'/book/' }}") || this.href.includes("{{ config('panels.panels.sahihalsunnah.domain').'/book/' }}") || this.href.includes("{{ config('panels.panels.curriculums.domain').'/book/' }}")) {
                            e.preventDefault();
                            Livewire.emit('loadContent', this.href);
                            $('#show-content').modal('show');
                        }

                    });
                });
            }

            function getDeviceWidth() {
                if (typeof(window.innerWidth) == 'number') {
                    //Non-IE
                    return window.innerWidth;
                } else if (document.documentElement && (document.documentElement.clientWidth || document
                        .documentElement.clientHeight)) {
                    //IE 6+ in 'standards compliant mode'
                    return document.documentElement.clientWidth;
                } else if (document.body && (document.body.clientWidth || document.body.clientHeight)) {
                    //IE 4 compatible
                    return document.body.clientWidth;
                }
                return 0;
            }
            initLinks();
            iniFontSizeButton();

            livewire.on('contentChanged', () => {
                //
                if (getDeviceWidth() <= 768) {
                    document.getElementById('content-wrapper').scrollIntoView({
                        behavior: 'smooth',
                        block: "start"
                    });
                }

            });

            livewire.on('pageChanged', () => {


                $(function() {
                    iniFontSizeButton();
                    initLinks();
                });
                // window.lightGallery.refresh();
                $('.clickable-star').click(function() {
                    // Select the current element and all elements before it
                    var elementsBeforeCurrent = $(this).prevAll().addBack();

                    // Add the desired class to all elements before the clicked element
                    elementsBeforeCurrent.addClass('check');

                    var elementsAfterCurrent = $(this).nextAll();
                    elementsAfterCurrent.removeClass('check');
                });
                //hover
                //     $(".clickable-star").hover(
                //         function() { //hover
                //             // Select the current element and all elements before it
                //             var elementsBeforeCurrent = $(this).prevAll().addBack();

                //             // Add the desired class to all elements before the clicked element
                //             elementsBeforeCurrent.addClass('check');
                //         },
                //         function() { //out
                //             // Select the current element and all elements before it
                //             var elementsBeforeCurrent = $(this).prevAll().addBack();
                //             // Add the desired class to all elements before the clicked element
                //             elementsBeforeCurrent.removeClass('check');
                //         }
                //     );
            });

            $('.clickable-star').click(function() {
                // Select the current element and all elements before it
                var elementsBeforeCurrent = $(this).prevAll().addBack();

                // Add the desired class to all elements before the clicked element
                elementsBeforeCurrent.addClass('check');

                var elementsAfterCurrent = $(this).nextAll();
                elementsAfterCurrent.removeClass('check');
            });

            //hover
            // $(".clickable-star").hover(
            //     function() { //hover
            //         // Select the current element and all elements before it
            //         var elementsBeforeCurrent = $(this).prevAll().addBack();

            //         // Add the desired class to all elements before the clicked element
            //         elementsBeforeCurrent.addClass('check');
            //     },
            //     function() { //out
            //         // Select the current element and all elements before it
            //         var elementsBeforeCurrent = $(this).prevAll().addBack();

            //         // Add the desired class to all elements before the clicked element
            //         elementsBeforeCurrent.removeClass('check');
            //     }
            // );
        });
    </script>
    <script>
        $(function() {
            $('[data-toggle="popover"]').popover();
        });


        document.addEventListener('DOMContentLoaded', function() {

            Livewire.on('contentChanged', () => {
                console.log('element f....');
                window.imgZoom();
            })

            window.lightGallery();
            livewire.on('refreshLightgallery', () => {
                window.imgZoom();
                window.lightGallery();

            });
            livewire.on('pageChanged', () => {
                $(function() {
                    $('[data-toggle="popover"]').popover();
                })
                // window.lightGallery.refresh();
            });
        })
    </script>
@endpush

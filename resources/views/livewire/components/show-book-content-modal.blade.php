
<div>
    <!-- Button trigger modal -->


    @if ($closeModal)
        <script wire:init='closeModalFun'>
            $('#show-content').modal('show');
        </script>
    @endif




    <!-- Modal -->
    <div class="modal fade " id="show-content" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle"
        aria-hidden="true" wire:ignore.self>
        <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
            <div class="modal-content rounded-xl border border-0">
                <div class="modal-header d-flex  justify-content-between ">
                    <h5 class="modal-title" id="exampleModalLongTitle">{{ $title?->name ?? 'المحتوى' }}</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>

                <div class="modal-body">
                    <div wire:loading.delay.block style="height: 200px; position: relative; display:none">
                        <div class="overlay__inner">
                            <div class="overlay__content">
                                <span class="spinner"></span>
                                <p>جاري التحميل</p>
                            </div>
                        </div>
                    </div>
                    <div wire:loading.remove class="">
                        <div class="content-text-container">
@php
    $title_id = $this->content?->title_id;
@endphp
                        @if ($this->content?->view_type == null)
                            <div class="content-text">
                                {!! $content?->text !!}
                                
                            </div>
                        @else
                        @if ($this->content->view_type == 1)
                            @include('components.text-content', [
                                'title_id' => $title_id,
                                'data' => $this->content->text,
                            ])
                        @elseif($this->content->view_type == 2)
                            @include('components.video-content', [
                                'title_id' => $title_id,
                                'data' => $this->content->videos,
                            ])
                        @elseif($this->content->view_type == 3)
                            @include('components.qouts-content', [
                                'title_id' => $title_id,
                                'data' => $this->content->qoutations,
                            ])
                        @elseif($this->content->view_type == 4)
                            @include('components.references-content', [
                                'title_id' => $title_id,
                                'data' => $this->content->refrences,
                            ])
                        @elseif($this->content->view_type == 5)
                            @include('components.activities-content', [
                                'title_id' => $title_id,
                                'data' => $this->content->activities,
                            ])
                        @elseif($this->content->view_type == 6)
                            @include('components.calenders-content', [
                                'title_id' => $title_id,
                                'data' => $this->content->calenders,
                            ])
                        @elseif($this->content->view_type == 7)
                            @include('components.texts-content', [
                                'title_id' => $title_id,
                                'data' => $this->content->texts,
                            ])
                        @elseif ($this->content->view_type == 8)
                            @include('components.tafseer-content', [
                                'title_id' => $title_id,
                                'data' => $this->content->data,
                            ])
                        @elseif ($this->content->view_type == 9)
                            @include('components.tadabor-content', [
                                'title_id' => $title_id,
                                'data' => $this->content->data,
                            ])
                        @endif
                    @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
